

interface IAppInfo {
    baseApiURL: string
    // homeURL: string,
    campaignURL: string

    importTKBFileUrl: string,
    // subSytemId: number,
    versionDataGridStorageKey?: string,
    headerHeight: number
}

const appInfo: IAppInfo = {
    baseApiURL: process.env.REACT_APP_API_BASE_URL?.toString() || "",
    campaignURL: "http://localhost:3000/api",
    // baseApiURL: "../../api",
    // homeURL: process.env.REACT_APP_PORTAL_URL?.toString() || "", 
    importTKBFileUrl: "https://docs.google.com/spreadsheets/d/1PcwgHeAg96axlc2vAsXFMJyuyDSgk4Iu/edit?usp=sharing&ouid=111169258709356213204&rtpof=true&sd=true",
    // subSytemId: 11,
    versionDataGridStorageKey: "",
    headerHeight: 48
};
console.log({
    appInfo
});

export { appInfo }

