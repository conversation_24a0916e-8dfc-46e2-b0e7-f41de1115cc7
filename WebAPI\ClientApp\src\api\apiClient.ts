import jwt_decode from 'jwt-decode';
import { appInfo } from '../AppInfo';
import { IBaseResponeModel } from '../models/response/base-response';
import { axiosClient } from './axiosClient';
type jwt_decodeResult = {
    exp: any
}
let refreshTokenRequest: Promise<any> | null;
export const clearAccessToken = () => {
    localStorage.removeItem("access_token");
}

export const clearRefreshToken = () => {
    localStorage.removeItem("refresh_token");
}
export const saveAccessToken = (access_token: string) => {
    localStorage.setItem("access_token", access_token)
}

export const saveRefreshToken = (refresh_token: string) => {
    localStorage.setItem("refresh_token", refresh_token)
}

const checkIsTokenExpired = (): boolean => {
    // return true;
    try {
        const data: jwt_decodeResult = jwt_decode(localStorage.access_token)
        if (localStorage.access_token && data) {
            const exp = data.exp;
            if (parseInt(exp) > Math.floor(Date.now() / 1000)) {
                return false;
            }
        }
        return true;
    } catch (error) {
        return true;
    }
}

const refreshToken = () => {
    return new Promise(resolve => {
        setTimeout(async () => {
            try {
                // debugger
                const res: IBaseResponeModel = await axiosClient.post(`${localStorage.getItem("homeURL") ?? ""}/api/account/token/refresh`, {
                    access_token: localStorage.access_token,
                    refresh_token: localStorage.refresh_token
                });

                if (res.is_success) {
                    resolve(res.message)
                } else {
                    resolve("");
                }
            } catch (error) {
                // debugger
                localStorage.removeItem("access_token");
                window.location.reload();
            }

        }, 1000);
    });
};
const configIfTokenExpired = async () => {
    const isTokenExpired = checkIsTokenExpired();
    if (isTokenExpired) {
        refreshTokenRequest = refreshTokenRequest
            ? refreshTokenRequest
            : refreshToken();

        const newTokens = await refreshTokenRequest;
        refreshTokenRequest = null;
        if (newTokens == "") {
            return {
                is_success: false,
                message: "Token expired."
            }
        }
        const new_access_token = newTokens.split(' ')[0];
        const new_refresh_token = newTokens.split(' ')[1];
        localStorage.access_token = new_access_token;
        localStorage.refresh_token = new_refresh_token;

    }
}
export const objectToQueryString = (obj: any, parentKey: any): string => {
    return Object.keys(obj)
        .map(key => {
            const value = obj[key];
            const encodedKey = parentKey ? `${parentKey}.${encodeURIComponent(key)}` : encodeURIComponent(key);

            if (typeof value === 'object' && !Array.isArray(value) && value !== null) {
                return objectToQueryString(value, encodedKey);
            }

            return `${encodedKey}=${encodeURIComponent(value)}`;
        })
        .join('&');
}
export const formatQueryString = (obj: any): string => {
    if (obj) {
        return objectToQueryString(obj, null);
    }
    return "";
}

const apiClient = {
    get: async (path: string): Promise<IBaseResponeModel> => {
        const url = `${appInfo.baseApiURL}/${path}`
        try {
            await configIfTokenExpired();
            const config = {
                headers: {
                    language: localStorage.getItem("language"),
                    Authorization: `Bearer ${localStorage.access_token}`,
                }
            }
            const res = await axiosClient.get<any, IBaseResponeModel>(url, config);
            return res;
        } catch (error: any) {
            if (error.response.status == 401) {
                return {
                    status_code: parseInt(error.response.status),
                    is_success: false,
                    message: "Bạn không được phân quyền để thực hiện thao tác này. Vui lòng liên hệ Quản trị viên để được hỗ trợ."
                }
            } else {
                return {
                    status_code: error.response.data.status_code,
                    is_success: false,
                    message: error.response.data.message || "Có lỗi"
                };
            }

        }
    }
    ,
    post: async (path: string, data?: any, domain?: string): Promise<IBaseResponeModel> => {
        const url = `${domain ? domain : appInfo.baseApiURL}/${path}`

        try {
            await configIfTokenExpired();
            const config = {
                headers: {
                    Authorization: `Bearer ${localStorage.access_token}`,
                    language: localStorage.getItem("language"),
                }
            }
            const res = await axiosClient.post<any, IBaseResponeModel>(url, data, config);
            return res;
        } catch (error: any) {
            //console.log('object', error);
            if (error.response.status == 401) {
                return {
                    status_code: parseInt(error.response.status),
                    is_success: false,
                    message: "Bạn không được phân quyền để thực hiện thao tác này. Vui lòng liên hệ Quản trị viên để được hỗ trợ."
                }
            } else {
                return {
                    status_code: error.response.data.status_code,
                    is_success: false,
                    message: error.response.data.message || "Có lỗi"
                };
            }

        }
    },
    put: async (path: string, data?: any): Promise<IBaseResponeModel> => {
        const url = `${appInfo.baseApiURL}/${path}`
        try {
            await configIfTokenExpired();
            const config = {

                headers: {
                    Authorization: `Bearer ${localStorage.access_token}`,
                    language: localStorage.getItem("language"),
                }
            }
            const res = await axiosClient.put<any, IBaseResponeModel>(url, data, config);
            return res;
        } catch (error: any) {
            if (error.response.status == 401) {
                return {
                    status_code: parseInt(error.response.status),
                    is_success: false,
                    message: "Bạn không được phân quyền để thực hiện thao tác này. Vui lòng liên hệ Quản trị viên để được hỗ trợ."
                }
            } else {
                return {
                    status_code: error.response.data.status_code,
                    is_success: false,
                    message: error.response.data.message || "Có lỗi"
                };
            }

        }
    },
    delete: async (path: string): Promise<IBaseResponeModel> => {
        const url = `${appInfo.baseApiURL}/${path}`
        try {
            await configIfTokenExpired();
            const config = {
                headers: {
                    Authorization: `Bearer ${localStorage.access_token}`,
                    language: localStorage.getItem("language"),
                }
            }
            const res = await axiosClient.delete<any, IBaseResponeModel>(url, config);
            return res;
        } catch (error: any) {
            if (error.response.status == 401) {
                return {
                    status_code: parseInt(error.response.status),
                    is_success: false,
                    message: "Bạn không được phân quyền để thực hiện thao tác này. Vui lòng liên hệ Quản trị viên để được hỗ trợ."
                }
            } else {
                return {
                    status_code: error.response.data.status_code,
                    is_success: false,
                    message: error.response.data.message || "Có lỗi"
                };
            }

        }
    },
    upload: async (path: string, data?: any): Promise<IBaseResponeModel> => {
        const url = `${appInfo.baseApiURL}/${path}`
        try {
            await configIfTokenExpired();
            const config = {

                headers: {
                    Authorization: `Bearer ${localStorage.access_token}`, 'Content-Type': 'multipart/form-data',
                    language: localStorage.getItem("language"),
                }
            }
            const res = await axiosClient.post<any, IBaseResponeModel>(url, data, config);
            return res;
        } catch (error: any) {
            if (error.response.status == 401) {
                return {
                    status_code: parseInt(error.response.status),
                    is_success: false,
                    message: "Bạn không được phân quyền để thực hiện thao tác này. Vui lòng liên hệ Quản trị viên để được hỗ trợ."
                }
            } else {
                return {
                    status_code: error.response.data.status_code,
                    is_success: false,
                    message: error.response.data.message || "Có lỗi"
                };
            }

        }
    }
}
export { apiClient };
