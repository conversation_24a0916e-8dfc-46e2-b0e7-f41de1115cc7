import { accountApi } from "./accountApi";

import { boDoiCongAnApi } from "./boDoiCongAnApi";
import { categorySourceApi } from "./categorySourceApi";
import { chienDichApi } from "./chienDichApi";
import { conCbnvApi } from "./conCbnvApi";
import { crmHocSinhApi } from "./crmHocSinhApi";
import { crmSurveyApi } from "./crmSurveyApi";
import { crmSurveyFormApi } from "./crmSurveyFormApi";
import { crmSurveyFormQuestionApi } from "./crmSurveyFormQuestionApi";
import { dictionaryApi } from "./dictionaryApi";
import { emailApi } from "./emailApi";
import { emailConfigApi } from "./emailConfigApi";
import { emailTemplateApi } from "./emailTemplateApi";
import { foCaAnApi } from "./foCaAnApi";
import { foCaAnTruongApi } from "./foCaAnTruongApi";
import { foMonAnChiTietApi } from "./foMonAnChiTietApi";
import { formApi } from "./formApi";
import { formEditorApi } from "./formEditorApi";
import { formEditorTypeApi } from "./formEditorTypeApi";
import { formItemApi } from "./formItemApi";
import { formRegisterResultApi } from "./formRegisterResultApi";
import { formRegisterResultDetailApi } from "./formRegisterResultDetailApi";
import { foThucDonApi } from "./foThucDonApi";
import { hocSinhAceApi } from "./hocSinhAceApi";
import { hocSinhAceChiTietApi } from "./hocSinhAceChiTietApi";
import { hocSinhApi } from "./hocSinhApi";
import { loaiGiayToApi } from "./loaiGiayToApi";
import { loaiPhongApi } from './loaiPhongApi';
import { loaiSucKhoeApi } from "./loaiSucKhoeApi";
import { localizedReourceApi } from './localizedReourceApi';
import { lopApi } from "./lopApi";
import { nguonBietToiApi } from "./nguonBietToiApi";
import { nguonBietToiTypeApi } from "./nguonBietToiTypeApi";
import { notifyApi } from './notifyApi';
import { phuHuynhApi } from "./phuHuynhApi";
import { sucKhoeApi } from "./sucKhoeApi";
import { thongTinCbnvApi } from "./thongTinCbnvApi";
import { trangThaiHocSinhApi } from "./trangThaiHocSinhApi";
import { trangThaiPhuHuynhApi } from "./trangThaiPhuHuynhApi";
import { uploadApi } from './uploadApi';
export const apiWrapper = {
    account: accountApi,
    uploadFile: uploadApi,
    categorySource: categorySourceApi,

    dictionary: dictionaryApi,
    lop: lopApi,
    hocSinh: hocSinhApi,
    loaiPhong: loaiPhongApi,
    notify: notifyApi,
    localizedResource: localizedReourceApi,
    formWrapper: {
        form: formApi,
        formItem: formItemApi,
        formEditor: formEditorApi,
        formEditorType: formEditorTypeApi,
        formRegisterResult: formRegisterResultApi,
        formRegisterResultDetail: formRegisterResultDetailApi
    },
    trangThaiHocSinh: trangThaiHocSinhApi,
    trangThaiPhuHuynh: trangThaiPhuHuynhApi,
    loaiGiayTo: loaiGiayToApi,
    crmSurveyForm: {
        crmSurveyFormQuestion: crmSurveyFormQuestionApi,
        crmSurvey: crmSurveyApi,
        crmSurveyForm: crmSurveyFormApi,
        chienDich: chienDichApi
    },
    crmCategoryWrapper: {
        nguonBietToi: nguonBietToiApi,
        nguonBietToiType: nguonBietToiTypeApi
    },
    crmHocSinh: crmHocSinhApi,
    thucDonWrapper: {
        caAn: foCaAnApi,
        caAnTruong: foCaAnTruongApi,
        monAnChiTiet: foMonAnChiTietApi,
        thucDon: foThucDonApi
    },
    crmPhuHuynh: phuHuynhApi,
    sucKhoe: {
        sucKhoe: sucKhoeApi,
        loaiSucKhoe: loaiSucKhoeApi
    },
    hocSinhAce: hocSinhAceApi,
    hocSinhAceChiTiet: hocSinhAceChiTietApi,
    thongTinCbnv: thongTinCbnvApi,
    conCbnv: conCbnvApi,
    boDoiCongAn: boDoiCongAnApi,
    email: {
        email: emailApi,
        emailConfig: emailConfigApi,
        emailTemplate: emailTemplateApi
    }

}