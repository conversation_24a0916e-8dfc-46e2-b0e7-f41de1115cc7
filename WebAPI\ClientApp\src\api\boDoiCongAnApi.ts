import { IHocSinhAceChiTietLoadStart } from "../models/request/hoc-sinh/IHocSinhAceChiTietLoadStart";
import { IBoDoiCongAn } from "../models/response/hoc-sinh/IBoDoiCongAn";
import { apiClient } from "./apiClient";

export const BODOI_CONGAN_END_POINT = "bodoi-congan";

export const boDoiCongAnApi = {
    select_all: () => apiClient.get(BODOI_CONGAN_END_POINT),
    select_all_by_hocsinh: (data: IHocSinhAceChiTietLoadStart) => apiClient.get(`${BODOI_CONGAN_END_POINT}/hoc_sinh/${data.ts_hocsinh_id}/nam_hoc/${data.nam_hoc}`),
    detail: (id: number) => apiClient.get(`${BODOI_CONGAN_END_POINT}${id}`),
    insert: (payload: IBoDoiCongAn) => apiClient.post(BODOI_CONGAN_END_POINT, payload),
    update: (payload: IBoDoiCongAn) => apiClient.put(BODOI_CONGAN_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${BODOI_CONGAN_END_POINT}/${id}`),

}