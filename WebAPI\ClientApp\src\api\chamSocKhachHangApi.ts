import { IInsertChamSocKhachHangRequest } from "../models/request/cskh/IInsertChamSocKhachHangRequest";
import { ISelectChamSocKhachHangRequest } from "../models/request/cskh/ISelectChamSocKhachHangRequest ";
import { ts_chamsockhachhang } from "../models/response/cskh/ts_chamsockhachhang";
import { apiClient } from "./apiClient";

export const chamSocKhachHangApi = {
    select: (data: ISelectChamSocKhachHangRequest) => apiClient.post(`cskh/select`, data),
    detail: (id: number) => apiClient.get(`cskh/${id}`),
    update: (data: ts_chamsockhachhang) => apiClient.put(`cskh`, data),
    insert: (data: IInsertChamSocKhachHangRequest) => apiClient.post(`cskh`, data),
    selectAll: () => apiClient.get(`cskh`),
}