import { IDmChienDich } from "../models/response/crm-survey/IDmChienDich";
import { apiClient } from "./apiClient";


export const CHIEN_DICH_API_END_POINT = "chien-dich";

export const chienDichApi = {
    load: () => apiClient.get(CHIEN_DICH_API_END_POINT),
    load_by_coso: (dm_coso_id: number) => apiClient.get(`co-so/${dm_coso_id}/chien-dich`),
    load_truong_by_chiendich: (dm_chiendich_id: number) => apiClient.get(`chien-dich/${dm_chiendich_id}/truong`),
    detail: (id: number) => apiClient.get(`${CHIEN_DICH_API_END_POINT}/${id}`),
    insert: (payload: IDmChienDich) => apiClient.post(`${CHIEN_DICH_API_END_POINT}`, payload),
    update: (payload: IDmChienDich) => apiClient.put(`${CHIEN_DICH_API_END_POINT}`, payload),
    delete: (id: number) => apiClient.delete(`${CHIEN_DICH_API_END_POINT}/${id}`),
    lock: (id: number) => apiClient.put(`${CHIEN_DICH_API_END_POINT}/lock/${id}`),
    unlock: (id: number) => apiClient.put(`${CHIEN_DICH_API_END_POINT}/unlock/${id}`)
}