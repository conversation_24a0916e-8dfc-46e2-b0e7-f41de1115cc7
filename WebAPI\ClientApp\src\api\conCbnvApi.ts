import { IHocSinhAceChiTietLoadStart } from "../models/request/hoc-sinh/IHocSinhAceChiTietLoadStart";
import { IConCbnv } from "../models/response/hoc-sinh/IConCbnv";
import { apiClient } from "./apiClient";

export const CON_CBNV_END_POINT = "con-cbnv";

export const conCbnvApi = {
    select_all: () => apiClient.get(CON_CBNV_END_POINT),
    select_all_by_hocsinh: (data: IHocSinhAceChiTietLoadStart) => apiClient.get(`${CON_CBNV_END_POINT}/hoc_sinh/${data.ts_hocsinh_id}/nam_hoc/${data.nam_hoc}`),
    detail: (id: number) => apiClient.get(`${CON_CBNV_END_POINT}${id}`),
    insert: (payload: IConCbnv) => apiClient.post(CON_CBNV_END_POINT, payload),
    update: (payload: IConCbnv) => apiClient.put(CON_CBNV_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${CON_CBNV_END_POINT}/${id}`),

}