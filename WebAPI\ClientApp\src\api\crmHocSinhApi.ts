import { apiClient } from "./apiClient";
import { IHocSinhSelectAllRequest } from "../models/request/hoc-sinh/IHocSinhSelectAllRequest";
import { IHocSinhImportRequest } from "../models/request/hoc-sinh/IHocSinhImportRequest";
import { IHocSinhUpdateStatusRequest } from "../models/request/hoc-sinh/IHocSinhUpdateStatusRequest";
import { IHocSinhNgayNhapHocRequest } from "./../models/request/hoc-sinh/IHocSinhNgayNhapHocRequest";
import { IHocSinhSelectRequest } from "../models/request/hoc-sinh/IHocSinhSelectRequest";
import { HocSinhFormSubmitRequest } from "../models/request/hoc-sinh/HocSinhFormSubmitRequest";
import { IStudentBaseInformation } from "../models/request/acceptance-form/IStudentBaseInformation";
import { apiGuestClient } from "./apiGuestClient";
import { HocSinhDoiTrangThaiRequest } from "../models/request/hoc-sinh/HocSinhDoiTrangThaiRequest ";

// API Endpoints
export const HOC_SINH_API_END_POINT = "hoc-sinh";
export const crmHocSinhApi = {
  select: (request: IHocSinhSelectRequest) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/select`, request),
  update: (data: HocSinhFormSubmitRequest) =>
    apiClient.put(`${HOC_SINH_API_END_POINT}`, data),

  getById: (id: number, namHoc: string) =>
    apiClient.get(`${HOC_SINH_API_END_POINT}/${id}/nam-hoc/${namHoc}`),
  getHSCCById: (id: number, namHoc: string) =>
    apiClient.get(`hocsinh-chuyencap/${id}/nam-hoc/${namHoc}`),

  insert: (data: any) => apiClient.post(`${HOC_SINH_API_END_POINT}`, data),

  delete: (id: number) => apiClient.delete(`${HOC_SINH_API_END_POINT}/${id}`),

  // Status and approval operations
  approve: (data: any) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/approve`, data),
  updateAcceptanceForm: (request: IStudentBaseInformation) =>
    apiGuestClient.put(`${HOC_SINH_API_END_POINT}/acceptance-form`, request),

  updateStatus: (request: IHocSinhUpdateStatusRequest) =>
    apiClient.put(`${HOC_SINH_API_END_POINT}/status`, request),

  updateNgayNhapHoc: (request: IHocSinhNgayNhapHocRequest) =>
    apiClient.put(`${HOC_SINH_API_END_POINT}/ngay-nhap-hoc`, request),

  // Search and select operations
  search: (dm_truong_id: number, key: string) =>
    apiClient.get(
      `${HOC_SINH_API_END_POINT}/search/truong/${dm_truong_id}/key/${encodeURIComponent(
        key
      )}`
    ),

  selectByParent: (ts_phuhuynh_id: number) =>
    apiClient.get(`phu-huynh/${ts_phuhuynh_id}/hoc-sinh`),

  selectParents: (data: any) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/phu-huynh/select`, data),

  selectToExport: (data: any) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/select-export`, data),

  selectSuggestEmail: (ts_hocsinh_id: number) =>
    apiClient.get(
      `${HOC_SINH_API_END_POINT}/select_suggest_email/${ts_hocsinh_id}`
    ),

  selectByMaHs: (ma_hs: string, dm_coso_id: number = 1) =>
    apiClient.get(`${HOC_SINH_API_END_POINT}/ma/${ma_hs}/co-so/${dm_coso_id}`),

  selectPhuHuynh: (id: number, dm_quanhe_id: number) =>
    apiClient.get(`${HOC_SINH_API_END_POINT}/id/${id}/quanhe/${dm_quanhe_id}`),

  // Import operations
  import: ({ data, nam_hoc, dm_coso_id = 0 }: IHocSinhImportRequest) =>
    apiClient.put(
      `${HOC_SINH_API_END_POINT}/import/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}`,
      data
    ),

  validateImport: ({ data, nam_hoc, dm_coso_id = 0 }: IHocSinhImportRequest) =>
    apiClient.put(
      `${HOC_SINH_API_END_POINT}/validate-import/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}`,
      data
    ),

  importLenLop: ({ data, nam_hoc, dm_coso_id = 0 }: IHocSinhImportRequest) =>
    apiClient.put(
      `${HOC_SINH_API_END_POINT}/import-len-lop/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}`,
      data
    ),

  validateImportLenLop: ({
    data,
    nam_hoc,
    dm_coso_id = 0,
  }: IHocSinhImportRequest) =>
    apiClient.put(
      `${HOC_SINH_API_END_POINT}/validate-import-len-lop/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}`,
      data
    ),

  // Special operations
  selectNotInAceChiTiet: (data: any) =>
    apiClient.post(
      `${HOC_SINH_API_END_POINT}/select_not_in_ace_chi_tiet`,
      data
    ),

  selectAceById: (data: any) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/select_ace_by_id`, data),

  SelectTongHopAce: (request: IHocSinhSelectRequest) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/select-all-list-ace`, request),

  SelectTongHopCbnv: (request: IHocSinhSelectRequest) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/select-all-list-cbnv`, request),
  SelectTongHopBdca: (request: IHocSinhSelectRequest) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/select-all-list-bdca`, request),

  SelectTongHopGiayTo: (request: IHocSinhSelectRequest) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/select-all-list-giayto`, request),


  
  DoiTrangThai: (request: HocSinhDoiTrangThaiRequest ) =>
    apiClient.post(`${HOC_SINH_API_END_POINT}/doi-trang-thai`, request),
};
