import { ILop } from '../models/response/crm-lop/ILop';
import { apiClient } from './apiClient';

export const LOP_API_ENDPOINT = "lop";

export const crmLopApi = {
    // L<PERSON>y danh sách tất cả lớp
    selectAll: () => apiClient.get(`${LOP_API_ENDPOINT}`),

    // Lấy lớp theo năm học
    selectByNamHoc: (nam_hoc: string) =>
        apiClient.get(`${LOP_API_ENDPOINT}/nam-hoc/${nam_hoc}`),

    // // Lấy danh sách học sinh trong lớp
    // selectHocSinh: (request: LopSelectHocSinhRequest) =>
    //     apiClient.post(`${LOP_API_ENDPOINT}/hoc-sinh`, request),

    // // Lấy lớp theo năm học và cơ sở dưới dạng cây
    // selectByNamHocAndCoSo: (nam_hoc: string, dm_coso_id: number) =>
    //     apiClient.get(`${LOP_API_ENDPOINT}/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}/tree-view`),

    // // Lấy cây lớp học theo năm học
    // selectTreeViewAsync: (nam_hoc: string) =>
    //     apiClient.get(`${LOP_API_ENDPOINT}/${nam_hoc}/tree-view`),

    // // Lấy thông tin lớp theo ID
     selectById: (id: number) => apiClient.get(`${LOP_API_ENDPOINT}/${id}`),

    // // Thêm lớp mới
     insert: (model: ILop) => apiClient.post(`${LOP_API_ENDPOINT}`, model),

    // // Cập nhật lớp
     update: (model: ILop) => apiClient.put(`${LOP_API_ENDPOINT}`, model),

    // // Xóa lớp theo ID
    delete: (id: number) => apiClient.delete(`${LOP_API_ENDPOINT}/${id}`),

    // // Thay đổi lớp cho học sinh
    // changeClass: (request: LopChangeHocSinhClassRequest) =>
    //     apiClient.put(`${LOP_API_ENDPOINT}/hoc-sinh/update`, request),

    // // Xóa học sinh khỏi lớp
    // removeHocSinh: (request: LopRemoveStudentRequest) =>
    //     apiClient.put(`${LOP_API_ENDPOINT}/hoc-sinh/remove`, request),

    // // Import danh sách phân lớp từ file Excel
    // import: (request: LopImportPhanLopRequest) =>
    //     apiClient.put(`${LOP_API_ENDPOINT}/hoc-sinh/import`, request),
};
