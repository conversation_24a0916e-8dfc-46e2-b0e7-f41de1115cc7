import { ISurveyVm } from "../models/response/crm-survey/ISurveyVm";
import { apiClient } from "./apiClient";

export const crmSurveyApi = {
    selectAll: () => apiClient.get(`crm_survey`),
    selectDetail: (id: number) => apiClient.get(`crm_survey/${id}`),
    insert: (data: ISurveyVm) => apiClient.post(`crm_survey`, data),
    update: (data: ISurveyVm) => apiClient.put(`crm_survey`, data),
    delete: (id: number) => apiClient.delete(`crm_survey/${id}`),
    selectRespones: (dm_chiendich_id: number) => apiClient.get(`crm_survey/${dm_chiendich_id}/respones`),
    selectAnswers: (crm_survey_form_id: number) => apiClient.get(`crm_survey/${crm_survey_form_id}/answers`),
    sendNotify: (id: number) => apiClient.post(`crm_survey/${id}/notify`),
}