import { ICrmSurveyFormVm } from "../models/response/crm-survey/ICrmSurveyFormVm";
import { apiClient } from "./apiClient";
import { apiGuestClient } from "./apiGuestClient";

export const crmSurveyFormApi = {
    selectAll: () => apiClient.get(`crm_survey-form`),
    selectDetail: (id: number) => apiGuestClient.get(`crm_survey-form/${id}`),
    selectByChienDich: (dm_chiendich_id: number) => apiClient.get(`crm_survey-form/chien_dich/${dm_chiendich_id}`),
    saveChanges: (data: ICrmSurveyFormVm) => apiClient.post(`crm_survey-form`, data),
    delete: (id: number) => apiClient.delete(`crm_survey-form/${id}`),
}