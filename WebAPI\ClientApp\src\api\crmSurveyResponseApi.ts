import { ICrmSurveyResponseVm } from "../models/response/crm-survey/ICrmSurveyResponse";
import { apiClient } from "./apiClient";
import { apiGuestClient } from "./apiGuestClient";

export const crmSurveyResponseApi = {
    selectAll: () => apiClient.get(`crm_survey-respone`),
    selectDetail: (id: number) => apiGuestClient.get(`crm_survey-respone/${id}`),
    saveChanges: (data: ICrmSurveyResponseVm) => apiGuestClient.post(`crm_survey-respone`, data),
    delete: (id: number) => apiClient.delete(`crm_survey-respone/${id}`),
}