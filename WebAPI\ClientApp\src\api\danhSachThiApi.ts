import { AddOrUpdatePhongThiRequest } from "../models/request/danh-sach-thi/AddOrUpdatePhongThiRequest";
import { DanhSachThiSelectRequest } from "../models/request/danh-sach-thi/DanhSachThiSelectRequest";
import { UpdatePhongThiThiSinhRequest } from "../models/request/danh-sach-thi/UpdatePhongThiThiSinhRequest";
import { UpdateVangThiRequest } from "../models/request/danh-sach-thi/UpdateVangThiRequest";
import { apiClient } from "./apiClient";


const DANH_SACH_THI_API_END_POINT = "danh-sach-thi";

export const danhSachThiApi = {
  // Select students by exam room and exam subject
  selectThiSinh: (ex_phongthi_id: number, ex_kythi_monthi_id: number) =>
    apiClient.get(`${DANH_SACH_THI_API_END_POINT}/ky-mon-thi/${ex_kythi_monthi_id}/phong-thi/${ex_phongthi_id}/select`),

  // Select students with custom request
  selectThiSinhWithRequest: (request: DanhSachThiSelectRequest) =>
    apiClient.post(`${DANH_SACH_THI_API_END_POINT}/thi-sinh/select`, request),

  // Add or update exam room assignments
  addOrUpdatePhongThiThiSinh: (request: AddOrUpdatePhongThiRequest) =>
    apiClient.post(`${DANH_SACH_THI_API_END_POINT}/chia-phong-thi`, request),

  // Remove student from exam room
  removePhongThiThiSinh: (request: UpdatePhongThiThiSinhRequest) =>
    apiClient.post(`${DANH_SACH_THI_API_END_POINT}/bo-phong-thi-thi-sinh`, request),

  // Update absence status
  updateVangThi: (request: UpdateVangThiRequest) =>
    apiClient.post(`${DANH_SACH_THI_API_END_POINT}/cap-nhat-vang-thi`, request),
};