import { DiemThiThanhPhanSelectRequest } from "../models/request/diem-thi-thanh-phan/DiemThiThanhPhanSelectRequest";
import { UpdateDiemThiThanhPhanRequest } from "../models/request/diem-thi-thanh-phan/UpdateDiemThiThanhPhanRequest ";
import { DiemThiSelectRequest } from "../models/request/diem-thi/DiemThiSelectRequest";
import { UpdateDiemThiRequest } from "../models/request/diem-thi/UpdateDiemThiRequest";
import { apiClient } from "./apiClient";

export const DIEM_THI_API_END_POINT = "diem-thi";

export const diemThiApi = {
  // Get scores by exam room
  selectDiemThiByPhongThi: (ex_phongthi_id: number, ex_kythi_monthi_id: number) =>
    apiClient.get(
      `${DIEM_THI_API_END_POINT}/ky-mon-thi/${ex_kythi_monthi_id}/phong-thi/${ex_phongthi_id}/thi-sinh`
    ),

  // Get component scores by exam room
  selectDiemThiThanhPhanByPhongThi: (ex_phongthi_id: number, ex_kythi_monthi_id: number) =>
    apiClient.get(
      `${DIEM_THI_API_END_POINT}/ky-mon-thi/${ex_kythi_monthi_id}/phong-thi/${ex_phongthi_id}/diem-thi-theo-phong-thi`
    ),

  // Select scores
  select: (request: DiemThiSelectRequest) =>
    apiClient.post(`${DIEM_THI_API_END_POINT}/select`, request),

  // Select component scores
  selectDiemThiThanhPhan: (request: DiemThiThanhPhanSelectRequest) =>
    apiClient.post(`${DIEM_THI_API_END_POINT}/diem-thi-thanh-phan`, request),

  // Update student scores
  updateDiemThiThiSinh: (request: UpdateDiemThiRequest) =>
    apiClient.put(`${DIEM_THI_API_END_POINT}/cap-nhat-diem-thi`, request),

  // Update component scores for student
  updateDiemThiThanhPhanThiSinh: (request: UpdateDiemThiThanhPhanRequest) =>
    apiClient.put(`${DIEM_THI_API_END_POINT}/cap-nhat-diem-thi-thanh-phan`, request),
};