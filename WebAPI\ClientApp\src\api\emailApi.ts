import { EmailSelectedRequest } from "../models/request/email/EmailSelectedRequest";
import { IEmail } from "../models/response/email/IEmail";
import { IEmailAddOrEdit } from "../models/response/email/IEmailAddOrEdit";
import { IEmailUser } from "../models/response/email/IEmailUser";
import { apiClient } from "./apiClient";

export const emailApi = {
    getEditModel: (payload: number) => apiClient.get(`email/${payload}`),
    save: (payload: IEmailAddOrEdit) => apiClient.post(`email/save_async`, payload),
    getUsers: (payload: number) => apiClient.get(`email/${payload}/users`),
    saveUsers: (payload: IEmailUser[]) => apiClient.post(`email/users`, payload),
    send: (payload: number) => apiClient.put(`email/${payload}/send`),
    selectAll: () => apiClient.get(`email`),
    insert: (payload: IEmail) => apiClient.post(`email`, payload),
    update: (payload: IEmail) => apiClient.put(`email`, payload),
    delete: (id: number) => apiClient.delete(`email/${id}`),
    RejectEmail: (payload: EmailSelectedRequest) => apiClient.post(`email/tuchoi-pheduyet`, payload),
    ApproveEmail: (payload: EmailSelectedRequest) => apiClient.post(`email/pheduyet`, payload)
}