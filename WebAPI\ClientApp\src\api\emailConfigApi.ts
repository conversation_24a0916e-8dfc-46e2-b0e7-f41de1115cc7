import { IDmEmailConfig } from "../models/response/email/IDmEmailConfig";
import { apiClient } from "./apiClient";
export const EMAIL_CONFIG_END_POINT = "email-config";

export const emailConfigApi = {
    selectAll: () => apiClient.get(EMAIL_CONFIG_END_POINT),
    detail: (id: number) => apiClient.get(`${EMAIL_CONFIG_END_POINT}${id}`),
    insert: (payload: IDmEmailConfig) => apiClient.post(EMAIL_CONFIG_END_POINT, payload),
    update: (payload: IDmEmailConfig) => apiClient.put(EMAIL_CONFIG_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${EMAIL_CONFIG_END_POINT}/${id}`),

}