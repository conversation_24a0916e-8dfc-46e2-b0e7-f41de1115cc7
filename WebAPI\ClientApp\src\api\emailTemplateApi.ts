import { IEmailTemplate } from "../models/response/email/IEmailTemplate";
import { apiClient } from "./apiClient";

export const emailTemplateApi = {
    selectAll: () => apiClient.get(`email-template`),
    insert: (payload: IEmailTemplate) => apiClient.post(`email-template`, payload),
    update: (payload: IEmailTemplate) => apiClient.put(`email-template`, payload),
    delete: (id: number) => apiClient.delete(`email-template/${id}`),
}