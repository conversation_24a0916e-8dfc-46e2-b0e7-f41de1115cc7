import { ISendEmailBaoPhiRequest } from "../models/request/hoc-sinh/ISendEmailBaoPhiRequest";
import { apiClient } from "./apiClient";
export const SF_TINHBAOPHI_INSERT_API = "finance-helper/sf_tinhbaophi_insert";
export const SF_TINHBAOPHI_SELECT_API = "finance-helper/sf_tinhbaophi_selectbyhocsinh";
export const SF_TINHBAOPHI_SELECTBYID_API = "finance-helper/sf_tinhbaophi_selectbyid";
export const SF_TINHBAOPHI_DELETE_API = "finance-helper/sf_tinhbaophi_delete";
export const SF_TINHBAOPHI_REMOVE_KHOANNOP_API = "finance-helper/sf_tinhbaophi_remove_khoannop";
export const SF_TINHBAOPHI_SENDMAIL_API = "finance-helper/sf_tinhbaophi_send_email";


export const financeHelperApi = {
  
    sf_tinhbaophi_insert: (data:any) => {
        return apiClient.post(`${SF_TINHBAOPHI_INSERT_API}`, data)
    },
    sf_tinhbaophi_selectbyhocsinh: (data:any) => {
        return apiClient.post(`${SF_TINHBAOPHI_SELECT_API}`, data)
    },
    sf_tinhbaophi_selectbyid: (data:any) => {
        return apiClient.post(`${SF_TINHBAOPHI_SELECTBYID_API}`, data)
    },
    sf_tinhbaophi_delete: (data:any) => {
        return apiClient.post(`${SF_TINHBAOPHI_DELETE_API}`, data)
    },
    sf_tinhbaophi_remove_khoannop: (data:any) => {
        return apiClient.post(`${SF_TINHBAOPHI_REMOVE_KHOANNOP_API}`, data)
    },
    sf_tinhbaophi_send_baophi: (data:ISendEmailBaoPhiRequest) => {
        return apiClient.post(`${SF_TINHBAOPHI_SENDMAIL_API}`, data)
    },
}