import { IFoCaAn } from "../models/response/thuc-don/IFoCaAn";
import { apiClient } from "./apiClient";
export const FO_CAAN_API_END_POINT = "food/ca-an";

export const foCaAnApi = {
    select_all: () => apiClient.get(FO_CAAN_API_END_POINT),
    detail: (id: number) => apiClient.get(`${FO_CAAN_API_END_POINT}/${id}`),
    insert: (payload: IFoCaAn) => apiClient.post(`${FO_CAAN_API_END_POINT}`, payload),
    update: (payload: IFoCaAn) => apiClient.put(`${FO_CAAN_API_END_POINT}`, payload),
    delete: (id: number) => apiClient.delete(`${FO_CAAN_API_END_POINT}/${id}`),
}