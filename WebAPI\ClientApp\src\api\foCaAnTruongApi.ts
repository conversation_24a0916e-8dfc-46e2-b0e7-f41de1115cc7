import { IFoCaAnTruongRequest } from "../models/request/thuc-don/IFoCaAnTruongRequest";
import { IFoCaAnTruong } from "../models/response/thuc-don/IFoCaAnTruong";
import { apiClient } from "./apiClient";
export const FO_CAAN_TRUONG_API_END_POINT = "food/ca-an-truong";
export const foCaAnTruongApi = {
    select_all: () => apiClient.get(FO_CAAN_TRUONG_API_END_POINT),
    select_by_coso: (dm_coso_id: number) => apiClient.get(`co-so/${dm_coso_id}/food/ca-an-truong`),
    select_by_caan: (payload: IFoCaAnTruongRequest) => apiClient.post(`${FO_CAAN_TRUONG_API_END_POINT}/select-ca-an`, payload),
    detail: (id: number) => apiClient.get(`${FO_CAAN_TRUONG_API_END_POINT}/${id}`),
    insert: (payload: IFoCaAnTruong) => apiClient.post(`${FO_CAAN_TRUONG_API_END_POINT}`, payload),
    update: (payload: IFoCaAnTruong) => apiClient.put(`${FO_CAAN_TRUONG_API_END_POINT}`, payload),
    delete: (id: number) => apiClient.delete(`${FO_CAAN_TRUONG_API_END_POINT}/${id}`),
}