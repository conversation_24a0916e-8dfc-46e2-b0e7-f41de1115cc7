import { IFoMonAnChiTiet } from "../models/response/thuc-don/IFoMonAnChiTiet";
import { apiClient } from "./apiClient";
export const FO_MONAN_CHITIET_API_END_POINT = "food/monan-chitiet";
export const foMonAnChiTietApi = {
    select_all: () => apiClient.get(FO_MONAN_CHITIET_API_END_POINT),
    select_by_coso: (dm_coso_id: number) => apiClient.get(`co-so/${dm_coso_id}/food/monan-chitiet`),
    detail: (id: number) => apiClient.get(`${FO_MONAN_CHITIET_API_END_POINT}/${id}`),
    insert: (payload: IFoMonAnChiTiet) => apiClient.post(`${FO_MONAN_CHITIET_API_END_POINT}`, payload),
    update: (payload: IFoMonAnChiTiet) => apiClient.put(`${FO_MONAN_CHITIET_API_END_POINT}`, payload),
    delete: (id: number) => apiClient.delete(`${FO_MONAN_CHITIET_API_END_POINT}/${id}`),
    import: (data: any) => apiClient.post(`${FO_MONAN_CHITIET_API_END_POINT}/import`, data),
    validate_import: (data: any) => apiClient.post(`${FO_MONAN_CHITIET_API_END_POINT}/validate-import`, data),
}