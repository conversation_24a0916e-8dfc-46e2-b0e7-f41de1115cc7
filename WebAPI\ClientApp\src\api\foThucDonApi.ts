import { IFoThucDonInsertItemRequest } from "../models/request/thuc-don/IFoThucDonInsertItemRequest";
import { IFoThucDonItemRequest } from "../models/request/thuc-don/IFoThucDonItemRequest";
import { IReadUploadedExcelFileRequest } from "../models/request/upload-file/IReadUploadedExcelFileRequest";
import { IFoThucDon } from "../models/response/thuc-don/IFoThucDon";
import { apiClient } from "./apiClient";
export const FO_THUCDON_API_END_POINT = "food/thucdon";
export const foThucDonApi = {
    select_all: () => apiClient.get(FO_THUCDON_API_END_POINT),
    select_by_coso: (dm_coso_id: number) => apiClient.get(`co-so/${dm_coso_id}/food/ca-an-truong`),
    select_detail_daybyday: (data: IFoThucDonItemRequest) => apiClient.post(`${FO_THUCDON_API_END_POINT}/theongay`, data),
    insert_multiple: (data: IFoThucDonInsertItemRequest) => apiClient.post(`${FO_THUCDON_API_END_POINT}/insert-multiple`, data),
    detail: (id: number) => apiClient.get(`${FO_THUCDON_API_END_POINT}/${id}`),
    insert: (payload: IFoThucDon) => apiClient.post(`${FO_THUCDON_API_END_POINT}`, payload),
    update: (payload: IFoThucDon) => apiClient.put(`${FO_THUCDON_API_END_POINT}`, payload),
    delete: (id: number) => apiClient.delete(`${FO_THUCDON_API_END_POINT}/${id}`),
    import: (data: IReadUploadedExcelFileRequest, dm_truong_id: number) => apiClient.post(`${FO_THUCDON_API_END_POINT}/import/truong/${dm_truong_id}`, data),
    validate_import: (data: IReadUploadedExcelFileRequest, dm_truong_id: number) => apiClient.post(`${FO_THUCDON_API_END_POINT}/validate-import/truong/${dm_truong_id}`, data),
}