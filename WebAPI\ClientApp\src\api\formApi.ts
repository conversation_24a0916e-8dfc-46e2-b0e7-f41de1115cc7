import { IForm } from "../models/response/form/IForm";
import { apiClient } from "./apiClient";

export const formApi = {
    load: () => apiClient.get(`form`),
    load_by_coso: (dm_coso_id: number, nam_hoc: string) => apiClient.get(`form/coso/${dm_coso_id}/${nam_hoc}`),
    detail: (id: number) => apiClient.get(`form/${id}`),
    insert: (payload: IForm) => apiClient.post(`form`, payload),
    update: (payload: IForm) => apiClient.put(`form`, payload),
    delete: (id: number) => apiClient.delete(`form/${id}`),
    lock: (id: number) => apiClient.put(`form/lock/${id}`),
    unlock: (id: number) => apiClient.put(`form/unlock/${id}`)
}