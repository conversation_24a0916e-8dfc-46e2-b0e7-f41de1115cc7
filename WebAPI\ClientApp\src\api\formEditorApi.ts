import { IFormEditor,IFormEditorVm } from "../models/response/form/IFormEditor";
import { apiClient } from "./apiClient";

export const formEditorApi = {
    load: () => apiClient.get(`form-editor`),
    load_by_coso: (dm_coso_id: number) => apiClient.get(`form-editor/coso/${dm_coso_id}`),
    load_by_truong: (dm_truong_id: number) => apiClient.get(`form-editor/dm_truong_id/${dm_truong_id}`),
    load_by_datasource: (id:number, dm_coso_id:number, parent_id:number) => apiClient.get(`form-editor/datasource/${id}/coso/${dm_coso_id}/parentid/${parent_id}`),
    detail: (id: number) => apiClient.get(`form-editor/${id}`),
    insert: (payload: IFormEditor) => apiClient.post(`form-editor`, payload),
    update: (payload: IFormEditor) => apiClient.put(`form-editor`, payload),
    delete: (id: number) => apiClient.delete(`form-editor/${id}`),
}