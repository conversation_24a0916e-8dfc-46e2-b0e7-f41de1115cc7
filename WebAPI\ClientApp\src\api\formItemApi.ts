import { IFormItem } from "../models/response/form/IFormItem";
import { apiClient } from "./apiClient";

export const formItemApi = {
    load: () => apiClient.get(`form-item`),
    select: (ts_form_id: number) => apiClient.get(`form-item/${ts_form_id}`),
    detail: (id: number) => apiClient.get(`form-item/${id}`),
    insert: (payload: IFormItem) => apiClient.post(`form-item`, payload),
    inserts: (payload: IFormItem[]) => apiClient.post(`form-item/inserts`, payload),
    update: (payload: IFormItem) => apiClient.put(`form-item`, payload),
    delete: (id: number) => apiClient.delete(`form-item/${id}`),
    updates: (payload: IFormItem[]) => apiClient.put(`form-item/update-multiple`, payload)
}