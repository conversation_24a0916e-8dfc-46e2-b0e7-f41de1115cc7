import { IFormBaseItemRequest } from "../models/request/form/IFormBaseItemRequest";
import { IFormRegisterAttendanceRequest } from "../models/request/form/IFormRegisterAttendanceRequest";
import { IFormRegisterSendInviteRequest } from "../models/request/form/IFormRegisterSendInviteRequest";
import { IFormResultInsertRequest } from "../models/request/form/IFormResultInsertRequest";
import { IFormResultRequest } from "../models/request/form/IFormResultRequest";
import { IReadUploadedExcelFileRequest } from "../models/request/upload-file/IReadUploadedExcelFileRequest";
import { IFormRegisterResult } from "../models/response/form/IFormRegisterResult";
import { apiClient } from "./apiClient";
import { apiGuestClient } from "./apiGuestClient";

export const FORM_REGISTER_API_END_POINT = "form-result";
export const FORM_REGISTER_DETAIL_END_POINT = "form-result-detail";

export const formRegisterResultApi = {
    load: () => apiClient.get(FORM_REGISTER_API_END_POINT),
    select: (payload:IFormBaseItemRequest) => apiClient.get(`${FORM_REGISTER_API_END_POINT}/co-so/${payload.dm_coso_id}/chien-dich/${payload.dm_chiendich_id}/nam-hoc/${payload.nam_hoc}`),
    detail: (id: number) => apiClient.get(`${FORM_REGISTER_API_END_POINT}/${id}`),
    insert: (payload: IFormRegisterResult) => apiGuestClient.post(FORM_REGISTER_API_END_POINT, payload),
    update: (payload: IFormRegisterResult) => apiClient.put(FORM_REGISTER_API_END_POINT, payload),
    update_result: (payload: IFormResultRequest) => apiClient.put(`${FORM_REGISTER_API_END_POINT}/update-result`, payload),
    update_hocsinh: (payload: IFormResultRequest) => apiClient.put(`${FORM_REGISTER_API_END_POINT}/update-hocsinh`, payload),
    update_invite: (payload: IFormResultRequest) => apiClient.put(`${FORM_REGISTER_API_END_POINT}/update-invite`, payload),
    update_status: (payload: IFormResultRequest) => apiClient.put(`${FORM_REGISTER_API_END_POINT}/update-status`, payload),
    attendance: (payload: IFormRegisterAttendanceRequest) => apiGuestClient.post(`${FORM_REGISTER_API_END_POINT}/attendance`, payload),
    validate_import: (data: IReadUploadedExcelFileRequest, nam_hoc: string, dm_coso_id: number) => apiClient.put(`${FORM_REGISTER_API_END_POINT}/validate-import/nam-hoc/${nam_hoc}/co-so/${(dm_coso_id ?? 0)}`, data),
    import: (data: IReadUploadedExcelFileRequest, nam_hoc: string, dm_coso_id: number) => apiClient.put(`${FORM_REGISTER_API_END_POINT}/import/nam-hoc/${nam_hoc}/co-so/${(dm_coso_id ?? 0)}`, data),
    delete: (id: number) => apiClient.delete(`${FORM_REGISTER_API_END_POINT}/${id}`),
    send_email: (payload: IFormResultInsertRequest) => apiGuestClient.post(`${FORM_REGISTER_API_END_POINT}/send-email`, payload),
    send_email_invite: (payload: IFormRegisterSendInviteRequest) => apiClient.post(`${FORM_REGISTER_API_END_POINT}/send-invite`, payload),
    send_email_moi_ghi_danh:  (payload: IFormRegisterSendInviteRequest) => apiClient.post(`${FORM_REGISTER_API_END_POINT}/send-moi-ghi-danh`, payload),

    // ReportLead: (dm_coso_id, tu_van_vien_id, from_date, to_date) =>
    //     apiHelper.post(`${FORM_REPORT_END_POINT}`, {
    //         dm_coso_id,
    //         tu_van_vien_id,
    //         from_date,
    //         to_date
    //     }),
}