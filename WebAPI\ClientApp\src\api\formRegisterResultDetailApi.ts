import { IFormResultDetailListRequest } from "../models/request/form/IFormResultDetailListRequest";
import { IFormRegisterResultDetail } from "../models/response/form/IFormRegisterResultDetail";
import { apiClient } from "./apiClient";
import { apiGuestClient } from "./apiGuestClient";

export const FORM_REGISTER_DETAIL_END_POINT = "form-result-detail";

export const formRegisterResultDetailApi = {
    load: () => apiClient.get(FORM_REGISTER_DETAIL_END_POINT),
    select: (dm_coso_id: number, dm_chiendich_id: number, nam_hoc: string) => apiClient.get(`${FORM_REGISTER_DETAIL_END_POINT}/co-so/${dm_coso_id}/chien-dich/${dm_chiendich_id}/nam-hoc/${nam_hoc}`),
    insert: (payload: IFormRegisterResultDetail) => apiGuestClient.post(FORM_REGISTER_DETAIL_END_POINT, payload),
    update: (payload: IFormRegisterResultDetail) => apiClient.put(FORM_REGISTER_DETAIL_END_POINT, payload),
    detail_list_by_result: (payload: IFormResultDetailListRequest) => apiClient.post(`${FORM_REGISTER_DETAIL_END_POINT}/results`, payload),
    detail_list: (dm_coso_id: number, dm_chiendich_id: number) => apiClient.get(`${FORM_REGISTER_DETAIL_END_POINT}/co-so/${dm_coso_id ?? 0}/chien-dich/${dm_chiendich_id ?? 0}`),
    detail_list_by_phone: (dm_coso_id: number, dm_chiendich_id: number, phone_number: string) => apiGuestClient.get(`${FORM_REGISTER_DETAIL_END_POINT}/co-so/${dm_coso_id ?? 0}/chien-dich/${dm_chiendich_id ?? 0}/phone/${phone_number}`),
    detail_information: (dm_coso_id: number, nam_hoc: string) => apiClient.get(`${FORM_REGISTER_DETAIL_END_POINT}/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}`),
}