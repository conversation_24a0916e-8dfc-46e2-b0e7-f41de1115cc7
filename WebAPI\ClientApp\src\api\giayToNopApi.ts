import { apiClient } from './apiClient';

export const GIAY_TO_NOP_API_END_POINT = "giay-to-nop";

export const giayToNopApi = {
    // Lấy giấy tờ nộp theo học sinh
    SelectByHocSinh: (ts_hocsinh_id: string | number) => 
        apiClient.get(`${GIAY_TO_NOP_API_END_POINT}/hoc-sinh/${ts_hocsinh_id}`),

    // Upload giấy tờ
    Upload: ({ 
        formFiles, 
        ts_hocsinh_id, 
        dm_loaigiayto_id 
    }: {
        formFiles: FormData,
        ts_hocsinh_id: string | number,
        dm_loaigiayto_id: string | number
    }) => 
        apiClient.post(
            `${GIAY_TO_NOP_API_END_POINT}/upload/hoc-sinh/${ts_hocsinh_id}/giay-to/${dm_loaigiayto_id}`, 
            formFiles
        ),

    // <PERSON><PERSON>a chi tiết giấy tờ nộp
    DeleteChiTiet: (id: string | number) => 
        apiClient.delete(`${GIAY_TO_NOP_API_END_POINT}/chi-tiet/${id}`),

    // Thêm nhiều giấy tờ nộp
    Inserts: (data: any) => 
        apiClient.post(`${GIAY_TO_NOP_API_END_POINT}/insert-multiple`, data),
};