import { IHe } from '../models/response/he/IHe';
import { apiClient } from './apiClient';

export const HE_API_END_POINT = "he";

export const heApi = {
    selectAll: () => apiClient.get(`${HE_API_END_POINT}`),
    detail: (id: number) => apiClient.get(`${HE_API_END_POINT}/${id}`),
    delete: (id: number) => apiClient.delete(`${HE_API_END_POINT}/${id}`),

    insert: (data: IHe) => apiClient.post(`${HE_API_END_POINT}`, data),
    update: (data: IHe) => apiClient.put(`${HE_API_END_POINT}`, data),
};
