import { IHinhThucMienGiamRequest } from "../models/request/hinh-thuc-mien-giam/IHinhThucMienGiamRequest";
import { apiClient } from "./apiClient";

export const HINH_THUC_MIEN_GIAM_API_END_POINT = "hinhth<PERSON>miengiam";

export const hinhThucMienGiamApi = {
  selectAllByNamHoc: (
    nam_hoc: string,
    ts_hocsinh_id: number,
    dm_coso_id: number
  ) =>
    apiClient.get(
      `${HINH_THUC_MIEN_GIAM_API_END_POINT}/select_all_by_namhoc/${nam_hoc}/${ts_hocsinh_id}/${dm_coso_id}`
    ),
  selectAllByNamHocCoso: (
    nam_hoc: string,
    dm_coso_id: number
  ) =>
    apiClient.get(
      `${HINH_THUC_MIEN_GIAM_API_END_POINT}/nam_hoc/${nam_hoc}/co_so/${dm_coso_id}`
    ),
  selectByHocSinh: (nam_hoc: string, ts_hocsinh_id: number) =>
    apiClient.get(
      `${HINH_THUC_MIEN_GIAM_API_END_POINT}/select_by_hocsinh/${nam_hoc}/${ts_hocsinh_id}`
    ),

  insertDeXuatMienGiam: (request: IHinhThucMienGiamRequest) =>
    apiClient.post(
      `${HINH_THUC_MIEN_GIAM_API_END_POINT}/insert_de_xuat_mien_giam`,
      request
    ),
  deleteDeXuatMienGiam: (request: IHinhThucMienGiamRequest) =>
    apiClient.post(
      `${HINH_THUC_MIEN_GIAM_API_END_POINT}/delete_de_xuat_mien_giam`,
      request
    ),
};
