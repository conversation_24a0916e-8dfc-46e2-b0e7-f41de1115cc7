import { IHocSinhAce } from "../models/response/hoc-sinh/IHocSinhAce";
import { apiClient } from "./apiClient";
import { apiGuestClient } from "./apiGuestClient";

export const HOC_SINH_ACE_END_POINT = "hoc-sinh-ace";

export const hocSinhAceApi = {
    select_all: () => apiGuestClient.get(HOC_SINH_ACE_END_POINT),
    detail: (id: number) => apiClient.get(`${HOC_SINH_ACE_END_POINT}${id}`),
    insert: (payload: IHocSinhAce) => apiClient.post(HOC_SINH_ACE_END_POINT, payload),
    update: (payload: IHocSinhAce) => apiClient.put(HOC_SINH_ACE_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${HOC_SINH_ACE_END_POINT}/${id}`),

}