import { IHocSinhAceChiTietLoadStart } from "../models/request/hoc-sinh/IHocSinhAceChiTietLoadStart";
import { IHocSinhAceChitiet } from "../models/response/hoc-sinh/IHocSinhAceChitiet";
import { apiClient } from "./apiClient";

export const HOC_SINH_ACE_CHI_TIET_END_POINT = "hoc-sinh-ace-chi-tiet";

export const hocSinhAceChiTietApi = {
    select_all: () => apiClient.get(HOC_SINH_ACE_CHI_TIET_END_POINT),
    select_all_by_hocsinh: (data: IHocSinhAceChiTietLoadStart) => apiClient.get(`${HOC_SINH_ACE_CHI_TIET_END_POINT}/hoc_sinh/${data.ts_hocsinh_id}/nam_hoc/${data.nam_hoc}`),
    detail: (id: number) => apiClient.get(`${HOC_SINH_ACE_CHI_TIET_END_POINT}${id}`),
    insert: (payload: IHocSinhAceChitiet) => apiClient.post(HOC_SINH_ACE_CHI_TIET_END_POINT, payload),
    update: (payload: IHocSinhAceChitiet) => apiClient.put(HOC_SINH_ACE_CHI_TIET_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${HOC_SINH_ACE_CHI_TIET_END_POINT}/${id}`),

    deleteNew: (id: number, nam_hoc:string) => apiClient.delete(`${HOC_SINH_ACE_CHI_TIET_END_POINT}/${id}/${nam_hoc}`),

}