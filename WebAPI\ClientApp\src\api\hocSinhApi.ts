import { IHocSinhSelectRequest } from "../models/request/hoc-sinh/IHocSinhSelectRequest";
import { IReadUploadedExcelFileRequest } from "../models/request/upload-file/IReadUploadedExcelFileRequest";
import { apiClient } from "./apiClient";
export const TS_HOCSINH_SELECT_PARENT_EMAIL = "hoc-sinh/select_suggest_email"

export const hocSinhApi = {
    Select: (request: IHocSinhSelectRequest) => apiClient.post('hoc-sinh/select', request),
    SelectViewShort: (request: IHocSinhSelectRequest) => apiClient.post('hoc-sinh/select_view_short', request),
    SelectViewShortNoiTru: (request: IHocSinhSelectRequest) => apiClient.post('hoc-sinh/select_view_short_noitru', request),   

    SelectById: (id: number) => apiClient.get(`hoc-sinh/${id}`),
    Search: (request: any) => apiClient.get(`hoc-sinh/search/truong/${request.dm_truong_id}/key/${request.key}`),
    UpdateAdvisor: (request: any) => apiClient.post(`hoc-sinh/udpate-advisor`, request),
    SelectByParent: (ts_phuhuynh_id: number) => apiClient.get(`phu-huynh/${ts_phuhuynh_id}/hoc-sinh`),
    ValidateImportHocSinh(request: IReadUploadedExcelFileRequest, nam_hoc: string, dm_coso_id: number) {
        return apiClient.put(`hoc-sinh/validate-import/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}`, request)
    },
    ImportHocSinh(request: IReadUploadedExcelFileRequest, nam_hoc: string, dm_coso_id: number) {
        return apiClient.put(`hoc-sinh/import/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}`, request)
    },
    selectParentEmail: (ts_hocsinh_id: number) => apiClient.get(`${TS_HOCSINH_SELECT_PARENT_EMAIL}/${ts_hocsinh_id}`),
    uploadAvatar: (file: File, dm_coso_id: number, ma_hs: string) => {
        const formData = new FormData();
        formData.append('files', file);
        return apiClient.post(`upload/avatar/${dm_coso_id}/${ma_hs}`, formData);
    }
}

