import { IHocSinhSelectRequest } from '../models/request/hoc-sinh/IHocSinhSelectRequest';
import { IFaceId } from '../models/response/faceid/IFaceId';
import { apiClient } from './apiClient';

export const HOC_SINH_FACE_ID_API_END_POINT = "hoc-sinh-faceid";

export const hocSinhFaceIdApi = {
    selectByHocSinhId: (ts_hocsinh_id: number) => apiClient.get(`${HOC_SINH_FACE_ID_API_END_POINT}/hocsinh/${ts_hocsinh_id}`),
    selectAll: () => apiClient.get(`${HOC_SINH_FACE_ID_API_END_POINT}`),
    detail: (id: number) => apiClient.get(`${HOC_SINH_FACE_ID_API_END_POINT}/${id}`),
    delete: (id: number) => apiClient.delete(`${HOC_SINH_FACE_ID_API_END_POINT}/${id}`),

    insert: (data: IFaceId) => apiClient.post(`${HOC_SINH_FACE_ID_API_END_POINT}`, data),
    update: (data: IFaceId) => apiClient.put(`${HOC_SINH_FACE_ID_API_END_POINT}`, data),

      SelectTongHopFaceId: (request: IHocSinhSelectRequest) =>
        apiClient.post(`${HOC_SINH_FACE_ID_API_END_POINT}/select-all-list`, request),

      uploadFaceId: (file: File, nam_hoc: string, ts_hocsinh_id: number) => {
        const formData = new FormData();
        formData.append('files', file);
        return apiClient.post(`upload/faceid/${nam_hoc}/${ts_hocsinh_id}`, formData);
    }
};
