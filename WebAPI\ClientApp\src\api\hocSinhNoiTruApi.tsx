import { ts_hocsinh_noitru } from "../models/request/noi-tru/ts_hocsinh_noitru";
import { apiClient } from "./apiClient";

const HOC_SINH_NOI_TRU_API_END_POINT = "hs-noitru";
const HOC_SINH_NOI_TRU_STATUS_API_END_POINT = "hs-noitru-status";


export const hocSinhNoiTruApi = {
  selectAll: () => 
    apiClient.get(`${HOC_SINH_NOI_TRU_API_END_POINT}/select_all`),

  detail: (id: number) => 
    apiClient.get(`${HOC_SINH_NOI_TRU_API_END_POINT}/${id}`),

  insert: (data: ts_hocsinh_noitru) => 
    apiClient.post(`${HOC_SINH_NOI_TRU_API_END_POINT}/insert`, data),

  update: (data: ts_hocsinh_noitru) => 
    apiClient.put(`${HOC_SINH_NOI_TRU_API_END_POINT}/update`, data),

  delete: (id: number) => 
    apiClient.delete(`noitru/${id}`),

  updateChuyenPhong: (data: { ids: number[], dm_phong_id: number }) => 
    apiClient.put(`hs-noitru/update-chuyen-phong`, data),
  removeFromPhong: (ids: number[]) => 
    apiClient.put(`hs-noitru/remove-from-phong`, ids),

  updateGiaoVien: (data: { ids: number[], sis_giaovien_id: number }) => 
    apiClient.put(`hs-noitru/update-giaovien`, data),
};

export const hocSinhNoiTruStatusApi = {
  selectAll: () => 
    apiClient.get(`hs-noitru-status/select_all`),

  detail: (id: number) => 
    apiClient.get(`${HOC_SINH_NOI_TRU_STATUS_API_END_POINT}/${id}`),

  insert: (data: ts_hocsinh_noitru) => 
    apiClient.post(`${HOC_SINH_NOI_TRU_STATUS_API_END_POINT}/insert`, data),

  update: (data: ts_hocsinh_noitru) => 
    apiClient.put(`${HOC_SINH_NOI_TRU_STATUS_API_END_POINT}/update`, data),

  delete: (id: number) => 
    apiClient.delete(`hs-noitru-status/${id}`),
};