import { apiClient } from "./apiClient";

interface IKetQuaThiRequest {
  ex_kythi_id: number;
  nam_hoc: string;
  ex_thisinh_id?: number;
}

export const KETQUATHI_API_END_POINT = "ket-qua-thi";

export const ketQuaThiApi = {
  SelectThiSinhCoDiemThiAsync: ({ ex_kythi_id, nam_hoc }: Omit<IKetQuaThiRequest, 'ex_thisinh_id'>) => 
    apiClient.get(`${KETQUATHI_API_END_POINT}/nam_hoc/${nam_hoc}/ky-thi/${ex_kythi_id}/thi-sinh`),

  SelectDiemThiByThiSinhAsync: ({ ex_kythi_id, ex_thisinh_id, nam_hoc }: IKetQuaThiRequest) => 
    apiClient.get(`${KETQUATHI_API_END_POINT}/nam_hoc/${nam_hoc}/ky-thi/${ex_kythi_id}/thi-sinh/${ex_thisinh_id}/diem-thi-thi-sinh`),

  UpdateDanhGiaKetQuaThiAsync: (data: any) => 
    apiClient.post(`${KETQUATHI_API_END_POINT}/update-danh-gia-ket-qua-thi`, data),

  UpdateNhanXetKetQuaThiAsync: (data: any) => 
    apiClient.post(`${KETQUATHI_API_END_POINT}/update-nhan-xet-ket-qua-thi`, data),

  CreatePdfAsync: (data: any) => 
    apiClient.post(`${KETQUATHI_API_END_POINT}/create-pdf`, data),
};