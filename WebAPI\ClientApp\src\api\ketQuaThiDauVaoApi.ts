import { ex_ketquathidauvao } from "../models/response/quan-ly-thi/ex_ketquathidauvao";
import { apiClient } from "./apiClient";
import { IReadUploadedExcelFileRequest } from "../models/request/upload-file/IReadUploadedExcelFileRequest";

export interface CreateWithSubjectsRequest {
  nam_hoc: string;
  ts_hocsinh_id: number;
  dm_truong_id: number;
  lan_thi: number;
  ngay_thi: Date;
  ghi_chu: string;
  ex_trangthai_thisinh_id?: number;
}

export const KETQUA_THI_DAU_VAO_API_END_POINT = "ketquathidauvao";

export const ketQuaThiDauVaoApi = {
  // Get all kết quả thi đầu vào
  selectAll: () =>
    apiClient.get(`${KETQUA_THI_DAU_VAO_API_END_POINT}`),

  // Get kết quả thi đầu vào by id
  selectById: (id: number) =>
    apiClient.get(`${KETQUA_THI_DAU_VAO_API_END_POINT}/${id}`),

  // Get kết quả thi đầu vào by học sinh id
  selectByHocSinh: (ts_hocsinh_id: number) =>
    apiClient.get(`${KETQUA_THI_DAU_VAO_API_END_POINT}/hocsinh/${ts_hocsinh_id}`),

  // Get kết quả thi đầu vào by học sinh id and năm học
  selectByHocSinhNamHoc: (ts_hocsinh_id: number, nam_hoc: string) =>
    apiClient.get(`${KETQUA_THI_DAU_VAO_API_END_POINT}/hocsinh/${ts_hocsinh_id}/namhoc/${nam_hoc}`),

  // Create new kết quả thi đầu vào
  insert: (data: ex_ketquathidauvao) =>
    apiClient.post(`${KETQUA_THI_DAU_VAO_API_END_POINT}`, data),

  // Update existing kết quả thi đầu vào
  update: (data: ex_ketquathidauvao) =>
    apiClient.put(`${KETQUA_THI_DAU_VAO_API_END_POINT}`, data),

  // Delete kết quả thi đầu vào
  delete: (id: number) =>
    apiClient.delete(`${KETQUA_THI_DAU_VAO_API_END_POINT}/${id}`),

  // Create kết quả thi đầu vào with all subjects
  createWithSubjects: (data: CreateWithSubjectsRequest) =>
    apiClient.post(`${KETQUA_THI_DAU_VAO_API_END_POINT}/create-with-subjects`, data),

  // Validate import data
  validateImport: (data: IReadUploadedExcelFileRequest, nam_hoc: string, dm_coso_id: number) =>
    apiClient.put(`${KETQUA_THI_DAU_VAO_API_END_POINT}/validate-import/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}`, data),

  // Import data
  import: (data: IReadUploadedExcelFileRequest, nam_hoc: string, dm_coso_id: number) =>
    apiClient.put(`${KETQUA_THI_DAU_VAO_API_END_POINT}/import/nam-hoc/${nam_hoc}/co-so/${dm_coso_id}`, data)
};
