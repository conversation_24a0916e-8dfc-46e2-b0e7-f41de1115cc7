import { ex_ketquathidauvao_chitiet, KetQuaThiDauVaoChiTietItemResponse } from "../models/response/quan-ly-thi/ex_ketquathidauvao_chitiet";
import { apiClient } from "./apiClient";

export const KETQUA_THI_DAU_VAO_CHI_TIET_API_END_POINT = "ketquathidauvao-chitiet";

export const ketQuaThiDauVaoChiTietApi = {
  // Get all kết quả thi đầu vào chi tiết
  selectAll: () => 
    apiClient.get(`${KETQUA_THI_DAU_VAO_CHI_TIET_API_END_POINT}`),

  // Get kết quả thi đầu vào chi tiết by id
  selectById: (id: number) =>
    apiClient.get(`${KETQUA_THI_DAU_VAO_CHI_TIET_API_END_POINT}/${id}`),

  // Get kết quả thi đầu vào chi tiết by kết quả thi đầu vào id
  selectByKetQuaThiDauVao: (ex_ketquathidauvao_id: number) =>
    apiClient.get(`${KETQUA_THI_DAU_VAO_CHI_TIET_API_END_POINT}/ketquathidauvao/${ex_ketquathidauvao_id}`),

  // Create new kết quả thi đầu vào chi tiết
  insert: (data: ex_ketquathidauvao_chitiet) =>
    apiClient.post(`${KETQUA_THI_DAU_VAO_CHI_TIET_API_END_POINT}`, data),

  // Update existing kết quả thi đầu vào chi tiết
  update: (data: ex_ketquathidauvao_chitiet) =>
    apiClient.put(`${KETQUA_THI_DAU_VAO_CHI_TIET_API_END_POINT}`, data),

  // Delete kết quả thi đầu vào chi tiết
  delete: (id: number) =>
    apiClient.delete(`${KETQUA_THI_DAU_VAO_CHI_TIET_API_END_POINT}/${id}`)
};
