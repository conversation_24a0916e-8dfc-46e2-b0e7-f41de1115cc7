import { apiClient } from "./apiClient";

export const khoanNopApi={
    selectClbNgoaiKhoa : () => apiClient.get(`khoan-nop/clb-ngoai-khoa`),
    selectClbNgoaiKhoaBus : () => apiClient.get(`khoan-nop/clb-ngoai-khoa/bus`),
    selectAll : () => apiClient.get(`sf_khoannop`),
    selectAllByHocSinh : (ts_hocsinh_id:number, nam_hoc:string) => apiClient.get(`sf_khoannop/ts_hocsinh_id/${ts_hocsinh_id}/nam_hoc/${nam_hoc}`)
}