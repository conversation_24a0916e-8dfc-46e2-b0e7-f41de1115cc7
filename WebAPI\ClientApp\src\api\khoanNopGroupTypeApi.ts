import { ISaleSetKhoanNopGroupType } from "../models/response/finance/ISaleSetKhoanNopGroupType";
import { apiClient } from "./apiClient";
export const KHOAN_NOP_GROUP_TYPE_END_POINT = "sf_khoannop_group_type";

export const khoanNopGroupTypeApi={
    selectAll : () => apiClient.get(`sf_khoannop_group_type`),
    selectAllByHocSinh : (ts_hocsinh_id:number, nam_hoc:string) => apiClient.get(`sf_khoannop_group_type/ts_hocsinh_id/${ts_hocsinh_id}/nam_hoc/${nam_hoc}`),
    detail: (id: number) => apiClient.get(`${KHOAN_NOP_GROUP_TYPE_END_POINT}${id}`),
    insert: (payload: ISaleSetKhoanNopGroupType) => apiClient.post(KHOA<PERSON>_NOP_GROUP_TYPE_END_POINT, payload),
    update: (payload: ISaleSetKhoanNopGroupType) => apiClient.put(KHOAN_NOP_GROUP_TYPE_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${KHOAN_NOP_GROUP_TYPE_END_POINT}/${id}`),
}