import { IKhoiHeRequest } from '../models/request/category/IKhoiHeRequest';
import { IKhoi } from '../models/response/khoi/IKhoi';
import { apiClient } from './apiClient';

export const KHOI_API_END_POINT = "khoi";

export const khoiApi = {
    // L<PERSON>y tất cả các khối
    selectAll: () => apiClient.get(`${KHOI_API_END_POINT}`),

    // L<PERSON>y danh sách hệ theo mã khối
    selectHe: (dm_khoi_id: number) => 
        apiClient.get(`${KHOI_API_END_POINT}/${dm_khoi_id}/he`),

    detail: (id: number) => apiClient.get(`${KHOI_API_END_POINT}/${id}`),
    delete: (id: number) => apiClient.delete(`${KHOI_API_END_POINT}/${id}`),

    insert: (data: IKhoi) => apiClient.post(`${KHOI_API_END_POINT}`, data),
    update: (data: IKhoi) => apiClient.put(`${KHOI_API_END_POINT}`, data),

    update_he_by_khoi_id: (data: IKhoiHeRequest) => apiClient.post(`${KHOI_API_END_POINT}/update/he`, data),

    delete_he_by_khoi_id: (data: IKhoiHeRequest) => apiClient.post(`${KHOI_API_END_POINT}/delete/he`, data),

    // export const delete_he_by_khoi_id = async (data) => {
    //     return call_post_api(appInfo.api_url + '/khoi/delete_he_by_khoi_id', data);
    // };
    
    // export const update_he_by_khoi_id = async (data) => {
    //     return call_post_api(appInfo.api_url + '/khoi/update_he_by_khoi_id', data);
    // };
};
