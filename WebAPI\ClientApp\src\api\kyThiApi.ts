import { CoSoRequest } from "../models/request/ky-thi/CoSoRequest";
import { KyThiItemRequest } from "../models/request/ky-thi/KyThiItemRequest";
import { apiClient } from "./apiClient";

export const KY_THI_API_END_POINT = "ky-thi";

export const kyThiApi = {
  // Get all ky thi
  selectAll: () => 
    apiClient.get(`${KY_THI_API_END_POINT}`),

  // Get ky thi by truong
  selectByTruong: (dm_truong_id: number) =>
    apiClient.get(`truong/${dm_truong_id}/${KY_THI_API_END_POINT}`),

  // Get ky thi by co so with pagination
  selectByCoso: (data: CoSoRequest) =>
    apiClient.post(`${KY_THI_API_END_POINT}/select-by-coso`, data),

  // Get ky thi by id
  selectById: (id: number) =>
    apiClient.get(`${KY_THI_API_END_POINT}/${id}`),

  // Create new ky thi
  insert: (data: KyThiItemRequest) =>
    apiClient.post(`${KY_THI_API_END_POINT}`, data),

  // Update existing ky thi
  update: (data: KyThiItemRequest) =>
    apiClient.put(`${KY_THI_API_END_POINT}`, data),

  // Delete ky thi
  delete: (id: number) =>
    apiClient.delete(`${KY_THI_API_END_POINT}/${id}`),
};
