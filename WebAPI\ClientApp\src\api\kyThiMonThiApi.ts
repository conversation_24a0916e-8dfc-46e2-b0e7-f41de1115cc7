import { KyThiMonThiItemRequest } from "../models/request/kythi-monthi/KyThiMonThiItemRequest";
import { apiClient } from "./apiClient";


// Interface for the PhongThi request
export interface PhongThiRequest {
  ex_kythi_id: number;
}

const KYTHI_MONTHI_API_END_POINT = "kythi-monthi";

export const kyThiMonThiApi = {
  // Create new KyThi MonThi
  insert: (data: KyThiMonThiItemRequest) => 
    apiClient.post(`${KYTHI_MONTHI_API_END_POINT}`, data),

  // Get KyThi MonThi by KyThi ID
  selectByKyThi: (kyThiId: number) => 
    apiClient.get(`${KYTHI_MONTHI_API_END_POINT}/ky-thi/${kyThiId}/mon-thi`),

  // Get KyThi MonThi by ID
  detail: (id: number) => 
    apiClient.get(`${KYTHI_MONTHI_API_END_POINT}/${id}`),

  // Update existing KyThi MonThi
  update: (data: KyThiMonThiItemRequest) => 
    apiClient.put(`${KYTHI_MONTHI_API_END_POINT}`, data),

  // Delete KyThi MonThi
  delete: (id: number) => 
    apiClient.delete(`${KYTHI_MONTHI_API_END_POINT}/${id}`),

  // Get PhongThi ThiSinh
  selectMonThiPhongThi: (data: PhongThiRequest) =>
    apiClient.post(`${KYTHI_MONTHI_API_END_POINT}/phongthi-thisinh`, data),
};