// apis/loaiGiayToApi.ts
import { ILoaiGiayToSelectRequest } from '../models/request/category/ILoaiGiayToSelectRequest';
import { dm_loaigiayto } from '../models/response/category/dm_loaigiayto';
import { apiClient } from './apiClient';

export const LOAI_GIAY_TO_API_END_POINT = "loai-giay-to";

export const loaiGiayToApi = {
    selectAll: () => 
        apiClient.get(`${LOAI_GIAY_TO_API_END_POINT}`),
    
    selectByTruong: (dm_truong_id: number) => 
        apiClient.get(`truong/${dm_truong_id}/loai-giay-to`),
    
    selectByCoso: (dm_coso_id: number) => 
        apiClient.get(`co-so/${dm_coso_id}/loai-giay-to`),
    
    select: (data: ILoaiGiayToSelectRequest) => 
        apiClient.post(`${LOAI_GIAY_TO_API_END_POINT}/select`, data),
    
    detail: (id: number) => 
        apiClient.get(`${LOAI_GIAY_TO_API_END_POINT}/${id}`),
    
    insert: (data: dm_loaigiayto) => 
        apiClient.post(`${LOAI_GIAY_TO_API_END_POINT}`, data),
    
    update: (data: dm_loaigiayto) => 
        apiClient.put(`${LOAI_GIAY_TO_API_END_POINT}`, data),
    
    delete: (id: number) => 
        apiClient.delete(`${LOAI_GIAY_TO_API_END_POINT}/${id}`),
    
    // deleteTruongKhoiHeByLoaigiaytoId: (data: ILoaiGiayToRequest) => 
    //     apiClient.post(`${LOAI_GIAY_TO_API_END_POINT}/delete_truong_khoi_he_by_loaigiayto_id`, data),
    
    // updateTruongKhoiHeByLoaigiaytoId: (data: ILoaiGiayToRequest) => 
    //     apiClient.post(`${LOAI_GIAY_TO_API_END_POINT}/update_truong_khoi_he_by_loaigiayto_id`, data)
};