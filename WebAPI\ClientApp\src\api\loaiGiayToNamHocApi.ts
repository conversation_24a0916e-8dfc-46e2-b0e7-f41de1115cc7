// apis/loaiGiayToApi.ts

import { ILoaiGiayToNamHocSelectRequest } from '../models/request/category/ILoaiGiayToNamHocSelectRequest';
import { ILoaiGiayToItemRequest } from '../models/request/loai-giay-to-nam-hoc/ILoaiGiayToItemRequest';
import { apiClient } from './apiClient';

export const LOAI_GIAY_TO_NAM_HOC_API_END_POINT = "loai-giay-to/nam-hoc";

export const loaiGiayToNamHocApi = {
    Select: (data: ILoaiGiayToNamHocSelectRequest) => 
        apiClient.post(`${LOAI_GIAY_TO_NAM_HOC_API_END_POINT}/select`, data),
    
    Inserts: (data: ILoaiGiayToItemRequest) => 
        apiClient.post(`${LOAI_GIAY_TO_NAM_HOC_API_END_POINT}`, data),
    
    Deletes: (ids: number[]) => 
        apiClient.post(`${LOAI_GIAY_TO_NAM_HOC_API_END_POINT}/delete`, ids),

};
