import { dm_loaisuckhoe } from "../models/response/category/dm_loaisuckhoe";
import { apiClient } from "./apiClient";
import { apiGuestClient } from "./apiGuestClient";

export const LOAI_SUC_KHOE_END_POINT = "dm_loaisuckhoe";
export const loaiSucKhoeApi = {
    select_all: () => apiGuestClient.get(LOAI_SUC_KHOE_END_POINT),
    detail: (id: number) => apiClient.get(`${LOAI_SUC_KHOE_END_POINT}${id}`),
    insert: (payload: dm_loaisuckhoe) => apiClient.post(LOAI_SUC_KHOE_END_POINT, payload),
    update: (payload: dm_loaisuckhoe) => apiClient.put(LOAI_SUC_KHOE_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${LOAI_SUC_KHOE_END_POINT}/${id}`),
}