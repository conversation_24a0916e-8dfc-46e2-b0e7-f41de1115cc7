import { ILopNoiTruSelectedItemRequest } from "../models/request/danh-muc/ILopNoiTruSelectedItemRequest";
import { ILopSelectedItemRequest } from "../models/request/danh-muc/ILopSelectedItemRequest";
import { IChangLopRequest } from "../models/request/dm-lop/IChangLopRequest";
import { ILopChangeHocSinhClassRequest } from "../models/request/dm-lop/ILopChangeHocSinhClassRequest";
import { ILopChangeThongTuRequest } from "../models/request/dm-lop/ILopChangeThongTuRequest";
import { ILopChangeThuTuHocSinhRequest } from "../models/request/dm-lop/ILopChangeThuTuHocSinhRequest";
import { ILopChuNhiemRequest } from "../models/request/dm-lop/ILopChuNhiemRequest";
import { ILopHanhChinhUpdateSoDiemRequest } from "../models/request/dm-lop/ILopHanhChinhUpdateSoDiemRequest";
import { ILopLockParentAppHocSinhRequest } from "../models/request/dm-lop/ILopLockParentAppHocSinhRequest";
import { ILopRemoveHocSinhClassRequest } from "../models/request/dm-lop/ILopRemoveHocSinhClassRequest";
import { ILopSelectHocSinhRequest } from "../models/request/dm-lop/ILopSelectHocSinhRequest";
import { ILopSelectRequest } from "../models/request/dm-lop/ILopSelectRequest";
import { ILopImportPhanLopRequest } from "../models/request/dm-lop/LopImportPhanLopRequest";
import { IHocSinhSelectRequest } from "../models/request/hoc-sinh/IHocSinhSelectRequest";
import { IReadUploadedExcelFileRequest } from "../models/request/upload-file/IReadUploadedExcelFileRequest";
import { dm_lop } from "../models/response/dm-lop/dm_lop";
import { apiClient } from "./apiClient";
import { BaseCRUDApi } from "./baseCRUDApi";

export const API_LOP_PATH = "lop";
class DMLopApi extends BaseCRUDApi<dm_lop> {
    constructor({ path }: { path: string; }) {
        super(path);
    }
    SelectByNamHoc(nam_hoc: string) { return apiClient.get(`lop/nam-hoc/${nam_hoc}`) }

    Select(request: ILopSelectRequest) { return apiClient.post(`lop/select`, request) }
    SelectLopChuNhiemAsync(request: ILopChuNhiemRequest) {
        return apiClient.post(`lop/select-lop-chu-nhiem`, request)
    }
    SelectTreeView(request: ILopSelectRequest) {
        return apiClient.get(`lop/nam_hoc/${request.nam_hoc}/truong/${request.dm_truong_id}/tree-view?dm_he_id=${request.dm_he_id}&is_show_all=${request.is_show_all ?? false}&is_add_lop_from_tkb=${request.is_add_lop_from_tkb ?? false}`)
    }
    SelectTreeViewNamHocCoso(request: ILopSelectRequest) {
        return apiClient.get(`${API_LOP_PATH}/nam_hoc/${request.nam_hoc}/co_so/${request.dm_coso_id}/tree_view`)
    }
    SelectHocSinhByLopsAsync(request: ILopSelectedItemRequest) {
        return apiClient.post(`lop/hoc-sinh`, request)
    }
    SelectHocSinhNoiTruByLopsAsync(request: ILopNoiTruSelectedItemRequest) {
        return apiClient.post(`lop/hoc-sinh`, request)
    }
    SelectHocSinhByLopFilterAsync(request: ILopSelectedItemRequest) {
        return apiClient.post(`lop/hoc-sinh/select_filter`, request)
    }
    SelectHocSinhChuaPhanLopAsync(request: IHocSinhSelectRequest) {
        return apiClient.post(`lop/hoc-sinh/chua-phan-lop`, request)
    }
    ChangeThuTuTheoHoTenABCAsync(dm_truong_id: number) {
        return apiClient.put(`lop/hoc-sinh/truong/${dm_truong_id}/change-thutu-macdinh`)
    }
    ChangeThuTuHocSinhAsync(request: ILopChangeThuTuHocSinhRequest) {
        return apiClient.put(`lop/hoc-sinh/change-thutu`, request)
    }
    ChangeHocSinhClassAsync(request: IChangLopRequest) {
        return apiClient.put(`lop/hoc-sinh/change-class`, request)
    }
    RemoveHocSinhClassAsync(request: ILopRemoveHocSinhClassRequest) {
        return apiClient.put(`lop/hoc-sinh/remove-class`, request)
    }
    LoadFromExcel(request: FormData) {
        return apiClient.upload(`lop/hoc-sinh/import/load-from-excel`, request)
    }
    SaveDataExcel(request: ILopImportPhanLopRequest) {
        return apiClient.post(`lop/hoc-sinh/import/save-data-excel`, request)
    }
    ChangeThongTuAsync(request: ILopChangeThongTuRequest) {
        return apiClient.put(`lop/hoc-sinh/change-thongtu`, request)
    }
    UpdateDgdkHocTap(request: ILopHanhChinhUpdateSoDiemRequest) {
        return apiClient.post(`lop/so-diem-nlpc/dinh-ky`, request)
    }
    UpdateDgtxHocTap(request: ILopHanhChinhUpdateSoDiemRequest) {
        return apiClient.post(`lop/so-diem-nlpc/thuong-xuyen`, request)
    }
    LockParentApp(request: ILopLockParentAppHocSinhRequest) {
        return apiClient.put(`lop/hoc-sinh/lock-parent-app`, request)
    }
    SelectPhuHuynh(request: any) {
        return apiClient.post(`lop/phu-huynh`, request)

    }
    ValidateImportPhanLop(request: IReadUploadedExcelFileRequest, dm_coso_id: number) {
        return apiClient.post(`lop/validate-import-phan-lop/coso/${dm_coso_id}`, request)
    }
    ImportPhanLop(request: IReadUploadedExcelFileRequest, dm_coso_id: number) {
        return apiClient.post(`lop/import-phan-lop/coso/${dm_coso_id}`, request)
    }
}
export const lopApi = new DMLopApi({ path: API_LOP_PATH });