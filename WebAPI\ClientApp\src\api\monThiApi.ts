import { KyThiMonThiItemRequest } from "../models/request/kythi-monthi/KyThiMonThiItemRequest";
import { ex_monthi } from "../models/response/mon-thi/ex_monthi";
import { ex_thangdiem } from "../models/response/thang-diem/ex_thangdiem";
import { apiClient } from "./apiClient";

export const MON_THI_API_END_POINT = "mon-thi";

export const monThiApi = {
  // Get all môn thi
  selectAll: () => 
    apiClient.get(`${MON_THI_API_END_POINT}`),

  // Get môn thi by cơ sở ID
  selectByCoSo: (dm_coso_id: number) => 
    apiClient.get(`co-so/${dm_coso_id}/${MON_THI_API_END_POINT}`),

  // Get môn thi by kỳ thi ID
  selectByKyThi: (ex_kythi_id: number) => 
    apiClient.get(`ky-thi/${ex_kythi_id}/${MON_THI_API_END_POINT}`),

  // Get môn thi by kỳ thi ID (POST method)
  selectByKyThiPost: (ex_kythi_id: number) => 
    apiClient.post(`${MON_THI_API_END_POINT}/select-by-ky-thi`, { ex_kythi_id: ex_kythi_id.toString() }),

  // Get môn thi not in kỳ thi
  selectByNotInKyThi: (ex_kythi_id: number) => 
    apiClient.get(`non-ky-thi/${ex_kythi_id}/${MON_THI_API_END_POINT}`),

  // Get môn thi by ID
  detail: (id: number) => 
    apiClient.get(`${MON_THI_API_END_POINT}/${id}`),

  // Insert new môn thi
  insert: (data: ex_monthi) => 
    apiClient.post(`${MON_THI_API_END_POINT}`, data),

  // Insert multiple môn thi to kỳ thi
  insertMultiple: (data: KyThiMonThiItemRequest) => 
    apiClient.post(`${MON_THI_API_END_POINT}/insert-multiple`, data),

  // Update môn thi
  update: (data: ex_monthi) => 
    apiClient.put(`${MON_THI_API_END_POINT}`, data),

  // Delete môn thi
  delete: (id: number) => 
    apiClient.delete(`${MON_THI_API_END_POINT}/${id}`),
};