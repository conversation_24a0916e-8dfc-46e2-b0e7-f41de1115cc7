import { apiClient } from './apiClient';
import { ex_monthidauvao } from '../models/response/quan-ly-thi/ex_monthidauvao';

const MON_THI_DAU_VAO_API_END_POINT = 'monthidauvao';

export const monThiDauVaoApi = {
  // Get all môn thi đầu vào
  selectAll: () => 
    apiClient.get(`${MON_THI_DAU_VAO_API_END_POINT}`),

  // Get môn thi đầu vào by id
  selectById: (id: number) =>
    apiClient.get(`${MON_THI_DAU_VAO_API_END_POINT}/${id}`),

  // Create new môn thi đầu vào
  insert: (data: ex_monthidauvao) =>
    apiClient.post(`${MON_THI_DAU_VAO_API_END_POINT}`, data),

  // Update existing môn thi đầu vào
  update: (data: ex_monthidauvao) =>
    apiClient.put(`${MON_THI_DAU_VAO_API_END_POINT}`, data),

  // Delete môn thi đầu vào
  delete: (id: number) =>
    apiClient.delete(`${MON_THI_DAU_VAO_API_END_POINT}/${id}`)
};
