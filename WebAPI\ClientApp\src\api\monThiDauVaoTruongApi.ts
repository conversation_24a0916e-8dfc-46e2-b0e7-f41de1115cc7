import { apiClient } from './apiClient';
import { ex_monthidauvao_truong } from '../models/response/quan-ly-thi/ex_monthidauvao_truong';

const MON_THI_DAU_VAO_TRUONG_API_END_POINT = 'monthidauvao_truong';

export const monThiDauVaoTruongApi = {
  // Get all môn thi đầu vào trường
  selectAll: () =>
    apiClient.get(`${MON_THI_DAU_VAO_TRUONG_API_END_POINT}`),

  // Get môn thi đầu vào trường by id
  selectById: (id: number) =>
    apiClient.get(`${MON_THI_DAU_VAO_TRUONG_API_END_POINT}/${id}`),

  // Get môn thi đầu vào trường by môn thi đầu vào id
  selectByMonThiDauVao: (ex_monthidauvao_id: number) =>
    apiClient.get(`${MON_THI_DAU_VAO_TRUONG_API_END_POINT}/by_monthidauvao/${ex_monthidauvao_id}`),

  // Get môn thi đầu vào trường by trường id
  selectByTruong: (dm_truong_id: number) =>
    apiClient.get(`${MON_THI_DAU_VAO_TRUONG_API_END_POINT}/by_truong/${dm_truong_id}`),

  // Create new môn thi đầu vào trường
  insert: (data: ex_monthidauvao_truong) =>
    apiClient.post(`${MON_THI_DAU_VAO_TRUONG_API_END_POINT}`, data),

  // Update existing môn thi đầu vào trường
  update: (data: ex_monthidauvao_truong) =>
    apiClient.put(`${MON_THI_DAU_VAO_TRUONG_API_END_POINT}`, data),

  // Delete môn thi đầu vào trường
  delete: (id: number) =>
    apiClient.delete(`${MON_THI_DAU_VAO_TRUONG_API_END_POINT}/${id}`)
};
