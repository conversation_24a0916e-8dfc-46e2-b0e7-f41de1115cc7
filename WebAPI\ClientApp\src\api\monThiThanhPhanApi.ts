import { KyThiMonThiItemRequest } from "../models/request/kythi-monthi/KyThiMonThiItemRequest";
import { ex_monthi } from "../models/response/mon-thi/ex_monthi";
import { ex_monthi_thanhphan } from "../models/response/monthi_thanhphan/ex_monthi_thanhphan";
import { ex_thangdiem } from "../models/response/thang-diem/ex_thangdiem";
import { apiClient } from "./apiClient";

export const MON_THI_THANH_PHAN_API_END_POINT = "mon-thi-thanh-phan";

export const monThiThanhPhanApi = {
  // Get all môn thi thành phần
  selectAll: () => 
    apiClient.get(`${MON_THI_THANH_PHAN_API_END_POINT}`),

  // Get môn thi thành phần by môn thi ID
  selectByMonThi: (ex_monthi_id: number) => 
    apiClient.get(`mon-thi/${ex_monthi_id}/${MON_THI_THANH_PHAN_API_END_POINT}`),

  // Get môn thi thành phần by ID
  detail: (id: number) => 
    apiClient.get(`${MON_THI_THANH_PHAN_API_END_POINT}/${id}`),

  // Insert new môn thi thành phần
  insert: (data: ex_monthi_thanhphan) => 
    apiClient.post(`${MON_THI_THANH_PHAN_API_END_POINT}`, data),

  // Update môn thi thành phần
  update: (data: ex_monthi_thanhphan) => 
    apiClient.put(`${MON_THI_THANH_PHAN_API_END_POINT}`, data),

  // Delete môn thi thành phần
  delete: (id: number) => 
    apiClient.delete(`${MON_THI_THANH_PHAN_API_END_POINT}/${id}`),
};