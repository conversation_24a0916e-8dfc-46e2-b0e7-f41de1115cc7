import { IDmNguonBietToi } from "../models/response/category/IDmNguonBietToi";
import { apiClient } from "./apiClient";
import { apiGuestClient } from "./apiGuestClient";

export const NGUON_BIET_TOI_END_POINT = "nguon-biet-toi";

export const nguonBietToiApi = {
    select_all: () => apiGuestClient.get(NGUON_BIET_TOI_END_POINT),
    detail: (id: number) => apiClient.get(`${NGUON_BIET_TOI_END_POINT}${id}`),
    insert: (payload: IDmNguonBietToi) => apiClient.post(NGUON_BIET_TOI_END_POINT, payload),
    update: (payload: IDmNguonBietToi) => apiClient.put(NGUON_BIET_TOI_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${NGUON_BIET_TOI_END_POINT}/${id}`),

}