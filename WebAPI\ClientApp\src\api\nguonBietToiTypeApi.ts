import { IDmNguonBietToiType } from "../models/response/category/IDmNguonBietToiType";
import { apiClient } from "./apiClient";

export const NGUON_BIET_TOI_TYPE_END_POINT = "nguon-biet-toi-type";

export const nguonBietToiTypeApi = {
    select_all: () => apiClient.get(NGUON_BIET_TOI_TYPE_END_POINT),
    detail: (id: number) => apiClient.get(`${NGUON_BIET_TOI_TYPE_END_POINT}${id}`),
    insert: (payload: IDmNguonBietToiType) => apiClient.post(NGUON_BIET_TOI_TYPE_END_POINT, payload),
    update: (payload: IDmNguonBietToiType) => apiClient.put(NGUON_BIET_TOI_TYPE_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${NGUON_BIET_TOI_TYPE_END_POINT}/${id}`),

}