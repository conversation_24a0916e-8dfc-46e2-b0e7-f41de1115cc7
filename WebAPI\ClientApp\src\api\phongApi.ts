import { dm_phong } from '../models/response/phong/dm_phong';
import { apiClient } from './apiClient';

export const PHONG_API_END_POINT = "phong";

export const phongApi = {
    selectAll: () => apiClient.get(`${PHONG_API_END_POINT}`),
    detail: (id: number) => apiClient.get(`${PHONG_API_END_POINT}/${id}`),
    delete: (id: number) => apiClient.delete(`${PHONG_API_END_POINT}/${id}`),

    insert: (data: dm_phong) => apiClient.post(`${PHONG_API_END_POINT}`, data),
    update: (data: dm_phong) => apiClient.put(`${PHONG_API_END_POINT}`, data),
};
