import { PhongThiInsertMultipleRequest } from "../models/request/phong-thi/PhongThiInsertMultipleRequest";
import { ex_phongthi } from "../models/response/phong-thi/ex_phongthi";
import { apiClient } from "./apiClient";


export const PHONG_THI_API_END_POINT = "phong-thi";

export const phongThiApi = {
  // Get list of PhongThi by MonThi
  selectByMonThi: (ex_kythi_monthi_id: number) => 
    apiClient.get(`${PHONG_THI_API_END_POINT}/ky-mon-thi/${ex_kythi_monthi_id}/select`),

  // Get PhongHoc by Truong and MonThi
  selectPhongHoc: (dm_truong_id: number, ex_kythi_monthi_id: number) =>
    apiClient.get(`${PHONG_THI_API_END_POINT}/truong/${dm_truong_id}/ky-mon-thi/${ex_kythi_monthi_id}/phong-hoc`),

  // Get TreeView data by KyThi
  selectTreeView: (ex_kythi_id: number) =>
    apiClient.get(`${PHONG_THI_API_END_POINT}/ky-thi/${ex_kythi_id}/treeview`),

  // Insert new PhongThi
  insert: (data: ex_phongthi) => 
    apiClient.post(`${PHONG_THI_API_END_POINT}`, data),

  // Insert multiple PhongThi
  insertMultiple: (data: PhongThiInsertMultipleRequest) =>
    apiClient.post(`${PHONG_THI_API_END_POINT}/insert-multiple`, data),

  // Update PhongThi
  update: (data: ex_phongthi) =>
    apiClient.put(`${PHONG_THI_API_END_POINT}`, data),

  // Update GiaoVienCoiThi
  updateGiaoVienCoiThi: (data: ex_phongthi[]) =>
    apiClient.post(`${PHONG_THI_API_END_POINT}/giao-vien-coi-thi`, data),

  // Update GiaoVienChamThi
  updateGiaoVienChamThi: (data: ex_phongthi[]) =>
    apiClient.post(`${PHONG_THI_API_END_POINT}/giao-vien-cham-thi`, data),

  // Delete PhongThi
  delete: (id: number) =>
    apiClient.delete(`${PHONG_THI_API_END_POINT}/${id}`),
};