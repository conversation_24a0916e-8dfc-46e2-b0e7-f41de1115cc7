import { IPhuHuynhUpdateInfoRequest } from "../models/request/phu-huynh/IPhuHuynhUpdateInfoRequest";
import { IParentBaseInformation } from "../models/request/acceptance-form/IParentBaseInformation";
import { apiClient } from "./apiClient";
import { apiGuestClient } from "./apiGuestClient";
import { PhuHuynhUpdateTuVanVienRequest } from "../models/request/phu-huynh/PhuHuynhUpdateTuVanVienRequest";

export const phuHuynhApi = {
    SelectByHocSinhQuanHeGiaDinh: (ts_hocsinh_id: number, ts_quanhegiadinh_id: number) => apiClient.get(`phu-huynh/hocsinh/${ts_hocsinh_id}/quanhegiadinh/${ts_quanhegiadinh_id}`),

    SelectAll: (dm_coso_id: number, ts_phuhuynh_adm_status_id: number, dm_chiendich_id: number) => apiClient.get(`phu-huynh/coso/${dm_coso_id}/status/${ts_phuhuynh_adm_status_id}/chiendich/${dm_chiendich_id}`),
    SelectPhuHuynhEmail: (dm_coso_id: number, nam_hoc: string) => apiClient.get(`phu-huynh/coso/${dm_coso_id}/nam_hoc/${nam_hoc}`),

    getById: (id: number) =>
        apiClient.get(`phu-huynh/${id}`),

    delete: (id: number) =>
        apiClient.delete(`phu-huynh/${id}`),

    updateInfo: (request: IPhuHuynhUpdateInfoRequest) =>
        apiClient.put(`phu-huynh`, request),
    insertInfo: (request: IPhuHuynhUpdateInfoRequest) =>
        apiClient.post(`phu-huynh`, request),
    selectFamily: (id: number) =>
        apiClient.get(`phu-huynh/${id}/family`),
    selectLichSuDangKy: (id: number) =>
        apiClient.get(`phu-huynh/${id}/lich-su-dang-ky`),
    SelectParentsByHocSinh: (ts_hocsinh_id: number) => apiGuestClient.get(`phu-huynh/hoc-sinh/${ts_hocsinh_id}`),

    UpdateParentsInfomation: (data: IParentBaseInformation[]) => apiGuestClient.put(`phu-huynh/parents-information`, data),
    UpdateTuVanVien: (data: PhuHuynhUpdateTuVanVienRequest) => apiClient.put(`phu-huynh/tu-van-vien`, data),
    UpdateTuVanVienSingle:  (ts_phuhuynh_id: number, user_id: number) => apiClient.put(`phu-huynh/tu-van-vien-single/${ts_phuhuynh_id}/${user_id}`),

}

