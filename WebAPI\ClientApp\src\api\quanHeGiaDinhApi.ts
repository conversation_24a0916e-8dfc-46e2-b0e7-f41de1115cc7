import { QuanHeGiaDinhDeleteItemRequest } from "../models/request/hoc-sinh/QuanHeGiaDinhDeleteItemRequest ";
import { QuanHeGiaDinhInsertRequest } from "../models/request/hoc-sinh/QuanHeGiaDinhInsertRequest ";
import { apiClient } from "./apiClient";

export const API_QUANHEGIADINH_ENDPOINT = "quan-he-gia-dinh";
export interface QHGDData {
  ts_hocsinh_id?: number;
}

export const quanHeGiaDinhApi = {
  selectByHocSinh: (ts_hocsinh_id: number) =>
    apiClient.get(`${API_QUANHEGIADINH_ENDPOINT}/hoc-sinh/${ts_hocsinh_id}`),

  deleteQHGD: (data: QuanHeGiaDinhDeleteItemRequest) =>
    apiClient.post(`${API_QUANHEGIADINH_ENDPOINT}/delete-qhgd`, data),

  insert: (data: QuanHeGiaDinhInsertRequest) => apiClient.post(API_QUANHEGIADINH_ENDPOINT, data),

  update: (data: QuanHeGiaDinhInsertRequest) => apiClient.put(API_QUANHEGIADINH_ENDPOINT, data),

  delete: (id: number) =>
    apiClient.delete(`${API_QUANHEGIADINH_ENDPOINT}/${id}`),

  detail: (id: number) => apiClient.get(`${API_QUANHEGIADINH_ENDPOINT}/${id}`),
};
