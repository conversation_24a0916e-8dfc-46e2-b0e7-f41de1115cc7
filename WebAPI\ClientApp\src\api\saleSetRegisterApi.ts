import { SaleSetRegisterItemRequest } from '../models/request/dich-vu/ISaleSetRegisterItemRequest ';
import { ISaleSetRegisterRequest } from '../models/request/dich-vu/ISaleSetRegisterRequest';
import { ISfSalesetRegister } from '../models/response/dich-vu/ISfSalesetRegister';
import { apiClient } from './apiClient';

export const SALESET_REGISTER_API_ENDPOINT = 'saleset-register';

export const saleSetRegisterApi = {
    Select: (request: ISaleSetRegisterRequest) => apiClient.post(`${SALESET_REGISTER_API_ENDPOINT}/select`, request),
    SelectHinhThucNopPhi: (request: ISaleSetRegisterRequest) => apiClient.post(`${SALESET_REGISTER_API_ENDPOINT}/select-hinhthuc-nopphi`, request),
    Insert: (data: ISfSalesetRegister) => apiClient.post(`${SALESET_REGISTER_API_ENDPOINT}`, data),
    InsertMultiple: (data: SaleSetRegisterItemRequest) => apiClient.post(`saleset-register/insert-multiple`, data),
    Update: (data: ISfSalesetRegister) => apiClient.put(`${SALESET_REGISTER_API_ENDPOINT}`, data),
    Delete: (id: number) => apiClient.delete(`${SALESET_REGISTER_API_ENDPOINT}/${id}`),
};
