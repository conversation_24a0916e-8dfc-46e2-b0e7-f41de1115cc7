import { CheckSetRegisterSaleRequest } from "../models/request/dich-vu/CheckSetRegisterSaleRequest";
import { ItemRequestKhoanNopHocSinh } from "../models/request/dich-vu/IItemRequestKhoanNopHocSinh";
import { KhoanThuRequestModel } from "../models/request/dich-vu/KhoanThuRequestModel";
import { apiClient } from "./apiClient";

const KHOANNOP_GROUP_API_ENDPOINT = "sf-khoannop-group";

export const khoanNopGroupApi = {
  SelectAll: () => apiClient.get(`${KHOANNOP_GROUP_API_ENDPOINT}`),

  SelectIsDichVu: (dm_coso_id: string) =>
    apiClient.get(`co-so/${dm_coso_id}/sf-khoannop-group`),

  SelectByHocSinh: (data: ItemRequestKhoanNopHocSinh) =>
    apiClient.post(`${KHOANNOP_GROUP_API_ENDPOINT}/select-by-hs`, data),

  GetKhoanNop: (
    truong_id: string | number,
    khoi_id: string | number,
    he_id: string | number,
    nam_hoc: string,
    coso: string | number,
    hoc_sinh: string | number
  ) =>
    apiClient.get(
      `${KHOANNOP_GROUP_API_ENDPOINT}/get-khoan-nop/${truong_id}/${khoi_id}/${he_id}/${nam_hoc}/${coso}/${hoc_sinh}`
    ),

  InsertKhoanThu: (formData: KhoanThuRequestModel) =>
    apiClient.post(`${KHOANNOP_GROUP_API_ENDPOINT}/insert-khoan-thu`, formData),

  CheckPaymentStatus: (formData: CheckSetRegisterSaleRequest) =>
    apiClient.post(
      `${KHOANNOP_GROUP_API_ENDPOINT}/check-payment-status`,
      formData
    ),
};
