import { IHocSinhSearchRequest } from "../models/request/hoc-sinh/IHocSinhSearchRequest";
import { SaleSetRegisterDichVuItemRequest } from "../models/response/dich-vu/SaleSetRegisterDichVuItemRequest";
import { sf_saleset_register_dichvu } from "../models/response/dich-vu/sf_saleset_register_dichvu";
import { apiClient } from "./apiClient";

// API Endpoint
const SALESET_REGISTER_DICHVU_API_END_POINT = "sf-saleset-register-dichvu";

// API Methods
export const saleSetRegisterDichVuApi = {
  SelectByCoSo: (data: IHocSinhSearchRequest) =>
    apiClient.post(
      `${SALESET_REGISTER_DICHVU_API_END_POINT}/select/by/coso`,
      data
    ),

  Detail: (id: number) =>
    apiClient.get(`${SALESET_REGISTER_DICHVU_API_END_POINT}/${id}`),

  Insert: (data: sf_saleset_register_dichvu) =>
    apiClient.post(`${SALESET_REGISTER_DICHVU_API_END_POINT}`, data),

  InsertMultiple: (data: SaleSetRegisterDichVuItemRequest) =>
    apiClient.post(
      `${SALESET_REGISTER_DICHVU_API_END_POINT}/insert-multiple`,
      data
    ),

  Update: (data: sf_saleset_register_dichvu) =>
    apiClient.put(`${SALESET_REGISTER_DICHVU_API_END_POINT}`, data),

  Delete: (id: number) =>
    apiClient.delete(`${SALESET_REGISTER_DICHVU_API_END_POINT}/${id}`),
};
