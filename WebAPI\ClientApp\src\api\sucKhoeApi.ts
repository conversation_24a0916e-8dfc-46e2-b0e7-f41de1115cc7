import { dm_suckhoe } from '../models/response/category/dm_suckhoe';
import { apiClient } from './apiClient';
import { apiGuestClient } from './apiGuestClient';

export const SUC_KHOE_API_END_POINT = "dm_suckhoe";

export const sucKhoeApi = {
    selectAll: () => apiGuestClient.get(`${SUC_KHOE_API_END_POINT}`),
    selectAllView: () => apiGuestClient.get(`${SUC_KHOE_API_END_POINT}/view`),
    detail: (id: number) => apiClient.get(`${SUC_KHOE_API_END_POINT}${id}`),
    insert: (payload: dm_suckhoe) => apiClient.post(SUC_KHOE_API_END_POINT, payload),
    update: (payload: dm_suckhoe) => apiClient.put(SUC_KHOE_API_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${SUC_KHOE_API_END_POINT}/${id}`),
};

