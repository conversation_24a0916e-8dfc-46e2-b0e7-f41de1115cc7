import { ex_thangdiem } from "../models/response/thang-diem/ex_thangdiem";
import { apiClient } from "./apiClient";

export const THANG_DIEM_API_END_POINT = "thang-diem";

export const thangDiemApi = {
  selectAll: () => apiClient.get(`${THANG_DIEM_API_END_POINT}`),
  detail: (id: number) => apiClient.get(`${THANG_DIEM_API_END_POINT}/${id}`),
  delete: (id: number) => apiClient.delete(`${THANG_DIEM_API_END_POINT}/${id}`),
  insert: (data: ex_thangdiem) => apiClient.post(`${THANG_DIEM_API_END_POINT}`, data),
  update: (data: ex_thangdiem) => apiClient.put(`${THANG_DIEM_API_END_POINT}`, data),
};
