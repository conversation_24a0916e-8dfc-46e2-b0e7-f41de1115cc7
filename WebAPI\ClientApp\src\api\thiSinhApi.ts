import { ThiSinhAddHocSinhRequest } from "../models/request/thi-sinh/ThiSinhAddHocSinhRequest";
import { ThiSinhDanhSoBaoDanhRequest } from "../models/request/thi-sinh/ThiSinhDanhSoBaoDanhRequest";
import { ThiSinhDeletesRequest } from "../models/request/thi-sinh/ThiSinhDeletesRequest";
import { ThiSinhSelectHocSinhRequest } from "../models/request/thi-sinh/ThiSinhSelectHocSinhRequest";
import { ThiSinhSelectRequest } from "../models/request/thi-sinh/ThiSinhSelectRequest";
import { apiClient } from "./apiClient";

export const THI_SINH_API_END_POINT = "thi-sinh";

export const thiSinhApi = {
  // Get all thi sinh
  select: (data: ThiSinhSelectRequest) =>
    apiClient.post(`${THI_SINH_API_END_POINT}/select`, data),

  // Get hoc sinh for thi sinh
  selectHocSinh: (data: ThiSinhSelectHocSinhRequest) =>
    apiClient.post(`${THI_SINH_API_END_POINT}/hoc-sinh/select`, data),

  // Add hoc sinh to thi sinh
  addHocSinh: (data: ThiSinhAddHocSinhRequest) =>
    apiClient.post(`${THI_SINH_API_END_POINT}/hoc-sinh/add`, data),

  // Mark student numbers
  danhSoBaoDanh: (data: ThiSinhDanhSoBaoDanhRequest) =>
    apiClient.post(`${THI_SINH_API_END_POINT}/danh-so-bao-danh`, data),

  // Delete thi sinh
  delete: (id: number) =>
    apiClient.delete(`${THI_SINH_API_END_POINT}/${id}`),

  // Delete multiple thi sinh
  deleteMultiple: (data: ThiSinhDeletesRequest) =>
    apiClient.post(`${THI_SINH_API_END_POINT}/deletes`, data),

  // Get hoc sinh by thi sinh
  selectByThiSinh: (ex_thisinh_id: number) =>
    apiClient.post(`${THI_SINH_API_END_POINT}/hoc-sinh/select_by_thisinh`, ex_thisinh_id),
};