import { IHe } from '../models/response/he/IHe';
import { ts_thoihocbaoluu } from '../models/response/thoi-hoc-bao-luu/ts_thoihocbaoluu';
import { apiClient } from './apiClient';

export const THOI_HOC_BAO_LUU_API_END_POINT = "thoi-hoc-bao-luu";
export const thoiHocBaoLuuApi = {
    selectAllView: () => apiClient.post(`${THOI_HOC_BAO_LUU_API_END_POINT}/select-all`, {}),
    
    detail: (id: number) => apiClient.get(`${THOI_HOC_BAO_LUU_API_END_POINT}/${id}`),
    delete: (id: number) => apiClient.delete(`${THOI_HOC_BAO_LUU_API_END_POINT}/${id}`),

    insert: (data: ts_thoihocbaoluu) => apiClient.post(`${THOI_HOC_BAO_LUU_API_END_POINT}`, data),
    update: (data: ts_thoihocbaoluu) => apiClient.put(`${THOI_HOC_BAO_LUU_API_END_POINT}`, data),
};