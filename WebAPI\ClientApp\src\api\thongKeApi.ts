import { IThongKeSiSoSelectRequest } from "../models/request/thong-ke/IThongKeSiSoSelectRequest";
import { apiClient } from "./apiClient";

export const thongKeApi = {
    ThongKeSiSo: (request: IThongKeSiSoSelectRequest) =>
        apiClient.get(`thong-ke/co-so/${request.dm_coso_id}/nam-hoc/${request.nam_hoc}/si-so`),
    ThongKeTrangThai: (request: IThongKeSiSoSelectRequest) =>
        apiClient.get(`thong-ke/co-so/${request.dm_coso_id}/nam-hoc/${request.nam_hoc}/trang-thai`),
    ThongKeCSKH: (request: IThongKeSiSoSelectRequest) =>
        apiClient.get(`thong-ke/co-so/${request.dm_coso_id}/nam-hoc/${request.nam_hoc}/cskh`),
    ThongKeCSKHByMonth: (request: IThongKeSiSoSelectRequest) =>
        apiClient.get(`thong-ke/co-so/${request.dm_coso_id}/nam-hoc/${request.nam_hoc}/cskh/by-month`),
    ThongKeCRMByChienDich: (request: IThongKeSiSoSelectRequest) =>
        apiClient.get(`thong-ke/co-so/${request.dm_coso_id}/nam-hoc/${request.nam_hoc}/crm/by-chien-dich`),
    ThongKeCRMByNguonBietToi: (request: IThongKeSiSoSelectRequest) =>
        apiClient.get(`thong-ke/co-so/${request.dm_coso_id}/nam-hoc/${request.nam_hoc}/crm/by-nguon-biet-toi`)
}