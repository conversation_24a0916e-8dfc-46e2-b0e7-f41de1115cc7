import { IThongTinCbnv } from "../models/response/hoc-sinh/IThongTinCbnv";
import { apiClient } from "./apiClient";
import { apiGuestClient } from "./apiGuestClient";

export const THONG_TIN_CNBV_END_POINT = "thongtin-cbnv";

export const thongTinCbnvApi = {
    select_all: () => apiGuestClient.get(THONG_TIN_CNBV_END_POINT),
    detail: (id: number) => apiClient.get(`${THONG_TIN_CNBV_END_POINT}${id}`),
    insert: (payload: IThongTinCbnv) => apiClient.post(THONG_TIN_CNBV_END_POINT, payload),
    update: (payload: IThongTinCbnv) => apiClient.put(THONG_TIN_CNBV_END_POINT, payload),
    delete: (id: number) => apiClient.delete(`${THONG_TIN_CNBV_END_POINT}/${id}`),

}