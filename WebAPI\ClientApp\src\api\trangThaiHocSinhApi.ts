import { apiClient } from './apiClient';
import { ITrangThaiHocSinh } from '../models/response/category/ITrangThaiHocSinh';  
import { ITrangThaiHocSinhCoSoItemRequest } from '../models/response/category/ITrangThaiHocSinhCoSoItemRequest';
import { ITrangThaiHocSinhItemRequest } from '../models/response/category/ITrangThaiHocSinhItemRequest';
export const TRANG_THAI_HOC_SINH_API_END_POINT = "dm_trangthaihocsinh";

export const trangThaiHocSinhApi = {
    selectAll: () => apiClient.get(`${TRANG_THAI_HOC_SINH_API_END_POINT}`),
    selectAllCheckDangKy: (dm_coso_id: number) => apiClient.get(`${TRANG_THAI_HOC_SINH_API_END_POINT}/check_dang_ky/${dm_coso_id}`),
    selectByCoSo: (dm_coso_id: number) => apiClient.get(`${TRANG_THAI_HOC_SINH_API_END_POINT}/select_by_coso/${dm_coso_id}`),
    detail: (id: number) => apiClient.get(`${TRANG_THAI_HOC_SINH_API_END_POINT}/${id}`),
    insert: (data: ITrangThaiHocSinh) => apiClient.post(`${TRANG_THAI_HOC_SINH_API_END_POINT}`, data),
    insertMultiple: (data: ITrangThaiHocSinhCoSoItemRequest) => apiClient.post(`${TRANG_THAI_HOC_SINH_API_END_POINT}/insert-multiple`, data),
    update: (data: ITrangThaiHocSinh) => apiClient.put(`${TRANG_THAI_HOC_SINH_API_END_POINT}`, data),
    updateThuTuAsync: (data: ITrangThaiHocSinhItemRequest) => apiClient.put(`${TRANG_THAI_HOC_SINH_API_END_POINT}/update-thu-tu`, data),
    delete: (id: number) => apiClient.delete(`${TRANG_THAI_HOC_SINH_API_END_POINT}/${id}`),
};
