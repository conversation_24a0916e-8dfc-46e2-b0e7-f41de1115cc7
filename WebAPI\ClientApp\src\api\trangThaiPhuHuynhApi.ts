import { ITrangThai<PERSON>hu<PERSON>uynhCoSoItemRequest } from './../models/request/category/ITrangThaiPhuHuynhCoSoItemRequest';
import { apiClient } from './apiClient';
import { ts_phuhuynh_adm_status } from '../models/response/category/ts_phuhuynh_adm_status';

export const TRANG_THAI_PHU_HUYNH_API_END_POINT = "ts_phuhuynh_adm_status";

export const trangThaiPhuHuynhApi = {
    selectAll: () => apiClient.get(`${TRANG_THAI_PHU_HUYNH_API_END_POINT}`),
    selectAllCheckDangKy: (dm_coso_id: number) => apiClient.get(`${TRANG_THAI_PHU_HUYNH_API_END_POINT}/check_dang_ky/${dm_coso_id}`),
    selectByCoSo: (dm_coso_id: number) => apiClient.get(`${TRANG_THAI_PHU_HUYNH_API_END_POINT}/select_by_coso/${dm_coso_id}`),
    detail: (id: number) => apiClient.get(`${TRANG_THAI_PHU_HUYNH_API_END_POINT}/${id}`),
    insert: (data: ts_phuhuynh_adm_status) => apiClient.post(`${TRANG_THAI_PHU_HUYNH_API_END_POINT}`, data),
    insertMultiple: (data: ITrangThaiPhuHuynhCoSoItemRequest) => apiClient.post(`${TRANG_THAI_PHU_HUYNH_API_END_POINT}/insert-multiple`, data),
    update: (data: ts_phuhuynh_adm_status) => apiClient.put(`${TRANG_THAI_PHU_HUYNH_API_END_POINT}`, data),
    delete: (id: number) => apiClient.delete(`${TRANG_THAI_PHU_HUYNH_API_END_POINT}/${id}`)
};