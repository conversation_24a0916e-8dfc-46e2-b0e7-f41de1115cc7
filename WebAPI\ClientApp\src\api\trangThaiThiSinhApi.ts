import { apiClient } from "./apiClient";
import { ex_trangthai_thisinh } from "../models/response/trang-thai-thi-sinh/ex_trangthai_thisinh";

export const TRANG_THAI_THI_SINH_API_END_POINT = "ex_trangthai_thisinh";

export const trangThaiThiSinhApi = {
    selectAll: () => apiClient.get(`${TRANG_THAI_THI_SINH_API_END_POINT}`),
    detail: (id: number) => apiClient.get(`${TRANG_THAI_THI_SINH_API_END_POINT}/${id}`),
    insert: (data: ex_trangthai_thisinh) => apiClient.post(`${TRANG_THAI_THI_SINH_API_END_POINT}`, data),
    update: (data: ex_trangthai_thisinh) => apiClient.put(`${TRANG_THAI_THI_SINH_API_END_POINT}`, data),
    delete: (id: number) => apiClient.delete(`${TRANG_THAI_THI_SINH_API_END_POINT}/${id}`),
};