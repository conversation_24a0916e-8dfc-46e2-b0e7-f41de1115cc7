import { ITruong } from '../models/response/truong/ITruong';
import { apiClient } from './apiClient';

export const TRUONG_API_END_POINT = "truong";

export const truongApi = {
    // L<PERSON>y tất cả trường
    selectAll: () => apiClient.get(`${TRUONG_API_END_POINT}`),

    // L<PERSON>y trường theo khối hệ
    selectTruongKhoiHe: () => apiClient.get(`${TRUONG_API_END_POINT}/khoi/he`),

    // L<PERSON>y khối theo trường
    selectKhoi: (dm_truong_id: number) => apiClient.get(`${TRUONG_API_END_POINT}/${dm_truong_id}/khoi`),

    select: (id: number) => apiClient.get(`${TRUONG_API_END_POINT}/${id}`),

    insert: (data: ITruong) => apiClient.post(`${TRUONG_API_END_POINT}`, data),
    update: (data: ITruong) => apiClient.put(`${TRUONG_API_END_POINT}`, data),
    delete: (id: number) => apiClient.delete(`${TRUONG_API_END_POINT}/${id}`),

};

