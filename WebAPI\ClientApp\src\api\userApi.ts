import { IUserLoadRequest } from "../models/request/user/IUserLoadRequest"
import { apiClient, formatQueryString } from "./apiClient"

export const USER_API_ENDPOINT = "user"
export const userApi = {
    getUsers: (request: IUserLoadRequest) => apiClient.get(`user?${formatQueryString(request)}`),
    getUser: (id: number) => apiClient.get(`user/${id}`),
    // getUserByEmail: (email: string) => apiClient.get(`user/email?email=${email}`),
    getUserByIds: (ids: number[]) => apiClient.post(`user/select-by-ids`, { ids: ids }),
    // getUsersInSchools: (request: IUserLoadRequest) => apiClient.get(`user/select-by-schools?${formatQueryString(request)}`),
    SelectTuVanVien: (dm_coso_id: number) => apiClient.get(`user/tu-van-vien/${dm_coso_id}`),
    SelectUserPheDuyet: (dm_coso_id: number) => apiClient.get(`user/phe-duyet/${dm_coso_id}`),
    SelectAllTuVanVien: () => apiClient.get(`user/all-tu-van-vien/`),

}