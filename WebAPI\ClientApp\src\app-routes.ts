import {
  AcceptanceFormPage,
  CskhPage,
  DanhSachThiPage,
  FoThucDonLayout,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ayout,
  HocSinhPhuHuynhPage,
  HomePage,
  KetQuaThiPage,
  Khoi,
  KhoiHePage,
  KyThi,
  KyThiMonThi,
  LoaiGiayTo,
  Lop,
  LopHanhChinhPage,
  MonThi,
  NguonBietToiLayout,
  NguonBietToiPage,
  NhapDiemThiPage,
  NoiTruLayout,
  PhongThiPage,
  PhuHuynhPage,
  SendEmailLayout,
  SucKhoeLayout,
  SurveyForm,
  SurveyLayout,
  ThangDiem,
  ThiSinh,
  ThongKePage,
  ThongTinCbnvPage,
  TongHopLayout,
  Truong,
  ThoiHocBaoLuu,
  MonThiDauVaoPage,
  ThiDauVaoPage,
  TrangThaiThiSinhPage
} from './pages';
import Faceid from './pages/face-id';
import TongHopGiayTo from './pages/tong-hop-giay-to';



const routes = [
  {
    path: "/home/<USER>",
    component: HomePage,
  },
  {
    path: "/dashboard",
    component: ThongKePage,
  },
  {
    path: "/ds_hoc_sinh_lop/:tab?",
    component: LopHanhChinhPage,
  },


  {
    path: "/crm_survey/form/:id",
    component: SurveyForm,
  },
  {
    path: "/crm_survey/:tab?",
    component: SurveyLayout,
  },

  {
    path: "/nguon_biet_toi/:tab?",
    component: NguonBietToiLayout,
  },
  {
    path: "/dm_trangthaihocsinh/:tab?",
    component: HocSinhPhuHuynhPage,
  },
  {
    path: "/loai_giay_to/:tab?",
    component: LoaiGiayTo,
  },
  {
    path: "/nguon_biet_toi/:tab?",
    component: NguonBietToiPage,
  },
  {
    path: "/send-email/:tab?",
    component: SendEmailLayout,
  },
  {
    path: "/hoc-sinh/:tab?",
    component: HocSinhLayout,
  },
  {
    path: "/food/:tab?",
    component: FoThucDonLayout,
  },
  {
    path: "/phu-huynh/:tab?",
    component: PhuHuynhPage,
  },
  {
    path: "/acceptance-form/:tab?",
    component: AcceptanceFormPage,
  },
  {
    path: "/truong/:tab?",
    component: Truong,
  },
  {
    path: "/he/:tab?",
    component: He,
  },
  {
    path: "/khoi/:tab?",
    component: Khoi,
  },
  {
    path: "/khoi_he/:tab?",
    component: KhoiHePage,
  },
  {
    path: "/lop/:tab?",
    component: Lop,
  },
  {
    path: "/suc-khoe/:tab?",
    component: SucKhoeLayout,
  },
  {
    path: "/thang-diem/:tab?",
    component: ThangDiem,
  },
  {
    path: "/mon-thi/:tab?",
    component: MonThi,
  },
  {
    path: '/ky-thi/:tab?',
    component: KyThi
  },
  {
    path: '/thongtin-cbnv/:tab?',
    component: ThongTinCbnvPage,
  },
  {
    path: "/ky-thi",
    component: KyThi,
  },
  {
    path: "kythi-monthi/:tab?",
    component: KyThiMonThi,
  },
  {
    path: "thi-sinh/:tab?",
    component: ThiSinh,
  },
  {
    path: "danh-sach-thi/:tab?",
    component: DanhSachThiPage,
  },
  {
    path: "phong-thi/:tab?",
    component: PhongThiPage,
  },
  {
    path: '/nhap-diem-thi/:tab?',
    component: NhapDiemThiPage
  },
  {
    path: '/ket-qua-thi/:tab?',
    component: KetQuaThiPage
  },
  {
    path: "/noi-tru/:tab?",
    component: NoiTruLayout,
  },
  {
    path: "/tong-hop/:tab?",
    component: TongHopLayout,
  },

  {
    path: "/tong-hop-giay-to/:tab?",
    component: TongHopGiayTo,
  },
  {
    path: "/face-id/:tab?",
    component: Faceid,
  },
  {
    path: "/cskh/:tab?",
    component: CskhPage,
  },
  {
    path: "/thong-ke",
    component: ThongKePage,
  },
  {
    path: "/thoi-hoc-bao-luu",
    component: ThoiHocBaoLuu,
  },
  {
    path: "/monthidauvao/:tab?",
    component: MonThiDauVaoPage,
  },
  {
    path: "/thidauvao/:tab?",
    component: ThiDauVaoPage,
  },
  {
    path: "/ex_trangthai_thisinh/:tab?",
    component: TrangThaiThiSinhPage,
  },
];

export default routes.map((route) => {
  return {
    ...route,
  };
});
