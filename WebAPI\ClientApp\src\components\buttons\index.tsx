import { I<PERSON><PERSON><PERSON>on, <PERSON><PERSON> as Primer<PERSON><PERSON>on, Tooltip } from '@primer/react';
import clsx from 'clsx';
import React, { useMemo } from 'react';
import { useDebounce } from 'use-debounce';
import TextTranslated from '../text';
import { useCommonContext } from '../../contexts/common';
type ButtonProps = {
    text?: string,
    icon?: string,
    isLoading?: boolean | false,
    isDisabled?: boolean | false,
    type?: "normal" | "primary" | "danger" | "success" | "invisible",
    style?: "text" | "filled" | "icon" | "hideText",
    onClick?: () => void,
    isSubmitButton?: boolean | false,
    id?: string,
    tootip?: string
}
const Button = (props: ButtonProps) => {
    const [isShowSpinner] = useDebounce<boolean>(props.isLoading || false, 100);
    const {translate} = useCommonContext();
    const Icon = () => {
        return (
            <i className={clsx(props.icon)}></i>
        );
    }
    const variant: 'default' | 'primary' | 'invisible' | 'danger' = useMemo(() => {
        if (props.type == "danger") return "danger"
        if (props.type == "success") return "primary"
        if (props.type == "invisible") return "invisible"
        return "default"
    }, [props.type])
    return (
        <React.Fragment>
            {(props.style == "text" || props.style == "filled" || !props.style) && props.tootip &&
                <Tooltip noDelay aria-label={props.tootip} direction='n'>
                    <PrimerButton variant={variant}
                        sx={{
                            display: "inline-block"
                        }}
                        leadingVisual={props.icon ? Icon : null}
                        disabled={props.isDisabled || props.isLoading}
                        size='small'
                        id={props.id}
                        onClick={() => {
                            if (props.onClick) {
                                props.onClick()
                            }
                        }}
                    >
                        <TextTranslated value={props.text ?? ""} />
                    </PrimerButton>
                </Tooltip>
            }
            {(props.style == "hideText") &&
                <Tooltip noDelay aria-label={translate(props.text??"")} direction='w'>
                    <PrimerButton variant={variant}
                        sx={{
                            display: "inline-block"
                        }}
                        leadingVisual={props.icon ? Icon : null}
                        disabled={props.isDisabled || props.isLoading}
                        size='small'
                        id={props.id}
                        type={props.isSubmitButton ? 'submit' : 'button'}
                        onClick={() => {
                            if (props.onClick) {
                                props.onClick()
                            }
                        }}
                    >

                    </PrimerButton>
                </Tooltip>
            }
            {(props.style == "text" || props.style == "filled" || !props.style) && !props.tootip &&
                <PrimerButton variant={variant}
                    sx={{
                        display: "inline-block"
                    }}
                    leadingVisual={props.icon ? Icon : null}
                    disabled={props.isDisabled || props.isLoading}
                    size='small'
                    type={props.isSubmitButton ? 'submit' : 'button'}
                    id={props.id}
                    onClick={() => {
                        if (props.onClick) {
                            props.onClick()
                        }
                    }}
                >
                    <TextTranslated value={props.text ?? ""} />
                </PrimerButton>
            }
            {(props.style == "icon") &&
                <Tooltip noDelay aria-label={props.text} direction='w'>
                    <IconButton
                        sx={{
                            display: "inline-block"
                        }}
                        variant={variant}
                        icon={Icon}
                        disabled={props.isDisabled || props.isLoading}
                        size='small'
                        aria-label={props.text ?? ""}
                        id={props.id}
                        type={props.isSubmitButton ? 'submit' : 'button'}
                        onClick={() => {
                            if (props.onClick) {
                                props.onClick()
                            }
                        }}
                    >
                        
                    </IconButton>
                </Tooltip>

            }
            {/* <button
                type={props.isSubmitButton ? "submit" : "button"}
                onClick={props.onClick}
                disabled={(props.isDisabled || props.isLoading) ? true : false}
                id={props.id}
                className={clsx(
                    styles.btn,
                    props.type == "primary" && styles.btn_primary,
                    props.type == "danger" && styles.btn_danger,
                    props.type == "success" && styles.btn_success,
                    (props.style == "text" || props.style == undefined) && styles.btn_text,
                    (props.isDisabled || props.isLoading) && styles.btn_disabled
                )}

            >
                {props.icon && !isShowSpinner && <i className={clsx(styles.icon, props.icon, "icon-active")}></i>}
                {isShowSpinner && <>{' '} <Spinner isBlack={!props.type || props.type === "normal"} /></>}
                <TextTranslated value={props.text ?? ""} />
            </button > */}

        </React.Fragment>
    );

}
export { Button };
