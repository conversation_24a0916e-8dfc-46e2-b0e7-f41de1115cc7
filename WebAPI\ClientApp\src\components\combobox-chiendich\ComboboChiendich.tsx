import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { chienDichApi } from "../../api/chienDichApi";
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
interface IComboboxChienDichProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
};
const ComboboxChienDich = (props: IComboboxChienDichProps) => {
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
    const [ChienDich, setChienDich] = useState<any[]>([]);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return ChienDich.map(x => ({ id: x.id, text: x.ten_chien_dich }))
    }, [ChienDich])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        handleGetChienDichAsync();
    }, [nam_hoc, dm_coso_id])

    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    const handleGetChienDichAsync = async () => {
        const res = await chienDichApi.load();
        if (res.is_success) {
            setChienDich(res.data.filter((x: any) => x.nam_hoc == nam_hoc && x.dm_coso_id == dm_coso_id))
        }
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button
                    trailingAction={TriangleDownIcon}
                    aria-labelledby={` ${ariaLabelledBy}`}
                    {...anchorProps}
                    sx={{
                        width: '100%',
                        '[data-component="buttonContent"]': {
                            display: 'flex',
                            justifyContent: 'flex-start',
                            gridTemplateAreas: 'none',
                            gridTemplateColumns: 'none',
                            '& > :first-child': {
                                flex: 1,
                                textAlign: 'left'
                            }
                        }
                    }}
                >
                    {children || translate("Chọn chiến dịch")}
                </Button>
            )}
            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />
    );
};

export default ComboboxChienDich;