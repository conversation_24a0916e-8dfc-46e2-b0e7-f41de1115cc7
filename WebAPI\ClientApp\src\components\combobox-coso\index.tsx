import { ActionList, ActionMenu } from "@primer/react";
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useCommonContext } from "../../contexts/common";
import { RootState } from "../../state/reducers";
import styles from "./ComboboxCoso.module.css";
import { actions } from "../../state/actions/actionsWrapper";
import { useAuth } from "../../hooks/useAuth";
export type ComboboxCoSoProps = {
    isReadonly?: boolean,
    value?: number,
    onValueChanged: (id: number) => void,
    className?: string,
    isShowClearButton?: boolean,
    preText?: string,
    stylingMode?: "outlined" | "filled" | "underlined"
}
const ComboboxCoSo = (props: ComboboxCoSoProps) => {
    const coSoAll = useSelector((state: RootState) => state.categorySource.dm_cosos);
    const { translate } = useCommonContext();
    const dispatch = useDispatch();
    const { user_info } = useAuth();
    var coSoIds = (user_info?.campus ?? []).map(x => x.dm_coso_id)
    const dm_cosos = useMemo(() => {
        return coSoAll.filter(c => coSoIds.includes(c.id))
    }, [coSoIds, coSoAll])
    const { language } = useSelector((x: RootState) => x.common)
    // console.log({
    //     dm_cosos, coSoIds, coSoAll
    // });

    useEffect(() => {
        if (dm_cosos.length === 0)
            dispatch(actions.categorySource.loadDmCoSoStart());
    }, []);
    const selectedData = useMemo(() => {
        if (props.value && dm_cosos) {
            return dm_cosos.find(x => x.id == props.value)
        }
        return undefined
    }, [dm_cosos, props.value])
    return (
        <div className={styles.container}>
            <ActionMenu>
                <ActionMenu.Button aria-label="Select campus">
                    {selectedData ? (language === "en" ? selectedData.ten_co_so_en : selectedData.ten_co_so) : translate("CoSoCombobox.PlaceHolder")}
                </ActionMenu.Button>
                <ActionMenu.Overlay width="auto">
                    <ActionList selectionVariant="single">
                        {props.isShowClearButton &&
                            <ActionList.Item key={0} selected={props.value != undefined && 0 === props.value}
                                onSelect={() => {
                                    props.onValueChanged(0)
                                }}
                            >
                                {language === "en" ? "Select" : "Chọn cơ sở"}
                            </ActionList.Item>
                        }
                        {dm_cosos && dm_cosos.map((item, index) => {
                            return (
                                <ActionList.Item key={item.id} selected={props.value != undefined && item.id === props.value}
                                    onSelect={() => {
                                        props.onValueChanged(item.id)
                                    }}
                                >
                                    {language === "en" ? item.ten_co_so_en : item.ten_co_so}
                                </ActionList.Item>
                            );
                        })}
                    </ActionList>

                </ActionMenu.Overlay>
            </ActionMenu >
        </div>
    );
}

export { ComboboxCoSo };

