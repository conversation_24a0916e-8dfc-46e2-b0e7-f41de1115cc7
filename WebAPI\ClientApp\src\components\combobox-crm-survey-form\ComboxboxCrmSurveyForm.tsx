import { TriangleDownIcon, XCircleFillIcon } from "@primer/octicons-react";
import { Box, Button, IconButton, SelectPanel } from "@primer/react";
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { apiWrapper } from '../../api/apiWrapper';
import { useCommonContext } from '../../contexts/common';
import { ICrmSurveyForm } from "../../models/response/crm-survey/ICrmSurveyForm";
import { RootState } from '../../state/reducers';
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { actions } from "../../state/actions/actionsWrapper";
type ComboboxCrmSurveyFormProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: ICrmSurveyForm) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
};
const ComboxboxCrmSurveyForm = (props: ComboboxCrmSurveyFormProps) => {
    const { language } = useSelector((x: RootState) => x.common);
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
    const { status, crmSurveyForms } = useSelector((state: RootState) => state.crmSurvey.crmSurveyForm);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(actions.crmSurveyFormWrapper.crmSurveyForm.LOAD_START(''));
    }, [dm_coso_id, nam_hoc]);
    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return crmSurveyForms.filter((x: any) => x.nam_hoc == nam_hoc && x.dm_coso_id == dm_coso_id).map(x => ({ id: x.id, text: x.name }))
    }, [crmSurveyForms, dm_coso_id, nam_hoc])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id == props.value)
    }, [dataSource, props.value])
    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                    {children || translate("CrmSurvyFormCombobox.PlaceHolder")}
                </Button>
            )}
            title={<Box sx={{ display: "flex", flexDirection: "row-reverse" }}>
                {props.isShowClearButton && props.value !== undefined && props.value > 0 &&
                    <IconButton aria-label={"Clear"} icon={XCircleFillIcon}
                        variant="invisible"
                        onClick={() => {
                            props.onValueChanged(0)
                        }}
                    ></IconButton>
                }
            </Box>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />

    );
};

export default ComboxboxCrmSurveyForm;
