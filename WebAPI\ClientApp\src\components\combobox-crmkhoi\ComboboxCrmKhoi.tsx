import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { khoiApi } from "../../api/khoiApi";

interface IComboboxCrmKhoiProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_khoi_id?: number;
    dm_truong_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
}

const ComboboxCrmKhoi = (props: IComboboxCrmKhoiProps) => {
    const [CrmKhoi, setCrmKhoi] = useState<any[]>([]);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();

    const dataSource = useMemo(() => {
        // Lọc theo trường trước khi map data
        const filteredData = props.dm_truong_id 
            ? CrmKhoi.filter(x => x.dm_truong_id === props.dm_truong_id) 
            : CrmKhoi;

        return filteredData.map(x => ({ id: x.id, text: x.ten_khoi }))
    }, [CrmKhoi, props.dm_truong_id])

    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])

    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        handleGetCrmKhoiAsync();
    }, []) // Chỉ gọi API lần đầu

    useEffect(() => {
        // Reset selected value khi đổi trường
        if (props.dm_truong_id && props.value) {
            const currentKhoi = CrmKhoi.find(x => x.id === props.value);
            if (!currentKhoi || currentKhoi.dm_truong_id !== props.dm_truong_id) {
                props.onValueChanged(0);
            }
        }
    }, [props.dm_truong_id])

    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }

    const handleGetCrmKhoiAsync = async () => {
        try {
            const res = await khoiApi.selectAll();
            if (res.is_success) {
                setCrmKhoi(res.data);
            }
        } catch (error) {
            console.error("Error fetching khoi data:", error);
        }
    }

    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button 
                    trailingAction={TriangleDownIcon} 
                    aria-labelledby={` ${ariaLabelledBy}`} 
                    {...anchorProps}
                    sx={{
                        width:'100%'
                    }}
                >
                    <p style={{ width: '100%', overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate("Chọn khối")}
                    </p>
                </Button>
            )}
            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />
    );
};

export default ComboboxCrmKhoi;