import { TriangleDownIcon, XCircleFillIcon } from "@primer/octicons-react";
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { crmLopApi } from "../../api/crmLopApi";

interface IComboboxCrmLopProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_khoi_id?: number;
    dm_truong_id?: number;
    dm_he_id?: number;
    nam_hoc?: string;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    ignore_id?: number;
}

const ComboboxCrmLop = (props: IComboboxCrmLopProps) => {
    const [CrmLop, setCrmLop] = useState<any[]>([]);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();

    const handleGetCrmLopAsync = async () => {
        const res = await crmLopApi.selectAll();
        if (res.is_success) {
            setCrmLop(res.data)
        }
    }

    useEffect(() => {
        handleGetCrmLopAsync();
    }, []); // Chỉ gọi API một lần

    const lopFilters = useMemo(() => {
        const ignore_id = props.ignore_id || 0;
        return CrmLop.filter((x) => {
            return x.id !== ignore_id && 
                   (!props.nam_hoc || x.nam_hoc === props.nam_hoc) &&
                   (!props.dm_truong_id || x.dm_truong_id === props.dm_truong_id) &&
                   (!props.dm_khoi_id || x.dm_khoi_id === props.dm_khoi_id) &&
                   (!props.dm_he_id || x.dm_he_id === props.dm_he_id);
        }).sort((a, b) => (a.ten_lop > b.ten_lop) ? 1 : -1);
    }, [CrmLop, props.dm_truong_id, props.dm_khoi_id, props.dm_he_id, props.nam_hoc, props.ignore_id]);

    const dataSource = useMemo(() => {
        return lopFilters.map(x => ({
            id: x.id,
            text: x.ten_lop
        }))
    }, [lopFilters])

    const filteredItems = useMemo(() => {
        return dataSource.filter(item => 
            item.text.toLowerCase().includes(filter.toLowerCase())
        )
    }, [dataSource, filter])

    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    // Reset selected value khi các điều kiện lọc thay đổi
    useEffect(() => {
        if (props.value) {
            const currentLop = CrmLop.find(x => x.id === props.value);
            if (!currentLop || 
                (props.nam_hoc && currentLop.nam_hoc !== props.nam_hoc) ||
                (props.dm_truong_id && currentLop.dm_truong_id !== props.dm_truong_id) ||
                (props.dm_khoi_id && currentLop.dm_khoi_id !== props.dm_khoi_id) ||
                (props.dm_he_id && currentLop.dm_he_id !== props.dm_he_id)) {
                props.onValueChanged(0);
            }
        }
    }, [props.nam_hoc, props.dm_truong_id, props.dm_khoi_id, props.dm_he_id]);

    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button 
                    trailingAction={TriangleDownIcon} 
                    aria-labelledby={` ${ariaLabelledBy}`} 
                    {...anchorProps}
                    sx={{
                        width: '100%',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis'
                    }}
                >
                    <p style={{ 
                        width: '100%', 
                        overflow: 'hidden', 
                        whiteSpace: 'nowrap', 
                        textOverflow: 'ellipsis' 
                    }}>
                        {children || translate("Chọn lớp")}
                    </p>
                </Button>
            )}
            title={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ flex: 1 }}>
                        {translate("Chọn lớp")}
                    </Box>
                    {props.isShowClearButton && (props.value ?? 0) > 0 && (
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant="invisible"
                            sx={{
                                color: 'danger.emphasis'
                            }}
                            onClick={() => {
                                props.onValueChanged(0);
                            }}
                        >
                            Bỏ chọn
                        </Button>
                    )}
                </Box>
            }
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={(data: any) => {
                if (data)
                    props.onValueChanged(data.id, CrmLop.find(x => x.id === data.id));
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'medium', height: 'medium' }}
        />
    );
};

export default ComboboxCrmLop;