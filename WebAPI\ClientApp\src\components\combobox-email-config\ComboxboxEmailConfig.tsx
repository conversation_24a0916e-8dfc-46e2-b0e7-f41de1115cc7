import { TriangleDownIcon, XCircleFillIcon } from "@primer/octicons-react";
import { Box, Button, IconButton, SelectPanel } from "@primer/react";
import { BetterSystemStyleObject } from "@primer/react/lib/sx";
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { ePageBaseStatus } from "../../models/ePageBaseStatus";
import { IDmEmailConfig } from "../../models/response/email/IDmEmailConfig";
import { actions } from "../../state/actions/actionsWrapper";
import { RootState } from '../../state/reducers';
type IComboxboxEmailConfigProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: IDmEmailConfig) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    placeHolder?: string;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    sx?: BetterSystemStyleObject;
    variant?: 'default' | 'primary' | 'invisible' | 'danger';
};
const ComboxboxEmailConfig = (props: IComboxboxEmailConfigProps) => {
    const { status, emailConfigs } = useSelector((state: RootState) => state.email.dmEmailConfig);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const dispatch = useDispatch();
    useEffect(() => {
        if (status === ePageBaseStatus.is_not_initialization || status === ePageBaseStatus.is_need_reload) {
            dispatch(actions.emailWrapper.dmEmailConfig.LOAD_START(undefined));
        }
    }, [status]);
    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return emailConfigs.map(x => ({ id: x.id, text: x.email_from_address }))
    }, [emailConfigs])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])
    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps} sx={props.sx} variant={props.variant ?? "default"}>
                    {children || (props.placeHolder ?? translate("ComboxboxEmailConfig.PlaceHolder"))}
                </Button>
            )}
            title={<Box sx={{ display: "flex", flexDirection: "row-reverse" }}>
                {props.isShowClearButton && props.value !== undefined && props.value > 0 &&
                    <IconButton aria-label={"Clear"} icon={XCircleFillIcon}
                        variant="invisible"
                        onClick={() => {
                            props.onValueChanged(0)
                        }}
                    ></IconButton>
                }
            </Box>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />

    );
};

export default ComboxboxEmailConfig;
