import { TriangleDownIcon, XCircleFillIcon } from "@primer/octicons-react";
import { Box, Button, IconButton, SelectPanel } from "@primer/react";
import { BetterSystemStyleObject } from "@primer/react/lib/sx";
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { ePageBaseStatus } from "../../models/ePageBaseStatus";
import { IEmailTemplate } from "../../models/response/email/IEmailTemplate";
import { actions } from "../../state/actions/actionsWrapper";
import { RootState } from '../../state/reducers';
type IComboxboxEmailTemplateProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: IEmailTemplate) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    placeHolder?: string;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    sx?: BetterSystemStyleObject;
    variant?: 'default' | 'primary' | 'invisible' | 'danger';
};
const ComboxboxEmailTemplate = (props: IComboxboxEmailTemplateProps) => {
    const { status, emailTemplates } = useSelector((state: RootState) => state.email.template);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const dispatch = useDispatch();
    useEffect(() => {
        if (status === ePageBaseStatus.is_not_initialization || status === ePageBaseStatus.is_need_reload) {
            dispatch(actions.emailWrapper.template.LOAD_START(undefined));
        }
    }, [status]);
    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return emailTemplates.map(x => ({ id: x.id, text: x.name }))
    }, [emailTemplates])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])
    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id, emailTemplates.find(x => x.id === selecteds.id))
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps} sx={props.sx} variant={props.variant ?? "default"}>
                    {children || (props.placeHolder ?? translate("ComboxboxEmailTemplate.PlaceHolder"))}
                </Button>
            )}
            title={<Box sx={{ display: "flex", flexDirection: "row-reverse" }}>
                {props.isShowClearButton && props.value !== undefined && props.value > 0 &&
                    <IconButton aria-label={"Clear"} icon={XCircleFillIcon}
                        variant="invisible"
                        onClick={() => {
                            props.onValueChanged(0)
                        }}
                    ></IconButton>
                }
            </Box>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />

    );
};

export default ComboxboxEmailTemplate;
