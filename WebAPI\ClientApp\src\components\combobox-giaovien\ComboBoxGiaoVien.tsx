import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { ePageBaseStatus } from '../../models/ePageBaseStatus';
import { sis_giaovien_view } from '../../models/response/giao-vien/sis_giaovien';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';

import { Box, Button, SelectPanel } from '@primer/react';
import { VariantType } from '@primer/react/lib/Button/types';
import Text from '../ui/text';
type IComboBoxGiaoVienProps = {
	dm_truong_id: number;
	isReadonly?: boolean;
	value: number;
	onValueChanged: (id: number, data?: sis_giaovien_view) => void;
	className?: string;
	isShowClearButton?: boolean;
	preText?: string;
	width?: string | number;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
	isShowClearBtn?: boolean,
	maxWidth?: any,
	variant?: VariantType
};
const getTrailingVisual = (giaoVien: sis_giaovien_view) => {
	return (
		<Text text={giaoVien.email} sx={{
			color: "fg.muted",
			fontSize: "12px"
		}} />
	);
}
const ComboBoxGiaoVien = (props: IComboBoxGiaoVienProps) => {
	const { translate } = useCommonContext();
	const dispatch = useDispatch();
	const { dm_truong_selected_id, dm_coso_selected_id } = useSelector((x: RootState) => x.common);
	const { status, sis_giaoviens } = useSelector((x: RootState) => x.giaoVien);

	useEffect(() => {
		if (status === ePageBaseStatus.is_not_initialization) {
			dispatch(actions.giaoVien.loadByTruongGiaoVienStart(dm_coso_selected_id));
		}
	}, [status]);

	const [open, setOpen] = useState(false)
	const [filter, setFilter] = useState('')
	const dataSource = useMemo(() => {
		return sis_giaoviens.map(x => {
			const item: any = {

				id: x.id,
				text: x.ho_ten,
				search: `${x.ho_ten} ${x.email} ${x.phone_number} ${x.ma_nv}`,
				trailingVisual: getTrailingVisual(x)
			}
			return item;
		})
	}, [sis_giaoviens, filter])
	const filterdData = useMemo(() => {
		return dataSource.filter(item =>
			item.search.toLowerCase().includes(filter.toLowerCase())
		)
	}, [dataSource, filter])
	const _selectedData = useMemo(() => {
		return dataSource.find(item => item.id === props.value)
	}, [props.value, dataSource])


	return (

		<SelectPanel
			renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
				<Button
					variant={props.variant}
					sx={{
						width: '100%'
					}} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
					<p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
						{children || translate(`ComboBoxGiaoVien.PlaceHolder`)}
					</p>
				</Button>
			)}
			title={<>
				<Box sx={{ display: "flex", alignItems: "center" }}>
					<Box sx={{ flex: 1 }}>
						{translate(`ComboBoxGiaoVien.PlaceHolder`)}
					</Box>
					{(props.isShowClearBtn === undefined || props.isShowClearBtn === true) && props.value > 0 &&
						<Button
							trailingVisual={XCircleFillIcon}
							variant='invisible'
							sx={{
								color: "danger.emphasis"
							}}
							onClick={() => {
								props.onValueChanged(0)
							}}
						>
							Bỏ chọn
						</Button>
					}
				</Box>
			</>}
			placeholderText="Search"
			open={open}
			onOpenChange={setOpen}
			items={filterdData}
			selected={_selectedData}
			onSelectedChange={(data: any) => {
				props.onValueChanged(data.id)
			}}
			onFilterChange={setFilter}
			showItemDividers={true}
			overlayProps={{ width: 'large', height: 'medium' }}
		/>

	);
};

export default ComboBoxGiaoVien;
