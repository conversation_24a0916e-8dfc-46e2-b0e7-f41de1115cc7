import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { ePageBaseStatus } from '../../models/ePageBaseStatus';
import { sis_giaovien_view } from '../../models/response/giao-vien/sis_giaovien';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';

import { Box, Button, Checkbox, FormControl, SelectPanel } from '@primer/react';
import Text from '../ui/text';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
type ISeletBoxGiaoVienMultipleProps = {
	dm_truong_id?: number;
	isReadonly?: boolean;
	value: number[];
	onValueChanged: (ids: number[]) => void;
	className?: string;
	isShowClearButton?: boolean;
	preText?: string;
	width?: string | number;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
	isShowClearBtn?: boolean,
	maxWidth?: any
};
const getTrailingVisual = (giaoVien: sis_giaovien_view) => {
	return (
		<Text text={giaoVien.email} sx={{
			color: "fg.muted",
			fontSize: "12px"
		}} />
	);
}
const SelectBoxGiaoVienMultiple = (props: ISeletBoxGiaoVienMultipleProps) => {
	const { translate } = useCommonContext();
	const dispatch = useDispatch();
	const { status, sis_giaoviens } = useSelector((x: RootState) => x.giaoVien);
	const { dm_truong_id, nam_hoc } = useCommonSelectedHook();

	useEffect(() => {
		if (status === ePageBaseStatus.is_not_initialization) {
			dispatch(actions.giaoVien.loadByTruongGiaoVienStart(props.dm_truong_id ?? dm_truong_id));
		}
	}, [status, props.dm_truong_id, dm_truong_id]);

	const [open, setOpen] = useState(false)
	const [filter, setFilter] = useState('')
	const dataSource = useMemo(() => {
		return sis_giaoviens.map(x => {
			const item: any = {

				id: x.id,
				text: x.ho_ten,
				search: `${x.ho_ten} ${x.email} ${x.phone_number} ${x.ma_nv}`,
				trailingVisual: getTrailingVisual(x)
			}
			return item;
		})
	}, [sis_giaoviens, filter])
	const filterdData = useMemo(() => {
		return dataSource.filter(item =>
			item.search.toLowerCase().includes(filter.toLowerCase())
		)
	}, [dataSource, filter])
	const _selectedDatas = useMemo(() => {
		return dataSource.filter(item => props.value.includes(item.id))
	}, [props.value, dataSource])

	const isSelectedAll = filterdData.length > 0 && filterdData.map(x => x.id).find(id => !props.value.includes(id)) === undefined;

	return (

		<SelectPanel
			renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
				<Button sx={{
					maxWidth: props.maxWidth ?? 300
				}} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
					<p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
						{children || translate(`ComboBoxGiaoVien.PlaceHolder`)}
					</p>
				</Button>
			)}
			title={<>
				<Box sx={{ display: "flex", alignItems: "center" }}>
					<Box sx={{ flex: 1 }}>
						<FormControl>
							<Checkbox checked={isSelectedAll} onChange={(e) => {
								if (e.target.checked) {
									props.onValueChanged(filterdData.map(x => x.id))
								} else {
									props.onValueChanged([])
								}
							}} />
							<FormControl.Label>Select all</FormControl.Label>
						</FormControl>
					</Box>
					{props.isShowClearBtn && props.value.length > 0 &&
						<Button
							trailingVisual={XCircleFillIcon}
							variant='invisible'
							sx={{
								color: "danger.emphasis"
							}}
							onClick={() => {
								props.onValueChanged([])
							}}
						>
							Bỏ chọn
						</Button>
					}
				</Box>
			</>}
			placeholderText="Search"
			open={open}
			loading={status === ePageBaseStatus.is_loading}
			onOpenChange={setOpen}
			items={filterdData}
			selected={_selectedDatas}
			onSelectedChange={(data: any) => {
				props.onValueChanged(data.map((x: any) => x.id))
			}}
			onFilterChange={setFilter}
			showItemDividers={true}
			overlayProps={{ width: 'large', height: 'medium' }}
		/>

	);
};

export default SelectBoxGiaoVienMultiple;
