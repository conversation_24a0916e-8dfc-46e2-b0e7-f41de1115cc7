import { ActionList, ActionMenu } from "@primer/react";

import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { dm_he } from '../../models/response/category/dm_he';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
type ComboboxHeProps = {
	isReadonly?: boolean;
	value?: number;
	onValueChanged: (id: number, data?: dm_he) => void;
	className?: string;
	isShowClearButton?: boolean;
	preText?: string;
	width?: string | number;
	dm_khoi_id?: number;
	dm_truong_id?: number;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
};
const ComboboxHe = (props: ComboboxHeProps) => {
	const { translate } = useCommonContext();
	const { language } = useSelector((x: RootState) => x.common);
	const { dm_coso_id } = useCommonSelectedHook();
	const dispatch = useDispatch();
	const categorySource = useSelector((state: RootState) => state.categorySource);

	useEffect(() => {
		if (categorySource.dm_hes.length === 0) dispatch(actions.categorySource.loadHeStart());
		if (categorySource.dm_truong_khoi_hes.length === 0) dispatch(actions.categorySource.loadDmTruongKhoiHeStart());
	}, [categorySource.dm_hes.length, categorySource.dm_truong_khoi_hes.length, dispatch, dm_coso_id]);

	const source: dm_he[] = useMemo(() => {
		// Start with base filter by dm_coso_id if available
		let filtered = categorySource.dm_hes
			.filter(x => !dm_coso_id || x.dm_coso_id === dm_coso_id);

		// If we have a khoi_id, filter by related hệ through dm_truong_khoi_he
		if (props.dm_khoi_id && props.dm_khoi_id > 0) {
			// Get all he_ids related to this khoi from dm_truong_khoi_he
			const relatedHeIds = categorySource.dm_truong_khoi_hes
				.filter(x => x.dm_khoi_id === props.dm_khoi_id)
				.map(x => x.dm_he_id);

			// Filter hệ by these ids
			if (relatedHeIds.length > 0) {
				filtered = filtered.filter(x => relatedHeIds.includes(x.id));
			}
		}

		return filtered.map(x => ({ ...x }));
	}, [categorySource.dm_hes, categorySource.dm_truong_khoi_hes, dm_coso_id, props.dm_khoi_id]);
	const selectedData = useMemo(() => {
		if (props.value && source) {
			return source.find(x => x.id === props.value)
		}
		return undefined
	}, [source, props.value])
	return (
		<ActionMenu>
			<ActionMenu.Button aria-label="Select school year" style={{
				width: '100%',
				display: 'flex',
				justifyContent: 'space-between'
			}}>
				{selectedData ? (language === "en" ? selectedData.ten_he_en : selectedData.ten_he) : translate("HeCombobox.PlaceHolder")}
			</ActionMenu.Button>
			<ActionMenu.Overlay width="auto">
				<ActionList selectionVariant="single">
					{props.isShowClearButton &&
						<ActionList.Item key={0} selected={props.value !== undefined && 0 === props.value}
							onSelect={() => {
								props.onValueChanged(0)
							}}
						>
							{language === "en" ? "Empty" : "Empty"}
						</ActionList.Item>
					}
					{source && source.map((item) => {
						return (
							<ActionList.Item key={item.id} selected={props.value !== undefined && item.id === props.value}
								onSelect={() => {
									props.onValueChanged(item.id)
								}}
							>
								{language === "en" ? item.ten_he_en : item.ten_he}
							</ActionList.Item>
						);
					})}
				</ActionList>

			</ActionMenu.Overlay>
		</ActionMenu >
		// <Select onChange={(e) => {
		// 	const dm_he_id: number = e.currentTarget.value ? parseInt(e.currentTarget.value) : 0
		// 	props.onValueChanged(dm_he_id, source.find(x => x.id == dm_he_id))
		// }}
		// 	value={`${props.value ?? 0}`}
		// 	placeholder={translate("HeCombobox.PlaceHolder")}
		// >
		// 	{source.map(x => {
		// 		return (
		// 			<Select.Option key={x.id} value={`${x.id}`}>{language === "en" ? x.ten_he_en : x.ten_he}</Select.Option>
		// 		);
		// 	})}
		// </Select>

	);
};
export default ComboboxHe;
