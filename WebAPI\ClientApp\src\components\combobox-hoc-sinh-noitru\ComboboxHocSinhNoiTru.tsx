import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { IHocSinhModel } from '../../models/response/hoc-sinh/IHocSinhModel';
import { RootState } from '../../state/reducers';
import { hocSinhNoiTruApi } from '../../api/hocSinhNoiTruApi';
import { lopApi } from '../../api/lopApi';

type ComboboxHocSinhNoiTruProps = {
  isReadonly?: boolean;
  value?: number;
  onValueChanged: (id: number, data?: IHocSinhModel) => void;
  className?: string;
  preText?: string;
  width?: string | number;
  dm_lop_id: number;
  dm_truong_id: number;
  dm_he_id: number;
  dm_khoi_id: number;
  stylingMode?: 'outlined' | 'filled' | 'underlined';
  isShowClearBtn?: boolean;
  maxWidth?: any;
};

const ComboboxHocSinhNoiTru = (props: ComboboxHocSinhNoiTruProps) => {
  const { translate } = useCommonContext();
  const [hocSinhs, setHocSinhs] = useState<IHocSinhModel[]>([]);
  const { nam_hoc } = useSelector((x: RootState) => x.common);
  const [filter, setFilter] = useState('');
  const [open, setOpen] = useState(false);

  const handleReloadHocSinh = async () => {
    if (props.dm_lop_id > 0) {
      const res = await lopApi.SelectHocSinhNoiTruByLopsAsync({
        nam_hoc: nam_hoc,
        dm_truong_id: props.dm_truong_id,
        dm_he_id: props.dm_he_id,
        dm_khoi_id: props.dm_khoi_id,
        dm_lop_ids: [props.dm_lop_id]
      });
      
      if (res.is_success) {
        setHocSinhs(res.data);
      } else {
        setHocSinhs([]);
      }
    } else {
      setHocSinhs([]);
    }
  };

  useEffect(() => {
    handleReloadHocSinh();
  }, [props.dm_lop_id, props.dm_truong_id, props.dm_he_id, props.dm_khoi_id]);

  const dataSource = useMemo(() => {
    return hocSinhs.map(x => ({
      id: x.id,
      text: x.ma_hs + ' - ' + x.ho_ten,
    }));
  }, [hocSinhs]);

  const filterdData = useMemo(() => {
    return dataSource.filter(item =>
      item.text.toLowerCase().includes(filter.toLowerCase())
    );
  }, [dataSource, filter]);

  const _selectedData = useMemo(() => {
    return dataSource.find(item => item.id === props.value);
  }, [props.value, dataSource]);

  return (
    <SelectPanel
      renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
        <Button 
          sx={{ 
            width: '100%', // Đặt chiều rộng 100%
            '& > div': { width: '100%' }, // Đảm bảo div con cũng 100%
            justifyContent: 'space-between', // Căn chỉnh nội dung
            textAlign: 'left' // Căn văn bản về bên trái
          }}
          trailingAction={TriangleDownIcon}
          aria-labelledby={` ${ariaLabelledBy}`}
          {...anchorProps}
          disabled={!props.dm_lop_id || props.dm_lop_id <= 0}
        >
          <Box sx={{ 
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            flex: 1 // Cho phép box này mở rộng tối đa
          }}>
            {children || translate(`Chọn học sinh`)}
          </Box>
        </Button>
      )}
      title={
        <Box sx={{ display: "flex", alignItems: "center", width: '100%' }}>
          <Box sx={{ flex: 1 }}>
            {translate(`Select a student`)}
          </Box>
          {props.isShowClearBtn && (props.value ?? 0) > 0 && (
            <Button
              trailingVisual={XCircleFillIcon}
              variant='invisible'
              sx={{ color: "danger.emphasis" }}
              onClick={() => props.onValueChanged(0)}
            >
              {translate("Clear")}
            </Button>
          )}
        </Box>
      }
      placeholderText="Search"
      open={open}
      onOpenChange={setOpen}
      items={filterdData}
      selected={_selectedData}
      onSelectedChange={(data: any) => {
        props.onValueChanged(data.id, hocSinhs.find(x => x.id === data.id));
      }}
      onFilterChange={setFilter}
      showItemDividers={true}
      overlayProps={{ 
        width: 'large', 
        height: 'medium',
        sx: { width: '100%' } // Đặt chiều rộng overlay 100%
      }}
    />
  );
};

export default ComboboxHocSinhNoiTru;