
import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { lopApi } from '../../api/lopApi';
import { useCommonContext } from '../../contexts/common';
import { IHocSinhModel } from '../../models/response/hoc-sinh/IHocSinhModel';
import { RootState } from '../../state/reducers';
type ComboboxHocSinhProps = {
	isReadonly?: boolean;
	value?: number;
	onValueChanged: (id: number, data?: IHocSinhModel) => void;
	className?: string;
	preText?: string;
	width?: string | number;
	dm_lop_id: number,
	stylingMode?: 'outlined' | 'filled' | 'underlined';
	isShowClearBtn?: boolean,
	maxWidth?: any
};
const ComboboxHocSinh = (props: ComboboxHocSinhProps) => {
	const { translate } = useCommonContext();
	const [hocSinhs, setHocSinhs] = useState<IHocSinhModel[]>([]);
	const { nam_hoc } = useSelector((x: RootState) => x.common);

	const handleReloadHocSinh = async () => {
		const res = await lopApi.SelectHocSinhByLopsAsync({
			nam_hoc: nam_hoc,
			dm_lop_id: props.dm_lop_id,
			dm_truong_id: 0,
			dm_he_id: 0,
			dm_khoi_id: 0
		});
		if (res.is_success) {
			setHocSinhs(res.data);
		}
	};

	useEffect(() => {
		handleReloadHocSinh();
	}, [props.dm_lop_id]);

	const [filter, setFilter] = useState('')
	const [open, setOpen] = useState(false)
	const dataSource = useMemo(() => {
		return hocSinhs.map(x => {
			const item: any = {
				id: x.id,
				text: x.ma_hs + ` - ` + x.ho_ten,
			}
			return item;
		})
	}, [hocSinhs, filter])
	const filterdData = useMemo(() => {
		return dataSource.filter(item =>
			item.text.toLowerCase().includes(filter.toLowerCase())
		)
	}, [dataSource, filter])
	const _selectedData = useMemo(() => {
		return dataSource.find(item => item.id === props.value)
	}, [props.value, dataSource])
	return (
		<SelectPanel
			renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
				<Button sx={{
					maxWidth: 300
				}} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
					<p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
						{children || translate(`Select a student`)}
					</p>
				</Button>
			)}
			title={<>
				<Box sx={{ display: "flex", alignItems: "center" }}>
					<Box sx={{ flex: 1 }}>
						{translate(`Select a student`)}
					</Box>
					{props.isShowClearBtn && (props.value ?? 0) > 0 &&
						<Button
							trailingVisual={XCircleFillIcon}
							variant='invisible'
							sx={{
								color: "danger.emphasis"
							}}
							onClick={() => {
								props.onValueChanged(0)
							}}
						>
							Bỏ chọn
						</Button>
					}
				</Box>
			</>}
			placeholderText="Search"
			open={open}
			onOpenChange={setOpen}
			items={filterdData}
			selected={_selectedData}
			onSelectedChange={(data: any) => {
				props.onValueChanged(data.id, hocSinhs.find(x => x.id === data.id))
			}}
			onFilterChange={setFilter}
			showItemDividers={true}
			overlayProps={{ width: 'large', height: 'medium' }}
		/>

	);
};

export default ComboboxHocSinh;
