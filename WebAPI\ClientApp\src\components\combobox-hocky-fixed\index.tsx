
import { ActionList, ActionMenu } from '@primer/react';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { RootState } from '../../state/reducers';

type IComboboxHocKyFixedProps = {
	isReadonly?: boolean;
	value?: number;
	onValueChanged: (id: number) => void;
	className?: string;
	isShowClearButton?: boolean;
	preText?: string;
	width?: string;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
};

export const ComboboxHocKyFixed = (props: IComboboxHocKyFixedProps) => {
	const { translate } = useCommonContext();
	const { language } = useSelector((x: RootState) => x.common)
	const dataSource =
		[
			{ id: 1, hoc_ky: `${translate('Base.Label.Semester')} 1`, hoc_ky_en: `1st Semester` },
			{ id: 2, hoc_ky: `${translate('Base.Label.Semester')} 2`, hoc_ky_en: `2st Semester` },
		];
	const selectedData = useMemo(() => {
		if (props.value) {
			return dataSource.find(x => x.id == props.value)
		}
		return undefined
	}, [dataSource, props.value])
	return (
		<ActionMenu>
			<ActionMenu.Button aria-label="Select school year">
				{selectedData ? selectedData.hoc_ky : translate("HocKySelectBox.PlaceHolder")}
			</ActionMenu.Button>
			<ActionMenu.Overlay width="auto">
				<ActionList selectionVariant="single">
					{dataSource.map((item, index) => {
						return (
							<ActionList.Item key={index} selected={props.value != undefined && item.id === props.value}
								onSelect={() => {
									props.onValueChanged(item.id)
								}}
							>
								{item.hoc_ky}
							</ActionList.Item>
						);
					})}
				</ActionList>

			</ActionMenu.Overlay>
		</ActionMenu >
		// <SelectBox
		// 	dataSource={[
		// 		{ id: 1, hoc_ky: `${translate('Base.Label.Semester')} 1`, hoc_ky_en: `1st Semester` },
		// 		{ id: 2, hoc_ky: `${translate('Base.Label.Semester')} 2`, hoc_ky_en: `2st Semester` },
		// 	]}
		// 	displayExpr={language == "en" ? "hoc_ky_en" : 'hoc_ky'}
		// 	valueExpr={'id'}
		// 	value={props.value}
		// 	stylingMode={props.stylingMode != undefined ? props.stylingMode : 'outlined'}
		// 	readOnly={props.isReadonly === true}
		// 	placeholder={translate('Chọn học kỳ')}
		// 	width={props.width ? props.width : '100%'}
		// 	className={'default_selectbox ' + (props.className ? props.className : '')}
		// 	showClearButton={props.isShowClearButton === true}
		// 	onValueChanged={(e) => {
		// 		if (e.event) props.onValueChanged(e.value);
		// 	}}
		// 	dropDownOptions={{
		// 		width: 150
		// 	}}
		// />
	);
};
