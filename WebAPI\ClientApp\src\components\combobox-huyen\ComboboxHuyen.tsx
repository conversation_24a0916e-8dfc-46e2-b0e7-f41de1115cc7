import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { tinhHuyenXaApi } from "../../api/tinhHuyenXaApi";
import { useCommonContext } from '../../contexts/common';
interface IComboboxHuyenProps {
    dm_tinh_id?: number;
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
};
const ComboboxHuyen = (props: IComboboxHuyenProps) => {
    const [Huyen, setHuyen] = useState<any[]>([]);

    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return Huyen.map(x => ({ id: x.id, text: x.ten_huyen }))
    }, [Huyen])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        if (props.dm_tinh_id !== undefined && props.dm_tinh_id > 0) {
            handleGetHuyenAsync();
        }
    }, [props.dm_tinh_id])

    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    const handleGetHuyenAsync = async () => {
        const res = await tinhHuyenXaApi.selectHuyenByTinh(props?.dm_tinh_id || 0);
        if (res.is_success) {
            setHuyen(res.data)
        }
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}
                    sx={{
                        width: '100%'
                    }}
                >
                    <p style={{ width: '100%', overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate("Chọn huyện")}
                    </p>
                    {/* {children || translate("KhoanNopCobobox.PlaceHolder")} */}
                </Button>
            )}

            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />

    );
};

export default ComboboxHuyen;