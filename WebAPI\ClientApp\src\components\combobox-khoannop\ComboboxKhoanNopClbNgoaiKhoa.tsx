import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { khoanNopApi } from "../../api/khoanNopApi";
import { useCommonContext } from '../../contexts/common';
interface IComboboxKhoanNopClbNgoaiKhoaProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_khoi_id?: number;
    dm_truong_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    // sx?: BetterSystemStyleObject,
    maxWidth?: any
};
const ComboboxKhoanNopClbNgoaiKhoa = (props: IComboboxKhoanNopClbNgoaiKhoaProps) => {
    const [khoanNops, setKhoanNops] = useState<any[]>([]);

    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return khoanNops.map(x => ({ id: x.id, text: x.ten_khoan_nop }))
    }, [khoanNops])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        handleGetKhoanNopsAsync();
    }, [])

    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    const handleGetKhoanNopsAsync = async () => {
        const res = await khoanNopApi.selectClbNgoaiKhoa();
        if (res.is_success) {
            setKhoanNops(res.data)
        }
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}
                    sx={{
                        maxWidth: props.maxWidth
                    }}
                >
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate("KhoanNopCobobox.PlaceHolder")}
                    </p>
                    {/* {children || translate("KhoanNopCobobox.PlaceHolder")} */}
                </Button>
            )}

            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />

    );
};

export default ComboboxKhoanNopClbNgoaiKhoa;