
import { ActionList, ActionMenu, Select } from '@primer/react';
import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { dm_khoi } from '../../models/response/category/dm_khoi';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
type ComboboxKhoiProps = {
	isReadonly?: boolean;
	value?: number;
	onValueChanged: (id: number, data?: dm_khoi) => void;
	className?: string;
	isShowClearButton?: boolean;
	preText?: string;
	width?: string | number;
	dm_truong_id?: number;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
};
const ComboboxKhoi = (props: ComboboxKhoiProps) => {
	const { dm_khois, dm_truong_khoi_hes } = useSelector((state: RootState) => state.categorySource);
	const { language } = useSelector((x: RootState) => x.common);
	const dispatch = useDispatch();
	useEffect(() => {
		if (dm_khois.length === 0) dispatch(actions.categorySource.loadKhoiStart());
	}, []);

	// Reset selected value when dm_truong_id changes
	useEffect(() => {
		if (props.value && props.dm_truong_id) {
			// Check if the currently selected khoi belongs to the selected truong
			const khoiBelongsToTruong = dm_khois.some(
				khoi => khoi.id === props.value && khoi.dm_truong_id === props.dm_truong_id
			);

			// If not, reset the selection
			if (!khoiBelongsToTruong && props.value !== 0) {
				props.onValueChanged(0);
			}
		}
	}, [props.dm_truong_id]);
	const { translate } = useCommonContext();
	const source: dm_khoi[] = useMemo(() => {
		if (props.dm_truong_id) {
			return dm_khois.filter((x) => x.dm_truong_id == props.dm_truong_id);
		}
		return dm_khois;
	}, [dm_khois, dm_truong_khoi_hes, props.dm_truong_id]);
	const selectedData = useMemo(() => {
		if (props.value && dm_khois) {
			return dm_khois.find(x => x.id == props.value)
		}
		return undefined
	}, [dm_khois, props.value])
	return (
		// <Select onChange={(e) => {
		// 	const dm_khoi_id: number = e.currentTarget.value ? parseInt(e.currentTarget.value) : 0
		// 	props.onValueChanged(dm_khoi_id, source.find(x => x.id == dm_khoi_id))

		// }}
		// 	value={`${props.value ?? 0}`}
		// 	width={'100%'}
		// 	placeholder={translate("KhoiCombobox.PlaceHolder")}
		// >
		// 	{source.map(x => {
		// 		return (
		// 			<Select.Option key={x.id} value={`${x.id}`}>{language === "en" ? x.ten_khoi_en : x.ten_khoi}</Select.Option>
		// 		);
		// 	})}
		// </Select>
		// <SelectBox
		// 	dataSource={source}
		// 	displayExpr={language === 'en' ? 'ten_khoi_en' : 'ten_khoi'}
		// 	valueExpr={'id'}
		// 	value={props.value}
		// 	searchEnabled
		// 	stylingMode={props.stylingMode || 'outlined'}
		// 	readOnly={props.isReadonly === true}
		// 	placeholder={translate('Base.Label.ChooseGrade')}
		// 	width={props.width ? props.width : '100%'}
		// 	className={'default_selectbox ' + (props.className ? props.className : '')}
		// 	showClearButton={props.isShowClearButton === true}
		// 	onValueChanged={(e) => {
		// 		if (e.event) {
		// 			// console.log({ e });
		// 			const data = source.find((x) => x.id == e.value);
		// 			props.onValueChanged(e.value, data);
		// 		}
		// 	}}
		// />
		<ActionMenu>
			<ActionMenu.Button
				aria-label="Select school year"
				style={{
					width: '100%',
					display: 'flex',
					justifyContent: 'space-between'
				}}
			>
				{selectedData ? (language === "en" ? selectedData.ten_khoi_en : selectedData.ten_khoi) : translate("KhoiCombobox.PlaceHolder")}
			</ActionMenu.Button>
			<ActionMenu.Overlay width="small">
				<ActionList selectionVariant="single">
					{props.isShowClearButton &&
						<ActionList.Item key={0} selected={props.value != undefined && 0 === props.value}
							onSelect={() => {
								props.onValueChanged(0)
							}}
						>
							{language === "en" ? "Select" : "Chọn khối"}
						</ActionList.Item>
					}
					{source && source.map((item, index) => {
						return (
							<ActionList.Item key={item.id} selected={props.value != undefined && item.id === props.value}
								onSelect={() => {
									props.onValueChanged(item.id)
								}}
							>
								{language === "en" ? item.ten_khoi_en : item.ten_khoi}
							</ActionList.Item>
						);
					})}
				</ActionList>
			</ActionMenu.Overlay>
		</ActionMenu>
	);
};

export default ComboboxKhoi;
