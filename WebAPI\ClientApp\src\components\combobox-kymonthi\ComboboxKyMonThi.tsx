import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { monThiApi } from "../../api/monThiApi";

interface IComboboxKyMonThiProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_khoi_id?: number;
    dm_truong_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    ex_kythi_id: number;
};

interface IKyMonThi {
    id: number;
    ten_ky_thi: string;
    mon_thi: string;
    ex_hinhthucthi: string;
}

const ComboboxKyMonThi = (props: IComboboxKyMonThiProps) => {
    const [kyMonThis, setKyMonThis] = useState<IKyMonThi[]>([]);
    const [open, setOpen] = useState(false);
    const [filter, setFilter] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const { translate } = useCommonContext();

    // Transform API data to dropdown items
    const dataSource = useMemo(() => {
        return kyMonThis.map(x => ({
            id: x.id,
            text: `${x.mon_thi} - ${x.ex_hinhthucthi}`
        }));
    }, [kyMonThis]);

    // Filter items based on search
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => 
            item.text.toLowerCase().includes(filter.toLowerCase())
        );
    }, [dataSource, filter]);

    // Find selected item
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value);
    }, [dataSource, props.value]);

    // Load data when ex_kythi_id changes
    useEffect(() => {
        const loadKyMonThis = async () => {
            if (!props.ex_kythi_id) {
                setKyMonThis([]);
                return;
            }

            setIsLoading(true);
            try {
                const res = await monThiApi.selectByKyThi(props.ex_kythi_id);
                if (res.is_success) {
                    setKyMonThis(res.data);
                } else {
                    setKyMonThis([]);
                }
            } catch (error) {
                console.error("Error loading ky mon thi:", error);
                setKyMonThis([]);
            } finally {
                setIsLoading(false);
            }
        };

        loadKyMonThis();
    }, [props.ex_kythi_id]);

    // Handle selection change
    const setSelected = (selected: any) => {
        if (selected) {
            const kyMonThi = kyMonThis.find(x => x.id === selected.id);
            props.onValueChanged(selected.id, kyMonThi);
        }
        setOpen(false);
    };

    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button 
                    trailingAction={TriangleDownIcon} 
                    aria-labelledby={`${ariaLabelledBy}`} 
                    {...anchorProps}
                    sx={{
                        width: props.width || '100%',
                        opacity: props.isReadonly ? 0.5 : 1,
                        cursor: props.isReadonly ? 'not-allowed' : 'pointer',
                        '[data-component="buttonContent"]': {
                            display: 'flex',
                            justifyContent: 'flex-start',
                            gridTemplateAreas: 'none',
                            gridTemplateColumns: 'none',
                            '& > :first-child': {
                                flex: 1,
                                textAlign: 'left'
                            }
                        }
                    }}
                    disabled={props.isReadonly || isLoading}
                >
                    <p style={{
                        width: '100%',
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        margin: 0
                    }}>
                        {selected?.text || (isLoading ? translate("Loading...") : translate("Chọn môn thi"))}
                    </p>
                </Button>
            )}
            title=""
            placeholderText={translate("Tìm kiếm môn thi...")}
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{
                width: 'small',
                height: 'medium'
            }}
        />
    );
};

export default ComboboxKyMonThi;