import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from "@primer/react";
import { useEffect, useMemo, useState } from "react";
import { useCommonContext } from "../../contexts/common";
import { kyThiApi } from "../../api/kyThiApi";
import { useSelector } from "react-redux";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";

interface IComboboxKyThiProps {
  isReadonly?: boolean;
  value?: number;
  onValueChanged: (id: number, data: any) => void;
  className?: string;
  isShowClearButton?: boolean;
  preText?: string;
  width?: string | number;
  dm_khoi_id?: number;
  dm_truong_id?: number;
  stylingMode?: "outlined" | "filled" | "underlined";
}

interface IKyThi {
  id: number;
  ten_ky_thi: string;
  ten_he: string;
  ten_khoi: string;
  da_ket_thuc: boolean;
  dm_truong_id: number;
  nam_hoc: string;
}

const ComboboxKyThi = (props: IComboboxKyThiProps) => {
  const [kyThis, setKyThis] = useState<IKyThi[]>([]);
  const [open, setOpen] = useState(false);
  const [filter, setFilter] = useState("");
  const { translate } = useCommonContext();
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

  const dataSource = useMemo(() => {
    let filtered = kyThis;

    if (props.dm_truong_id) {
      filtered = filtered.filter(
        (x) => x.dm_truong_id === props.dm_truong_id && x.nam_hoc === nam_hoc
      );
    }

    const mappedData = filtered.map((x) => ({
      id: x.id,
      text: `${x.ten_ky_thi} - ${x.ten_he} - ${x.ten_khoi}`,
      da_ket_thuc: x.da_ket_thuc,
    }));
    console.log("Mapped Ky Thi Data:", mappedData);
    return mappedData;
  }, [kyThis, props.dm_truong_id, nam_hoc]);

  const filteredItems = useMemo(() => {
    const items = dataSource.filter((item) =>
      item.text.toLowerCase().includes(filter.toLowerCase())
    );
    //console.log("Filtered Items:", items);
    return items;
  }, [dataSource, filter]);

  const selected = useMemo(() => {
    const selectedItem = dataSource.find((x) => x.id === props.value);
    console.log("Selected Item:", selectedItem);
    return selectedItem;
  }, [dataSource, props.value]);

  useEffect(() => {
    loadKyThis();
  }, [props.dm_truong_id, nam_hoc]);

  const setSelected = (selected: any) => {
    if (selected) {
      const kyThi = kyThis.find((x) => x.id === selected.id);
      //console.log("Selected Ky Thi:", kyThi);
      props.onValueChanged(selected.id, kyThi);
    }
  };

  const loadKyThis = async () => {
    if (!props.dm_truong_id) {
      setKyThis([]);
      return;
    }

    try {
      const res = await kyThiApi.selectByTruong(props.dm_truong_id);
      console.log("API Response:", res);
      if (res.is_success) {
        //console.log("Raw Ky Thi Data:", res.data);
        setKyThis(res.data);
      }
    } catch (error) {
      //console.error("Error loading ky thi:", error);
      setKyThis([]);
    }
  };

  return (
    <SelectPanel
      renderAnchor={({
        children,
        "aria-labelledby": ariaLabelledBy,
        ...anchorProps
      }) => (
        <Button
          trailingAction={TriangleDownIcon}
          aria-labelledby={`${ariaLabelledBy}`}
          {...anchorProps}
          sx={{
            width: props.width || "100%",
            opacity: props.dm_truong_id ? 1 : 0.5,
            cursor: props.dm_truong_id ? "pointer" : "not-allowed",
          }}
          disabled={!props.dm_truong_id || props.isReadonly}
        >
          <p
            style={{
              width: "100%",
              overflow: "hidden",
              textOverflow: "ellipsis",
              margin: 0,
            }}
          >
            {children || translate("Chọn kỳ thi")}
          </p>
        </Button>
      )}
      title=""
      placeholderText="Tìm kiếm kỳ thi..."
      open={open}
      onOpenChange={setOpen}
      items={filteredItems}
      selected={selected}
      onSelectedChange={setSelected}
      onFilterChange={setFilter}
      showItemDividers={true}
      overlayProps={{
        width: "small",
        height: "medium",
      }}
    />
  );
};

export default ComboboxKyThi;