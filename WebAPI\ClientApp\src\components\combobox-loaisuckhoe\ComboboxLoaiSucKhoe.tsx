import { ActionList, ActionMenu } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { dm_loaisuckhoe } from "../../models/response/category/dm_loaisuckhoe";
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
type ComboboxLoaiSucKhoeProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: dm_loaisuckhoe) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
};
export const ComboboxLoaiSucKhoe = (props: ComboboxLoaiSucKhoeProps) => {
    const { status, loaiSucKhoes } = useSelector((state: RootState) => state.sucKhoe.loaiSucKhoe);
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
    const { language } = useSelector((x: RootState) => x.common);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(actions.sucKhoe.loaiSucKhoe.LOAD_START(''));
    }, [dm_coso_id, nam_hoc]);
    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return loaiSucKhoes.filter(x => x.dm_coso_id === dm_coso_id && x.nam_hoc === nam_hoc).map(x => ({ id: x.id, text: x.name }))
    }, [loaiSucKhoes, dm_coso_id, nam_hoc])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selectedData = useMemo(() => {
        if (props.value && dataSource) {
            return dataSource.find(x => x.id === props.value)
        }
        return undefined
    }, [dataSource, props.value])
    return (
        <div style={{ width: '100%' }}>
            <ActionMenu>
                <ActionMenu.Button
                    aria-label="Chọn loại sức khỏe"
                    style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between'
                    }}
                >
                    {selectedData ? selectedData.text : translate("Chọn loại sức khỏe")}
                </ActionMenu.Button>
                <ActionMenu.Overlay width="small">
                    <ActionList selectionVariant="single">
                        {props.isShowClearButton &&
                            <ActionList.Item key={0} selected={props.value !== undefined && 0 === props.value}
                                onSelect={() => {
                                    props.onValueChanged(0)
                                }}
                            >
                                {"Chọn loại sức khỏe"}
                            </ActionList.Item>
                        }
                        {dataSource && dataSource.map((item, index) => {
                            return (
                                <ActionList.Item key={item.id} selected={props.value != undefined && item.id === props.value}
                                    onSelect={() => {
                                        props.onValueChanged(item.id)
                                    }}
                                >
                                    {item.text}
                                </ActionList.Item>
                            );
                        })}
                    </ActionList>
                </ActionMenu.Overlay>
            </ActionMenu>
        </div>
    );
};
