
import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { lopApi } from '../../api/lopApi';
import { useCommonContext } from '../../contexts/common';
import { dm_lop_viewmodel } from '../../models/response/dm-lop/dm_lop';
type ComboboxLopProps = {
	isReadonly?: boolean;
	value?: number;
	ignore_id?: number;
	onValueChanged: (id: number, data?: dm_lop_viewmodel) => void;
	className?: string;
	preText?: string;
	width?: string | number;
	dm_khoi_id?: number;
	dm_truong_id?: number;
	dm_he_id?: number;
	nam_hoc?: string;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
	isShowClearBtn?: boolean,
	maxWidth?: any
};
const ComboboxLop = (props: ComboboxLopProps) => {
	const { translate } = useCommonContext();
	const [lops, setLops] = useState<dm_lop_viewmodel[]>([]);
	const handleReloadLops = async () => {
		const res = await lopApi.Select({
			nam_hoc: props.nam_hoc || '',
			dm_he_id: props.dm_he_id || 0,
			dm_khoi_id: props.dm_khoi_id || 0,
			dm_truong_id: props.dm_truong_id || 0,
		});
		if (res.is_success) {
			setLops(res.data);
		}
	};

	useEffect(() => {
		handleReloadLops();
	}, [props.nam_hoc, props.dm_truong_id, props.dm_khoi_id, props.dm_khoi_id, props.dm_he_id]);

	const lopFilters: dm_lop_viewmodel[] = useMemo(() => {
		const ignore_id = props.ignore_id || 0;
		return lops && props.nam_hoc && props.dm_truong_id
			? lops.filter((x) => x.id !== ignore_id && x.nam_hoc === props.nam_hoc && x.dm_truong_id === (props.dm_truong_id ?? 0)).sort((a, b) => (a.ten_lop > b.ten_lop) ? 1 : -1)
			: [];
	}, [lops, props.dm_truong_id, props.nam_hoc, props.ignore_id]);
	const [filter, setFilter] = useState('')
	const [open, setOpen] = useState(false)
	const dataSource = useMemo(() => {
		return lops.map(x => {
			const item: any = {
				id: x.id,
				text: x.ten_lop,
			}
			return item;
		})
	}, [lopFilters, filter])
	const filterdData = useMemo(() => {
		return dataSource.filter(item =>
			item.text.toLowerCase().includes(filter.toLowerCase())
		)
	}, [dataSource, filter])
	const _selectedData = useMemo(() => {
		return dataSource.find(item => item.id === props.value)
	}, [props.value, dataSource])
	return (
		<SelectPanel
			renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
				<Button
					sx={{
						width: '100%', 
						overflow: 'hidden', 
						whiteSpace: 'nowrap', 
						textOverflow: 'ellipsis' 
					}}
					trailingAction={TriangleDownIcon}
					aria-labelledby={` ${ariaLabelledBy}`}
					{...anchorProps}
				>
					<p
						style={{
							width: '100%',
							overflow: 'hidden',
							whiteSpace: 'nowrap',
							textOverflow: 'ellipsis'
						}}
					>
						{children || translate(`ComboboxLop.PlaceHolder`)}
					</p>
				</Button>
			)}
			title={
				<>
					<Box sx={{ display: 'flex', alignItems: 'center' }}>
						<Box sx={{ flex: 1 }}>
							{translate(`ComboboxLop.PlaceHolder`)}
						</Box>
						{props.isShowClearBtn && (props.value ?? 0) > 0 && (
							<Button
								trailingVisual={XCircleFillIcon}
								variant="invisible"
								sx={{
									color: 'danger.emphasis'
								}}
								onClick={() => {
									props.onValueChanged(0);
								}}
							>
								Bỏ chọn
							</Button>
						)}
					</Box>
				</>
			}
			placeholderText="Search"
			open={open}
			onOpenChange={setOpen}
			items={filterdData}
			selected={_selectedData}
			onSelectedChange={(data: any) => {
				if (data)
					props.onValueChanged(data.id, lops.find(x => x.id === data.id));
			}}
			onFilterChange={setFilter}
			showItemDividers={true}
			overlayProps={{
				width: 'medium',
				height: 'medium'
			}}
		/>


	);
};

export default ComboboxLop;
