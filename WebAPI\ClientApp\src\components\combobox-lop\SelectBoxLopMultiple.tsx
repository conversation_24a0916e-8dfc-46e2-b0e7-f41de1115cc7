
import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, Checkbox, FormControl, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { dm_lop, dm_lop_viewmodel } from '../../models/response/dm-lop/dm_lop';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
type ISelectBoxLopMultipleProps = {
	isReadonly?: boolean;
	ignore_id?: number,
	value: number[];
	onValueChanged: (id: number[], lops?: dm_lop[]) => void;
	className?: string;
	preText?: string;
	width?: string | number;
	dm_khoi_id?: number;
	dm_truong_id?: number;
	dm_he_id?: number;
	nam_hoc?: string;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
	isShowClearBtn?: boolean,
	maxWidth?: any
};
const SelectBoxLopMultiple = (props: ISelectBoxLopMultipleProps) => {
	const { translate } = useCommonContext();
	const { nam_hoc, dm_truong_id } = useCommonSelectedHook();
	const dispatch = useDispatch();
	const { status: lopStatus, dm_lops } = useSelector((x: RootState) => x.danhMucWrapper.lop);

	useEffect(() => {
		dispatch(actions.danhMucWrapper.lop.loadStart(nam_hoc, dm_truong_id));
	}, [nam_hoc, dm_truong_id])


	const lopFilters: dm_lop_viewmodel[] = useMemo(() => {
		const ignore_id = props.ignore_id ?? 0;
		return dm_lops && props.nam_hoc && props.dm_truong_id
			? dm_lops.filter((x) => x.id !== ignore_id && x.nam_hoc === props.nam_hoc && x.dm_truong_id === (props.dm_truong_id ?? 0)).sort((a, b) => (a.ten_lop > b.ten_lop) ? 1 : -1)
			: [];
	}, [dm_lops, props.dm_truong_id, props.nam_hoc, props.ignore_id]);
	const [filter, setFilter] = useState('')
	const [open, setOpen] = useState(false)
	const dataSource = useMemo(() => {
		return dm_lops.map(x => {
			const item: any = {
				id: x.id,
				text: x.ten_lop,
			}
			return item;
		})
	}, [lopFilters, filter])
	const filterdData = useMemo(() => {
		return dataSource.filter(item =>
			item.text.toLowerCase().includes(filter.toLowerCase())
		)
	}, [dataSource, filter])
	const _selectedDatas = useMemo(() => {
		// console.log({
		// 	values: props.value
		// });

		return dataSource.filter(item => props.value && props.value.includes(item.id))
	}, [props.value, dataSource])
	const isSelectedAll = filterdData.length > 0 && filterdData.map(x => x.id).find(id => !props.value.includes(id)) === undefined;
	return (
		<SelectPanel
			renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
				<Button sx={{
					maxWidth: 300
				}} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
					<p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
						{children || translate(`ComboboxLop.PlaceHolder`)}
					</p>
				</Button>
			)}
			title={<>
				<Box sx={{ display: "flex", alignItems: "center" }}>
					<Box sx={{ flex: 1 }}>
						<FormControl>
							<Checkbox checked={isSelectedAll} onChange={(e) => {
								if (e.target.checked) {
									props.onValueChanged(filterdData.map(x => x.id), dm_lops)
								} else {
									props.onValueChanged([])
								}
							}} />
							<FormControl.Label>Select all</FormControl.Label>
						</FormControl>
					</Box>
					{props.isShowClearBtn && props.value.length > 0 &&
						<Button
							trailingVisual={XCircleFillIcon}
							variant='invisible'
							sx={{
								color: "danger.emphasis"
							}}
							onClick={() => {
								props.onValueChanged([])
							}}
						>
							Bỏ chọn
						</Button>
					}
				</Box>
			</>}
			placeholderText="Search"
			open={open}
			onOpenChange={setOpen}
			items={filterdData}
			selected={_selectedDatas}
			onSelectedChange={(data: any) => {
				const ids: number[] = data.map((x: any) => x.id);
				props.onValueChanged(ids, dm_lops.filter(x => ids.includes(x.id)))
			}}
			onFilterChange={setFilter}
			showItemDividers={true}
			overlayProps={{ width: 'medium', height: 'medium' }}
		/>

	);
};

export default SelectBoxLopMultiple;
