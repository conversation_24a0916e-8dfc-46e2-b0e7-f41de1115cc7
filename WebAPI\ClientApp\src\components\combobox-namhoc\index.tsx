import { <PERSON>List, ActionMenu } from "@primer/react";
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useCommonContext } from "../../contexts/common";
import { actions } from "../../state/actions/actionsWrapper";
import { RootState } from "../../state/reducers";
type ComboboxNamHocProps = {
  isReadonly?: boolean;
  value?: string;
  onValueChanged: (id: string) => void;
  className?: string;
  isShowClearButton?: boolean;
  preText?: string;
  width?: string;
  stylingMode?: "outlined" | "filled" | "underlined";
};
export const ComboboxNamHoc = (props: ComboboxNamHocProps) => {
  const dm_namhocs = useSelector(
    (state: RootState) => state.categorySource.dm_namhocs
  );
  let stylingMode = "outlined";
  if (props.stylingMode) stylingMode = props.stylingMode;
  const dispatch = useDispatch();
  useEffect(() => {
    if (dm_namhocs.length === 0)
      dispatch(actions.categorySource.loadNamHocStart());
  }, []);
  const { translate } = useCommonContext();
  const dataSource = useMemo(() => {
    return dm_namhocs.map(x => ({ ...x, text: `${props.preText ?? ""}${x.nam_hoc}` }))
  }, [props.preText, dm_namhocs])
  const selectedData = useMemo(() => {
    if (props.value) {
      return dataSource.find(x => x.nam_hoc.toString() == props.value)
    }
    return undefined
  }, [dataSource, props.value])
  return (

    <ActionMenu>
      <ActionMenu.Button style={{ width: "100%" }} aria-label="Select school year">
        {selectedData ? selectedData.text : translate("NamHocSelecteBox.PlaceHolder")}
      </ActionMenu.Button>
      <ActionMenu.Overlay width="small">
        <ActionList selectionVariant="single">
          {dataSource.map((item, index) => {
            return (
              <ActionList.Item key={item.nam_hoc} selected={props.value != undefined && item.nam_hoc.toString() === props.value}
                onSelect={() => {
                  if (!props.isReadonly) {
                    props.onValueChanged(item.nam_hoc)
                  }
                }}
              >
                {item.text}
              </ActionList.Item>
            );
          })}
        </ActionList>

      </ActionMenu.Overlay>
    </ActionMenu >
  );
};
