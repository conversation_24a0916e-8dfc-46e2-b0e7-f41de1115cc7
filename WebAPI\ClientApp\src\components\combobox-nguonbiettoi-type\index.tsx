import { TriangleDownIcon,XCircleFillIcon } from "@primer/octicons-react";
import { Box, Button, IconButton, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { IDmNguonBietToiType } from '../../models/response/category/IDmNguonBietToiType';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
type ComboboxNguonBietToiTypeProps = {
	isReadonly?: boolean;
	value?: number;
	onValueChanged: (id: number, data?: IDmNguonBietToiType) => void;
	className?: string;
	isShowClearButton?: boolean;
	preText?: string;
	width?: string | number;
	dm_khoi_id?: number;
	dm_truong_id?: number;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
};
export const ComboboxNguonBietToiType = (props: ComboboxNguonBietToiTypeProps) => {
	const { status, nguonBietToiTypes } = useSelector((state: RootState) => state.crmCategoryWrapper.nguonBietToiType);
	const { language } = useSelector((x: RootState) => x.common);
	const [open, setOpen] = useState(false)
	const [filter, setFilter] = useState('')
	const dispatch = useDispatch();
	useEffect(() => {
		dispatch(actions.crmCategoryWrapper.nguonBietToiType.LOAD_START(''));
	}, []);
	const { translate } = useCommonContext();
	const dataSource = useMemo(() => {
		return nguonBietToiTypes.map(x => ({ id: x.id, text: x.name }))
	}, [nguonBietToiTypes])
	const filteredItems = useMemo(() => {
		return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
	}, [dataSource,filter])
	const selected = useMemo(() => {
		return dataSource.find(x => x.id == props.value)
	}, [dataSource, props.value])
	const setSelected = (selecteds: any) => {
		if (selecteds)
			props.onValueChanged(selecteds.id)
	}
	return (
		<SelectPanel
			renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
				<Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
					{children || translate("NguonBietToiTypeCombobox.PlaceHolder")}
				</Button>
			)}
			title={<Box sx={{ display: "flex", flexDirection: "row-reverse" }}>
				{props.isShowClearButton && props.value !== undefined && props.value > 0 &&
					<IconButton aria-label={"Clear"} icon={XCircleFillIcon}
						variant="invisible"
						onClick={() => {
							props.onValueChanged(0)
						}}
					></IconButton>
				}
			</Box>}
			placeholderText="Search"
			open={open}
			onOpenChange={setOpen}
			items={filteredItems}
			selected={selected}
			onSelectedChange={setSelected}
			onFilterChange={setFilter}
			showItemDividers={true}
			overlayProps={{ width: 'auto', height: 'medium' }}
		/>
		
	);
};
