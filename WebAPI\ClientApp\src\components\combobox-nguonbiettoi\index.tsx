import { TriangleDownIcon, XCircleFillIcon } from "@primer/octicons-react";
import { Box, Button, IconButton, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { IDmNguonBietToi } from '../../models/response/category/IDmNguonBietToi';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
type ComboboxNguonBietToiProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: IDmNguonBietToi) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
};
export const ComboboxNguonBietToi = (props: ComboboxNguonBietToiProps) => {
    const {dm_coso_id } = useCommonSelectedHook();
    const { status, nguonBietTois } = useSelector((state: RootState) => state.crmCategoryWrapper.nguonBietToi);
    const { language } = useSelector((x: RootState) => x.common);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(actions.crmCategoryWrapper.nguonBietToi.LOAD_START(''));
    }, [dm_coso_id]);
    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        if (dm_coso_id == 0) {
            return nguonBietTois.map(x => ({ id: x.id, text: x.name }))
        }
        else {
            return nguonBietTois.filter(x => x.dm_coso_id == dm_coso_id).map(x => ({ id: x.id, text: x.name }))

        }
    }, [nguonBietTois, dm_coso_id])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id == props.value)
    }, [dataSource, props.value])
    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    return (
        <SelectPanel
        sx={{ width: '100%' }}

            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}
                sx={{
                    width: '100%',
                    justifyContent: 'space-between'
                }}>
                    {children || translate("NguonBietToiCombobox.PlaceHolder")}
                </Button>
            )}
            title={<Box sx={{ width: '100%', display: "flex", flexDirection: "row-reverse" }}>
                {props.isShowClearButton && props.value !== undefined && props.value > 0 &&
                    <IconButton aria-label={"Clear"} icon={XCircleFillIcon}
                        variant="invisible"
                        onClick={() => {
                            props.onValueChanged(0)
                        }}
                    ></IconButton>
                }
            </Box>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />

    );
};
