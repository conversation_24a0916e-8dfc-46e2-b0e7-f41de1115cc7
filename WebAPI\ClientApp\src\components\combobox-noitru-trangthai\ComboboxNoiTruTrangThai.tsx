import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { hocSinhNoiTruStatusApi } from "../../api/hocSinhNoiTruApi";

interface IComboboxNoiTruTrangThaiProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    showAllOption?: boolean;
    allOptionText?: string;
};

const ComboboxNoiTruTrangThai = (props: IComboboxNoiTruTrangThaiProps) => {
    const [NoiTruTrangThai, setNoiTruTrangThai] = useState<any[]>([]);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const { translate } = useCommonContext();
    
    // Define a special ID for the "All" option
    const ALL_OPTION_ID = -999;
    const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
    
    const dataSource = useMemo(() => {
        let items = NoiTruTrangThai.map(x => ({ id: x.id, text: x.trang_thai }));
        
        if (props.showAllOption) {
            items = [{ id: ALL_OPTION_ID, text: props.allOptionText || translate("Tất cả") }, ...items];
        }
        
        return items;
    }, [NoiTruTrangThai, props.showAllOption, props.allOptionText]);
    
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    
    const selected = useMemo(() => {
        if (isAllSelected) {
            return dataSource.find(x => x.id === ALL_OPTION_ID);
        }
        return dataSource.find(x => x.id === props.value);
    }, [dataSource, props.value, isAllSelected]);
    
    useEffect(() => {
        handleGetNoiTruTrangThaiAsync();
    }, [])
    
    const setSelected = (selecteds: any) => {
        if (selecteds) {
            if (selecteds.id === ALL_OPTION_ID) {
                setIsAllSelected(true);
                props.onValueChanged(0, { isAllOption: true });
            } else {
                setIsAllSelected(false);
                props.onValueChanged(selecteds.id);
            }
        }
    }
    
    const handleGetNoiTruTrangThaiAsync = async () => {
        const res = await hocSinhNoiTruStatusApi.selectAll();
        if (res.is_success) {
            setNoiTruTrangThai(res.data)
        }
    }
    
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button 
                    trailingAction={TriangleDownIcon} 
                    aria-labelledby={` ${ariaLabelledBy}`} 
                    {...anchorProps}
                    sx={{
                        width: '100%',
                        '[data-component="buttonContent"]': {
                            display: 'flex',
                            justifyContent: 'flex-start',
                            gridTemplateAreas: 'none',
                            gridTemplateColumns: 'none',
                            '& > :first-child': {
                                flex: 1,
                                textAlign: 'left'
                            }
                        }
                    }}
                >
                    {isAllSelected 
                        ? props.allOptionText || translate("Tất cả") 
                        : children || translate("Chọn trạng thái")}
                </Button>
            )}
            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />
    );
};

export default ComboboxNoiTruTrangThai;