import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { phongApi } from "../../api/phongApi";

// Interface cho props của ComboboxPhong
export interface IComboboxPhongProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_toa?: string;
    dm_coso_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
}

// Interface cho dữ liệu phòng
export interface dm_phong {
    id: number;
    dm_coso_id: number;
    toa: string;
    ten_phong: string;
}

const ComboboxPhong = (props: IComboboxPhongProps) => {
    const [phongs, setPhongs] = useState<dm_phong[]>([]);
    const [open, setOpen] = useState(false);
    const [filter, setFilter] = useState('');

    const { translate } = useCommonContext();

    const dataSource = useMemo(() => {
        if (!props.dm_toa) return [];
        return phongs
            .filter(x => x.toa === props.dm_toa)
            .map(x => ({ 
                id: x.id, 
                text: x.ten_phong,
                data: x 
            }));
    }, [phongs, props.dm_toa]);

    const filteredItems = useMemo(() => {
        return dataSource.filter(item => 
            item.text.toLowerCase().includes(filter.toLowerCase())
        );
    }, [dataSource, filter]);

    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value);
    }, [dataSource, props.value]);

    useEffect(() => {
        loadPhongs();
    }, [props.dm_coso_id]);

    const setSelected = (selected: any) => {
        if (selected) {
            props.onValueChanged(selected.id, selected.data);
        }
    };

    const loadPhongs = async () => {
        if (props.dm_coso_id) {
            try {
                const res = await phongApi.selectAll();
                if (res.is_success) {
                    setPhongs(res.data.filter((x: dm_phong) => x.dm_coso_id === props.dm_coso_id));
                }
            } catch (error) {
                console.error("Không thể tải danh sách phòng:", error);
            }
        }
    };

    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button
                    trailingAction={TriangleDownIcon}
                    aria-labelledby={`${ariaLabelledBy}`}
                    {...anchorProps}
                    sx={{
                        width: props.width || '100%',
                        '[data-component="buttonContent"]': {
                            display: 'flex',
                            justifyContent: 'flex-start',
                            gridTemplateAreas: 'none',
                            gridTemplateColumns: 'none',
                            '& > :first-child': {
                                flex: 1,
                                textAlign: 'left'
                            }
                        }
                    }}
                    disabled={props.isReadonly || !props.dm_toa}
                >
                    {children || translate("Chọn phòng")}
                </Button>
            )}
            title=""
            placeholderText="Tìm kiếm phòng..."
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />
    );
};

export default ComboboxPhong;