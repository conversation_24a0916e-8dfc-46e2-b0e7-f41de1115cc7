import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { quanHeApi } from "../../api/quanHeApi";
import { useCommonContext } from '../../contexts/common';
interface IComboboxQuanHeProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isAcceptance?: boolean;
    // sx?: BetterSystemStyleObject,
};
const ComboboxQuanHe = (props: IComboboxQuanHeProps) => {
    const [QuanHe, setQuanHe] = useState<any[]>([]);

    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        if (props.isAcceptance) {
            return QuanHe.filter(x => x.id > 2).map(x => ({ id: x.id, text: x.quan_he }))
        }
        else {
            return QuanHe.map(x => ({ id: x.id, text: x.quan_he }))
        }
    }, [QuanHe])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        handleGetQuanHeAsync();
    }, [])
    const handleOpenChange = (isOpen: boolean) => {
        if (props.isReadonly) {
            // Ngừng việc mở dropdown khi readonly
            return;
        }
        setOpen(isOpen);
    };

    const handleSelectionChange = (selected: any) => {
        if (props.isReadonly) {
            // Ngừng thay đổi lựa chọn khi readonly
            return;
        }
        setSelected(selected);
    };
    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    const handleGetQuanHeAsync = async () => {
        const res = await quanHeApi.selectAll();
        if (res.is_success) {
            setQuanHe(res.data)
        }
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}
                    sx={{
                        width: '100%'
                    }}
                >
                    <p style={{ width: '100%', overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate("Chọn quan hệ")}
                    </p>
                    {/* {children || translate("KhoanNopCobobox.PlaceHolder")} */}
                </Button>
            )}
            
            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={handleOpenChange}
            items={filteredItems}
            selected={selected}
            onSelectedChange={handleSelectionChange}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />

    );
};

export default ComboboxQuanHe;