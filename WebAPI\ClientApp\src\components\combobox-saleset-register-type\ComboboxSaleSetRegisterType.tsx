import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { saleSetRegisterTypeApi } from "../../api/saleSetRegisterTypeApi"; 

interface IComboboxSaleSetRegisterTypeProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_khoi_id?: number;
    dm_truong_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    // sx?: BetterSystemStyleObject,
    maxWidth?: any
};
const ComboboxSaleSetRegisterType = (props: IComboboxSaleSetRegisterTypeProps) => {
    const [type, setType] = useState<any[]>([]);

    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return type.map(x => ({ id: x.id, text: x.name }))
    }, [type])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        handleGetSaleSetRegisterTypeAsync();
    }, [])

    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    const handleGetSaleSetRegisterTypeAsync = async () => {
        const res = await saleSetRegisterTypeApi.selectAll();
        if (res.is_success) {
            setType(res.data)
        }
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}
                    sx={{
                        maxWidth: props.maxWidth
                    }}
                >
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate("Chọn loại đăng ký")}
                    </p>
                </Button>
            )}

            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />

    );
};

export default ComboboxSaleSetRegisterType;