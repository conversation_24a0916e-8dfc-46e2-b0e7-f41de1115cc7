import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { tinhHuyenXaApi } from "../../api/tinhHuyenXaApi";
import { useCommonContext } from '../../contexts/common';
interface IComboboxTinhProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
};
const ComboboxTinh = (props: IComboboxTinhProps) => {
    const [Tinh, setTinh] = useState<any[]>([]);

    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return Tinh.map(x => ({ id: x.id, text: x.ten_tinh }))
    }, [Tinh])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        handleGetTinhAsync();
    }, [])

    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    const handleGetTinhAsync = async () => {
        const res = await tinhHuyenXaApi.selectTinh();
        if (res.is_success) {
            setTinh(res.data)
        }
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}
                    sx={{
                        width: '100%'
                    }}
                >
                    <p style={{ maxWidth: '100%', overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate("Chọn tỉnh")}
                    </p>
                </Button>
            )}

            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />

    );
};

export default ComboboxTinh;