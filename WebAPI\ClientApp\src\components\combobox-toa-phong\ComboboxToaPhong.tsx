import { TriangleDownIcon } from "@primer/octicons-react";
import { Box, Button, FormControl, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { phongApi } from "../../api/phongApi";

interface ComboToaPhongProps {
    isReadonly?: boolean;
    onValueChanged: (phongId: number, phongData?: any) => void;
    className?: string;
    width?: string | number;
    dm_coso_id?: number;
    initialRoomId?: number;  // Thêm prop này
}

interface dm_phong {
    id: number;
    dm_coso_id: number;
    toa: string;
    ten_phong: string;
}

const ComboToaPhong = (props: ComboToaPhongProps) => {
    const [phongs, setPhongs] = useState<dm_phong[]>([]);
    const [openToa, setOpenToa] = useState(false);
    const [openPhong, setOpenPhong] = useState(false);
    const [filterToa, setFilterToa] = useState('');
    const [filterPhong, setFilterPhong] = useState('');
    const [selectedToa, setSelectedToa] = useState<string>('');
    const [selectedPhongId, setSelectedPhongId] = useState<number>(props.initialRoomId || 0);

    const { translate } = useCommonContext();

    // Effect để set tòa ban đầu khi có initialRoomId
    useEffect(() => {
        if (props.initialRoomId && phongs.length > 0) {
            const initialPhong = phongs.find(p => p.id === props.initialRoomId);
            if (initialPhong) {
                setSelectedToa(initialPhong.toa);
                setSelectedPhongId(initialPhong.id);
            }
        }
    }, [props.initialRoomId, phongs]);

    const toaList = useMemo(() => {
        return Object.values(
            phongs.reduce((acc: { [key: string]: { id: string; text: string } }, curr) => {
                if (!acc[curr.toa]) {
                    acc[curr.toa] = {
                        id: curr.toa,
                        text: curr.toa
                    };
                }
                return acc;
            }, {})
        );
    }, [phongs]);

    const phongList = useMemo(() => {
        if (!selectedToa) return [];
        return phongs
            .filter(x => x.toa === selectedToa)
            .map(x => ({
                id: x.id,
                text: x.ten_phong,
                data: x
            }));
    }, [phongs, selectedToa]);

    const filteredToas = useMemo(() => {
        return toaList.filter(item =>
            item.text.toLowerCase().includes(filterToa.toLowerCase())
        );
    }, [toaList, filterToa]);

    const filteredPhongs = useMemo(() => {
        return phongList.filter(item =>
            item.text.toLowerCase().includes(filterPhong.toLowerCase())
        );
    }, [phongList, filterPhong]);

    useEffect(() => {
        loadPhongs();
    }, [props.dm_coso_id]);

    const loadPhongs = async () => {
        if (props.dm_coso_id) {
            try {
                const res = await phongApi.selectAll();
                if (res.is_success) {
                    setPhongs(res.data.filter((x: dm_phong) => x.dm_coso_id === props.dm_coso_id));
                }
            } catch (error) {
                console.error("Không thể tải danh sách phòng:", error);
            }
        }
    };

    const handleToaSelected = (selected: any) => {
        if (selected) {
            setSelectedToa(selected.id);
            setSelectedPhongId(0);
            props.onValueChanged(0, null);
        }
    };

    const handlePhongSelected = (selected: any) => {
        if (selected) {
            setSelectedPhongId(selected.id);
            props.onValueChanged(selected.id, selected.data);
        }
    };

    return (
        <Box sx={{ display: 'flex', gap: 3 }}>
            <FormControl sx={{ flex: 1 }}>
                <FormControl.Label>Tòa</FormControl.Label>
                <SelectPanel
                    renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                        <Button
                            trailingAction={TriangleDownIcon}
                            aria-labelledby={`${ariaLabelledBy}`}
                            {...anchorProps}
                            sx={{
                                width: '100%',
                                '[data-component="buttonContent"]': {
                                    display: 'flex',
                                    justifyContent: 'flex-start',
                                    gridTemplateAreas: 'none',
                                    gridTemplateColumns: 'none',
                                    '& > :first-child': {
                                        flex: 1,
                                        textAlign: 'left'
                                    }
                                }
                            }}
                            disabled={props.isReadonly}
                        >
                            {children || selectedToa || translate("Chọn tòa")}
                        </Button>
                    )}
                    placeholderText="Tìm kiếm tòa..."
                    open={openToa}
                    onOpenChange={setOpenToa}
                    items={filteredToas}
                    selected={toaList.find(x => x.id === selectedToa)}
                    onSelectedChange={handleToaSelected}
                    onFilterChange={setFilterToa}
                    showItemDividers={true}
                    overlayProps={{ width: 'small', height: 'medium' }}
                />
            </FormControl>

            <FormControl sx={{ flex: 1 }}>
                <FormControl.Label>Phòng</FormControl.Label>
                <SelectPanel
                    renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                        <Button
                            trailingAction={TriangleDownIcon}
                            aria-labelledby={`${ariaLabelledBy}`}
                            {...anchorProps}
                            sx={{
                                width: '100%',
                                '[data-component="buttonContent"]': {
                                    display: 'flex',
                                    justifyContent: 'flex-start',
                                    gridTemplateAreas: 'none',
                                    gridTemplateColumns: 'none',
                                    '& > :first-child': {
                                        flex: 1,
                                        textAlign: 'left'
                                    }
                                }
                            }}
                            disabled={props.isReadonly || !selectedToa}
                        >
                            {children || (phongList.find(x => x.id === selectedPhongId)?.text) || translate("Chọn phòng")}
                        </Button>
                    )}
                    placeholderText="Tìm kiếm phòng..."
                    open={openPhong}
                    onOpenChange={setOpenPhong}
                    items={filteredPhongs}
                    selected={phongList.find(x => x.id === selectedPhongId)}
                    onSelectedChange={handlePhongSelected}
                    onFilterChange={setFilterPhong}
                    showItemDividers={true}
                    overlayProps={{ width: 'small', height: 'medium' }}
                />
            </FormControl>
        </Box>
    );
};

export default ComboToaPhong;