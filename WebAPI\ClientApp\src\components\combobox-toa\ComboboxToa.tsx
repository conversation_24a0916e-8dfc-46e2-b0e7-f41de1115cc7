import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { phongApi } from "../../api/phongApi";
interface IComboboxToaProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_khoi_id?: number;
    dm_truong_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    dm_coso_id?: number;  
    // sx?: BetterSystemStyleObject,
};
const ComboboxToa = (props: IComboboxToaProps) => {
    const [Toa, setToa] = useState<any[]>([]);

    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return Toa.map(x => ({ id: x.id, text: x.toa }))
    }, [Toa])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        handleGetToaAsync();
    }, [])

    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    const handleGetToaAsync = async () => {
        const res = await phongApi.selectAll();
        if (res.is_success) {
            setToa(res.data)
        }
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button 
                    trailingAction={TriangleDownIcon} 
                    aria-labelledby={` ${ariaLabelledBy}`} 
                    {...anchorProps}
                    sx={{
                        width: '100%',
                        '[data-component="buttonContent"]': {
                            display: 'flex',
                            justifyContent: 'flex-start',
                            gridTemplateAreas: 'none',
                            gridTemplateColumns: 'none',
                            '& > :first-child': {
                                flex: 1,
                                textAlign: 'left'
                            }
                        }
                    }}
                >
                    {children || translate("Chọn tòa")}
                </Button>
            )}
            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />
    );
};

export default ComboboxToa;