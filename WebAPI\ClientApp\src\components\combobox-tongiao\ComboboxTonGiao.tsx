import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { tonGiaoApi } from "../../api/tonGiaoApi";
interface IComboboxTonGiaoProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_khoi_id?: number;
    dm_truong_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    // sx?: BetterSystemStyleObject,
};
const ComboboxTonGiao = (props: IComboboxTonGiaoProps) => {
    const [TonGiao, setTonGiao] = useState<any[]>([]);

    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();
    const dataSource = useMemo(() => {
        return TonGiao.map(x => ({ id: x.id, text: x.ton_giao }))
    }, [TonGiao])
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        handleGetTonGiaoAsync();
    }, [])

    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }
    const handleGetTonGiaoAsync = async () => {
        const res = await tonGiaoApi.selectAll();
        if (res.is_success) {
            setTonGiao(res.data)
        }
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}
                    sx={{
                        width:'100%'
                    }}
                >
                    <p style={{ width: '100%', overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate("Chọn tôn giáo")}
                    </p>
                    {/* {children || translate("KhoanNopCobobox.PlaceHolder")} */}
                </Button>
            )}

            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />
    );
};

export default ComboboxTonGiao;