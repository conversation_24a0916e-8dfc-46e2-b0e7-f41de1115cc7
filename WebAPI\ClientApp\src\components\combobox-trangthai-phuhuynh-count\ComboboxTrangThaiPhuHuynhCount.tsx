import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { trangThaiPhuHuynhApi } from "../../api/trangThaiPhuHuynhApi";

interface IComboboxTrangThaiPhuHuynhProps {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: any) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_khoi_id?: number;
    dm_truong_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    phuHuynhs?: any[]; 
};

const ComboboxTrangThaiPhuHuynh = (props: IComboboxTrangThaiPhuHuynhProps) => {
    const [TrangThaiPhuHuynh, setTrangThai<PERSON>huHuynh] = useState<any[]>([]);
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')

    const { translate } = useCommonContext();

    // Tính toán số lượng cho mỗi trạng thái
    const calculateCounts = useMemo(() => {
        const counts: { [key: number]: number } = {};
        if (props.phuHuynhs && props.phuHuynhs.length > 0) {
            props.phuHuynhs.forEach(ph => {
                const statusId = ph.ts_phuhuynh_adm_status_id;
                counts[statusId] = (counts[statusId] || 0) + 1;
            });
        }
        return counts;
    }, [props.phuHuynhs]);

    const dataSource = useMemo(() => {
        const items = TrangThaiPhuHuynh.map(x => {
            const count = calculateCounts[x.id] || 0;
            return {
                id: x.id,
                text: `${x.name} - [${count}]`,
                count: count
            };
        });
    
        const totalCount = props.phuHuynhs?.length || 0;
        const allOption = {
            id: 0,
            text: `Tất cả - [${totalCount}]`,
            count: totalCount
        };
    
        return [allOption, ...items];
    }, [TrangThaiPhuHuynh, calculateCounts, props.phuHuynhs]);

    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])

    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value)
    }, [dataSource, props.value])

    useEffect(() => {
        handleGetTrangThaiPhuHuynhAsync();
    }, [])

    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id)
    }

    const handleGetTrangThaiPhuHuynhAsync = async () => {
        const res = await trangThaiPhuHuynhApi.selectAll();
        if (res.is_success) {
            setTrangThaiPhuHuynh(res.data)
        }
    }

    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}
                    sx={{
                        width:'100%'
                    }}
                >
                    <p style={{ width: '100%', overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate("Chọn trạng thái phụ huynh")}
                    </p>
                </Button>
            )}
            
            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />
    );
};

export default ComboboxTrangThaiPhuHuynh;