import { ActionList, ActionMenu } from "@primer/react";
import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { trangThaiThiSinhApi } from "../../api/trangThaiThiSinhApi";
import { useCommonContext } from "../../contexts/common";
import { ex_trangthai_thisinh } from "../../models/response/trang-thai-thi-sinh/ex_trangthai_thisinh";
import { RootState } from "../../state/reducers";

export type ComboboxTrangThaiThiSinhProps = {
    isReadonly?: boolean,
    value?: number,
    onValueChanged: (id: number) => void,
    className?: string,
    isShowClearButton?: boolean,
    preText?: string,
    stylingMode?: "outlined" | "filled" | "underlined"
}

const ComboboxTrangThaiThiSinh = (props: ComboboxTrangThaiThiSinhProps) => {
    const [dm_trangThaiThiSinhs, setDmTrangThaiThiSinhs] = useState<ex_trangthai_thisinh[]>([]);
    const { translate } = useCommonContext();
    const { language } = useSelector((x: RootState) => x.common);

    const handleReloadAsync = async () => {
        const res = await trangThaiThiSinhApi.selectAll();
        if (res.is_success) {
            setDmTrangThaiThiSinhs(res.data);

            // Mặc định chọn id = 1 nếu chưa có value và có dữ liệu
            if (!value && res.data.length > 0) {
                const defaultItem = res.data.find((x: ex_trangthai_thisinh) => x.id === 1);
                if (defaultItem) {
                    onValueChanged(1);
                }
            }
        }
    };

    useEffect(() => {
        handleReloadAsync();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const { value, onValueChanged } = props;

    const selectedData = useMemo(() => {
        if (value && dm_trangThaiThiSinhs) {
            const found = dm_trangThaiThiSinhs.find(x => x.id === value);
            return found;
        }
        return undefined;
    }, [dm_trangThaiThiSinhs, value]);

    return (
        <div style={{ width: '100%' }}>
            <ActionMenu>
                <ActionMenu.Button
                    aria-label="Select trang thai thi sinh"
                    style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between'
                    }}
                >
                    {selectedData ?
                        (language === "en" ? selectedData.ten_trangthai_en : selectedData.ten_trangthai)
                        : (props.preText || translate("Chọn trạng thái thí sinh"))}
                </ActionMenu.Button>
                <ActionMenu.Overlay width="small">
                    <ActionList selectionVariant="single">
                        {props.isShowClearButton &&
                            <ActionList.Item key={0} selected={value !== undefined && 0 === value}
                                onSelect={() => {
                                    onValueChanged(0)
                                }}
                            >
                                {language === "en" ? "Select" : "Chọn trạng thái"}
                            </ActionList.Item>
                        }
                        {dm_trangThaiThiSinhs && dm_trangThaiThiSinhs.map((item) => {
                            return (
                                <ActionList.Item
                                    key={item.id}
                                    selected={value !== undefined && item.id === value}
                                    onSelect={() => {
                                        onValueChanged(item.id || 0)
                                    }}
                                >
                                    {language === "en" ? item.ten_trangthai_en : item.ten_trangthai}
                                </ActionList.Item>
                            );
                        })}
                    </ActionList>
                </ActionMenu.Overlay>
            </ActionMenu>
        </div>
    );
};

export default ComboboxTrangThaiThiSinh;
