import { TriangleDownIcon } from "@primer/octicons-react";
import { Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { trangThaiHocSinhApi } from "../../api/trangThaiHocSinhApi";
import { useCommonContext } from "../../contexts/common";
import { RootState } from "../../state/reducers";

export type ComboboxTrangThaiHocSinhProps = {
    isReadonly?: boolean,
    value?: number,
    onValueChanged: (id: number) => void,
    className?: string,
    isShowClearButton?: boolean,
    preText?: string,
    width?: string | number,
    stylingMode?: "outlined" | "filled" | "underlined"
}

const ComboboxTrangThaiHocSinh = (props: ComboboxTrangThaiHocSinhProps) => {
    const { translate } = useCommonContext();
    const { language } = useSelector((x: RootState) => x.common);
    const [dm_TrangThaiHocSinhs, setDmTrangThaiHocSinhs] = useState<any[]>([]);
    const [open, setOpen] = useState(false);
    const [filter, setFilter] = useState('');
    
    // Chuyển đổi dữ liệu cho SelectPanel
    const dataSource = useMemo(() => {
        return dm_TrangThaiHocSinhs.map(x => ({ 
            id: x.id, 
            text: language === "en" ? x.trang_thai_en : x.trang_thai 
        }));
    }, [dm_TrangThaiHocSinhs, language]);
    
    // Lọc các mục theo từ khóa tìm kiếm
    const filteredItems = useMemo(() => {
        return dataSource.filter(item => 
            item.text.toLowerCase().includes(filter.toLowerCase())
        );
    }, [dataSource, filter]);
    
    // Lấy mục đã chọn
    const selected = useMemo(() => {
        return dataSource.find(x => x.id === props.value);
    }, [dataSource, props.value]);
    
    useEffect(() => {
        const fetchData = async () => {
            try {
                const res = await trangThaiHocSinhApi.selectAllCheckDangKy(1);
                if (res.is_success) {
                    setDmTrangThaiHocSinhs(res.data); 
                } else {
                    console.error('Failed to load data:', res.message);
                }
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };
        fetchData();
    }, []);
    
    const setSelected = (selecteds: any) => {
        if (selecteds) {
            props.onValueChanged(selecteds.id);
        }
    }
    
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button 
                    trailingAction={TriangleDownIcon} 
                    aria-labelledby={` ${ariaLabelledBy}`} 
                    {...anchorProps}
                    sx={{
                        width: '100%',
                        '[data-component="buttonContent"]': {
                            display: 'flex',
                            justifyContent: 'flex-start',
                            gridTemplateAreas: 'none',
                            gridTemplateColumns: 'none',
                            '& > :first-child': {
                                flex: 1,
                                textAlign: 'left',
                                overflow: "hidden",
                                textOverflow: "ellipsis"
                            }
                        }
                    }}
                >
                    {children || translate("Chọn trạng thái")}
                </Button>
            )}
            title={""}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'small', height: 'medium' }}
        />
    );
}

export { ComboboxTrangThaiHocSinh };