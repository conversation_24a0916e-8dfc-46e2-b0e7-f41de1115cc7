import { Action<PERSON>ist, ActionMenu } from "@primer/react";
import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { truongApi } from "../../api/truongApi";
import { useCommonContext } from "../../contexts/common";
import { ITruong } from "../../models/response/truong/ITruong";
import { RootState } from "../../state/reducers";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";

export type ComboboxTruongProps = {
    isReadonly?: boolean,
    value?: number,
    onValueChanged: (id: number) => void,
    className?: string,
    isShowClearButton?: boolean,
    preText?: string,
    stylingMode?: "outlined" | "filled" | "underlined"
}
const ComboboxTruong = (props: ComboboxTruongProps) => {
    const [dm_truongs, setDmTruongs] = useState<ITruong[]>([]);
    const { translate } = useCommonContext();
    const { language } = useSelector((x: RootState) => x.common);
    const { dm_coso_id } = useCommonSelectedHook();

    const handleReloadAsync = async () => {
        const res = await truongApi.selectAll();
        if (res.is_success) {
            if (dm_coso_id) {
                // Lọc chỉ các trường thuộc cơ sở hiện tại
                const filteredData = res.data.filter((truong: ITruong) => truong.dm_coso_id === dm_coso_id);

                // Nếu có giá trị đã chọn và không thuộc cơ sở hiện tại, thêm trường đó vào danh sách
                if (value && value > 0) {
                    const selectedSchool = res.data.find((x: ITruong) => x.id === value);
                    if (selectedSchool && selectedSchool.dm_coso_id !== dm_coso_id) {
                        // Thêm trường đã chọn vào đầu danh sách
                        filteredData.unshift(selectedSchool);
                    }
                }

                console.log('ComboboxTruong - Filtered schools by dm_coso_id:', dm_coso_id, 'Found:', filteredData.length, 'Current value:', value);
                setDmTruongs(filteredData);
            } else {
                console.log('ComboboxTruong - All schools loaded, no dm_coso_id filter. Current value:', value);
                setDmTruongs(res.data);
            }
        }
    };

    useEffect(() => {
        handleReloadAsync();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dm_coso_id]);

    const { value, onValueChanged } = props;

    const selectedData = useMemo(() => {
        if (value && dm_truongs) {
            const found = dm_truongs.find(x => x.id === value);
            console.log('ComboboxTruong - Looking for school with id:', value, 'Found:', found ? 'Yes' : 'No');
            return found;
        }
        return undefined;
    }, [dm_truongs, value]);

    // Comment out this effect to prevent automatic reset of value when school is not found
    // useEffect(() => {
    //     if (value && dm_truongs && !selectedData && onValueChanged) {
    //         onValueChanged(0);
    //     }
    // }, [dm_truongs, value, selectedData, onValueChanged]);
    // Log the current state when rendering
    console.log('ComboboxTruong - Rendering with value:', value, 'selectedData:', selectedData ? selectedData.id : 'none', 'dm_truongs count:', dm_truongs.length);

    return (
        <div style={{ width: '100%' }}>
        <ActionMenu>
            <ActionMenu.Button
                aria-label="Select school year"
                style={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between'
                }}
            >
                {selectedData ?
                    (language === "en" ? selectedData.ten_truong_en : selectedData.ten_truong)
                    : translate("TruongCombobox.PlaceHolder")}
            </ActionMenu.Button>
            <ActionMenu.Overlay width="small">
                <ActionList selectionVariant="single">
                    {props.isShowClearButton &&
                        <ActionList.Item key={0} selected={value !== undefined && 0 === value}
                            onSelect={() => {
                                onValueChanged(0)
                            }}
                        >
                            {language === "en" ? "Select" : "Chọn cấp học"}
                        </ActionList.Item>
                    }
                    {dm_truongs && dm_truongs.map((item) => {
                        return (
                            <ActionList.Item
                                key={item.id}
                                selected={value !== undefined && item.id === value}
                                onSelect={() => {
                                    onValueChanged(item.id || 0)
                                }}
                            >
                                {language === "en" ? item.ten_truong_en : item.ten_truong}
                            </ActionList.Item>
                        );
                    })}
                </ActionList>
            </ActionMenu.Overlay>
        </ActionMenu>
    </div>
    );
}

export { ComboboxTruong };

