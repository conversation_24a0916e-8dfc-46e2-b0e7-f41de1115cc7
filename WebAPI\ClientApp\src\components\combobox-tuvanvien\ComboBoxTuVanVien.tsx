import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { sis_giaovien_view } from '../../models/response/giao-vien/sis_giaovien';
import { Box, Button, SelectPanel } from '@primer/react';
import { VariantType } from '@primer/react/lib/Button/types';
import Text from '../ui/text';
import { userApi } from '../../api/userApi';
import { IUserModel } from '../../models/response/account/user-info';

type IComboBoxGiaoVienProps = {
    dm_truong_id: number;
    isReadonly?: boolean;
    value: number;
    onValueChanged: (id: number, data?: IUserModel) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean,
    maxWidth?: any,
    variant?: VariantType
};

const getTrailingVisual = (giaoVien: IUserModel) => {
    return (
        <Text text={giaoVien.email || ""} sx={{
            color: "fg.muted",
            fontSize: "12px"
        }} />
    );
}

const ComboBoxGiaoVien = (props: IComboBoxGiaoVienProps) => {
    const { translate } = useCommonContext();
    const [user, setUsers] = useState<IUserModel[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                const response = await userApi.SelectAllTuVanVien();
                if (response && response.data) {
                    setUsers(response.data);
					console.log(response.data)
                }
            } catch (error) {
                console.error("Error fetching tu van vien data:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    
    const dataSource = useMemo(() => {
        return user.map(x => {
            const item: any = { 
                id: x.id,
                text: x.full_name,
                search: `${x.full_name} ${x.email} ${x.phone_number}`,
                trailingVisual: getTrailingVisual(x)
            }
            return item;
        })
    }, [user, filter])
    
    const filterdData = useMemo(() => {
        return dataSource.filter(item => 
            item.search.toLowerCase().includes(filter.toLowerCase())
        )
    }, [dataSource, filter])
    
    const _selectedData = useMemo(() => {
        return dataSource.find(item => item.id === props.value)
    }, [props.value, dataSource])
 
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button
                    variant={props.variant}
                    sx={{
                        width: '100%'
                    }} 
                    trailingAction={TriangleDownIcon} 
                    aria-labelledby={` ${ariaLabelledBy}`} 
                    {...anchorProps}
                    disabled={loading}
                >
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {loading ? 'Đang tải...' : (children || translate(`Chọn tư vấn viên`))}
                    </p>
                </Button>
            )}
            title={<>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        {translate(`Chọn tư vấn viên`)}
                    </Box>
                    {(props.isShowClearBtn === undefined || props.isShowClearBtn === true) && props.value > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => {
                                props.onValueChanged(0)
                            }}
                        >
                            Bỏ chọn
                        </Button>
                    }
                </Box>
            </>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedData}
            onSelectedChange={(data: any) => {
                const selectedItem = user.find(item => item.id === data.id);
                props.onValueChanged(data.id, selectedItem);
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />
    );
};

export default ComboBoxGiaoVien;