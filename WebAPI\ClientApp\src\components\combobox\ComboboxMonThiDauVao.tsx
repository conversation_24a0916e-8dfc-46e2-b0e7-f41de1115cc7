import { ActionList, ActionMenu } from "@primer/react";
import { useEffect, useMemo, useState } from "react";
import { useCommonContext } from "../../contexts/common";
import { monThiDauVaoApi } from "../../api/monThiDauVaoApi";
import { NotifyHelper } from "../../helpers/toast";
import { useSelector } from "react-redux";
import { RootState } from "../../state/reducers";

export type ComboboxMonThiDauVaoProps = {
  isReadonly?: boolean,
  value?: number,
  onValueChanged: (id: number) => void,
  className?: string,
  isShowClearButton?: boolean,
  preText?: string,
  stylingMode?: "outlined" | "filled" | "underlined"
}

interface MonThiDauVaoItem {
  id: number;
  mon_thi: string;
  mon_thi_en?: string;
}

const ComboboxMonThiDauVao = (props: ComboboxMonThiDauVaoProps) => {
  const [monThiDauVaos, setMonThiDauVaos] = useState<MonThiDauVaoItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { translate } = useCommonContext();
  const { language } = useSelector((x: RootState) => x.common);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const res = await monThiDauVaoApi.selectAll();
      if (res.is_success) {
        setMonThiDauVaos(res.data || []);
      } else {
        NotifyHelper.Error(res.message || "Có lỗi khi tải dữ liệu môn thi đầu vào");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi khi tải dữ liệu môn thi đầu vào");
    } finally {
      setIsLoading(false);
    }
  };

  const selectedData = useMemo(() => {
    if (props.value && monThiDauVaos) {
      return monThiDauVaos.find(x => x.id === props.value);
    }
    return undefined;
  }, [monThiDauVaos, props.value]);

  return (
    <div style={{ width: '100%' }}>
      <ActionMenu>
        <ActionMenu.Button
          aria-label="Select môn thi đầu vào"
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between'
          }}
        >
          {selectedData
            ? (language === "en" && selectedData.mon_thi_en ? selectedData.mon_thi_en : selectedData.mon_thi)
            : translate("Base.Label.Select")}
        </ActionMenu.Button>
        <ActionMenu.Overlay width="small">
          <ActionList selectionVariant="single">
            {props.isShowClearButton &&
              <ActionList.Item
                key={0}
                selected={props.value !== undefined && 0 === props.value}
                onSelect={() => {
                  props.onValueChanged(0);
                }}
              >
                {language === "en" ? "Select" : "Chọn môn thi đầu vào"}
              </ActionList.Item>
            }
            {monThiDauVaos && monThiDauVaos.map((item) => {
              return (
                <ActionList.Item
                  key={item.id}
                  selected={props.value !== undefined && item.id === props.value}
                  onSelect={() => {
                    props.onValueChanged(item.id);
                  }}
                >
                  {language === "en" && item.mon_thi_en ? item.mon_thi_en : item.mon_thi}
                </ActionList.Item>
              );
            })}
          </ActionList>
        </ActionMenu.Overlay>
      </ActionMenu>
    </div>
  );
};

export { ComboboxMonThiDauVao };
