.container {
    padding: 2rem 1rem 1rem 1rem;
    text-align: center;
}

.icon {
    font-size: 3rem;
    color: var(--danger);
    color: #a40e26;
    text-shadow: 2px 8px 6px rgba(0, 0, 0, 0.2),
        0px -5px 35px rgba(255, 255, 255, 0.3);
}

.icon_primary {
    color: var(--primay) !important;
}

.icon_success {
    color: var(--success) !important;
}

.text {
    font-size: 1.2rem;
    font-weight: 500;
    margin-top: 2rem;
}

.actions {
    margin-top: 3rem;
}

.actions button {
    min-width: 100px;
    margin-left: 10px;
}

/* .bp3-overlay-backdrop{
    background: rgba(255,255,255,0.3);
}
.bp3-dialog{
    background: rgb(235, 241, 245,0.5);
    backdrop-filter: blur(20px);
} */