import { Box } from '@primer/react';
import { DialogHeight, DialogWidth } from '@primer/react/lib-esm/drafts';
import React from 'react';
import Button from '../ui/button';
import Modal from '../ui/modal';
import Text from '../ui/text';
type AnimationConfrimProps = {
    icon?: string,
    // text: string,
    text_confirm_button?: string,
    text_close_button?: string,
    onConfirmed: () => void,
    onClose: () => void,
    type?: 'default' | 'primary' | 'danger' | 'success',
    size?: "small" | "large",
    is_saving?: boolean | false,
    animationOf?: string,
    iconComponent?: React.ReactNode,
    text?: string,
    closeButtonText?: string,
    confirmButtonText?: string,
    children?: React.ReactNode,
    title?: string,
    subtitle?: string,
    width?: DialogWidth,
    height?: DialogHeight,
    isSaving?: boolean
}
type DefaultConfrimProps = {
    icon?: string,
    text_confirm_button?: string,
    text_close_button?: string,
    onConfirmed: () => void,
    onClose: () => void,
    size?: "small" | "large",
    is_saving?: boolean | false,
    // onConfirm: () => void,
    // onCancel: () => void,
    type?: 'default' | 'primary' | 'danger' | 'success',
    iconComponent?: React.ReactNode,
    text?: string,
    closeButtonText?: string,
    confirmButtonText?: string,
    children?: React.ReactNode,
    title?: string,
    subtitle?: string,
    width?: DialogWidth,
    height?: DialogHeight,
    isSaving?: boolean
}
const AnimationConfirm = (props: AnimationConfrimProps) => {
    // const [closePopUp, setClosePopUp] = useState<boolean>(false);
    const { onClose, onConfirmed } = props;
    return (
        <Modal
            isOpen={true}
            onClose={onClose}
            // title={props.title ?? "Xác nhận"}
            subtitle={props.subtitle}
            width={props.width ?? "medium"}
            height={props.height ?? "auto"}
            renderHeader={() => {
                return (
                    <Box sx={{
                        p: 3,
                        pl: 3,
                        pb: 0,
                        fontSize: "24px",
                        fontWeight: 600
                    }}>
                        {props.title ?? "Confirm"}
                    </Box>
                );
            }}
        >
            <Box sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                flexDirection: "column",
                pl: 0,
                pr: 0
            }}>
                {props.iconComponent && <Box>
                    {props.iconComponent}
                </Box>
                }
                {props.text && <Box>
                    <Text text={props.text} sx={{
                        fontSize: 14,
                        color: "fg.muted"
                        // fontWeight: "500"
                    }} />
                </Box>}

            </Box>
            {props.children}
            <Box sx={{
                // p: 2,
                p: 0,
                pt: 3,
                display: "flex",
                flexDirection: "row-reverse"
            }}>

                <Button text='Base.Label.Confirm' variant={props.type === "success" ? "primary" : "danger"}
                    onClick={onConfirmed}
                    isLoading={props.isSaving}
                    size='medium'
                />
                <Button text='Base.Label.Cancel'
                    onClick={onClose}
                    size='medium'
                    sx={{ mr: 1 }}
                />
            </Box>
        </Modal>

    );
}
const DefaultConfirm = (props: DefaultConfrimProps) => {
    const { onClose, onConfirmed } = props;

    return (
        <Modal
            isOpen={true}
            onClose={onClose}
            // title={props.title ?? "Xác nhận"}
            subtitle={props.subtitle}
            width={props.width ?? "medium"}
            height={props.height ?? "auto"}
            renderHeader={() => {
                return (
                    <Box sx={{
                        p: 3,
                        pl: 4,
                        pb: 0,
                        fontSize: "24px",
                        fontWeight: 600
                    }}>
                        {props.title ?? "Confirm"}
                    </Box>
                );
            }}
        >
            <Box sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                flexDirection: "column",
                pl: 2,
                pr: 2
            }}>
                {props.iconComponent && <Box>
                    {props.iconComponent}
                </Box>
                }
                {props.text && <Box>
                    <Text text={props.text} sx={{
                        fontSize: 14,
                        color: "fg.muted"
                        // fontWeight: "500"
                    }} />
                </Box>}

            </Box>
            {props.children}
            <Box sx={{
                p: 3,
                display: "flex",
                flexDirection: "row-reverse"
            }}>

                <Button text='Base.Label.Confirm' variant={props.type === "success" ? "primary" : "danger"}
                    onClick={onConfirmed}
                    isLoading={props.isSaving}
                    size='medium'
                />
                <Button text='Base.Label.Cancel'
                    onClick={onClose}
                    size='medium'
                    sx={{ mr: 1 }}
                />
            </Box>
        </Modal>

    );
}


export { AnimationConfirm, DefaultConfirm };

