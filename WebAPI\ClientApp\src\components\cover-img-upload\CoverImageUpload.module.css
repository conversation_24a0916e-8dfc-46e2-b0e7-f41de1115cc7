.container {
    /* border-radius: 10px; */
}

.empty_container {
    /* background-color: rgba(0, 0, 0, 0.06); */
    /* border-radius: 10px; */
    /* min-height: 100px; */
    /* display: flex; */
    /* align-items: center; */
    /* justify-content: center; */
}

.img_container {
    position: relative;
    border-radius: 10px;
    min-height: 100px;
    /* background: #ddd; */
}

.img_container img {
    width: 100%;
    height: auto;
    border-radius: 10px;
}

.remove {
    color: #c2242c !important;
    position: absolute;
    top: 5px;
    right: 5px;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    background-color: rgba(255, 255, 255, .7);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;

}
.remove:hover{
    background-color: white;
    box-shadow: rgba(3, 102, 214, 0.3) 0px 0px 0px 3px;
}