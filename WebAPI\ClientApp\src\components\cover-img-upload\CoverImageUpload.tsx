import { Box } from '@primer/react';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { ePageBaseStatus } from '../../models/ePageBaseStatus';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
import Upload from '../upload';
import styles from './CoverImageUpload.module.css';
interface ICoverImageUploadProps {
	value: string;
	onValueChanged: (fileUpload: string) => void;
}

const CoverImageUpload = (props: ICoverImageUploadProps) => {
	const { value, onValueChanged } = props;
	const dispatch = useDispatch();
	const { translate } = useCommonContext();
	const { status, uploadImageResult } = useSelector((state: RootState) => state.uploadFile);

	const handleSelectFile = () => {
		const input = document.createElement('input');
		input.setAttribute('type', 'file');
		input.setAttribute('accept', 'image/png, image/gif, image/jpeg');
		input.click();
		input.onchange = async () => {
			var file = input && input.files ? input.files[0] : null;
			if (file) {
				dispatch(actions.uploadFile.uploadImageStart(file));
			}
		};
	};

	useEffect(() => {
		if (status === ePageBaseStatus.is_completed) {
			onValueChanged(uploadImageResult?.url || '');
			dispatch(actions.uploadFile.uploadImageComplete());
		}
		//eslint-disable-next-line react-hooks/exhaustive-deps
	}, [status]);

	return (
		<div className={styles.container}>
			{value ? (
				<Box className={styles.img_container}
					sx={{
						borderRadius: 2,
						borderStyle: "solid",
						borderColor: "border.default",
						borderWidth:"1px"
					}}
				>
					<img src={value} alt='cover' />
					<button type='button' className={styles.remove} onClick={() => onValueChanged('')} title={translate('Base.Label.Delete')}>
						<em className='fas fa-times'></em>
					</button>
				</Box>
			) : (
				<div className={styles.empty_container}>
					<Upload
						onUploadSuccess={(data) => {
							onValueChanged(data.url)

						}}
					/>
					{/* <Button type='success' stylingMode='text' text='Upload' icon='upload' onClick={() => handleSelectFile()} /> */}
				</div>
			)}
		</div>
	);
};

export default CoverImageUpload;
