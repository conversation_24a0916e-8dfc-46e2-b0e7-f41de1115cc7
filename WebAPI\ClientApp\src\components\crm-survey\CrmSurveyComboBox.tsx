import { ChevronDownIcon, ChevronRightIcon, TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, IconButton, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { crmSurveyFormQuestionApi } from '../../api/crmSurveyFormQuestionApi';
import { ICrmSurveyFormQuestionDataSource } from '../../models/response/crm-survey/ICrmSurveyFormQuestionDataSource';

type CrmSurveyComboBoxProps = {
    crm_survey_form_question_id: number;
    value?: number;
    onValueChanged: (id: number, data?: ICrmSurveyFormQuestionDataSource) => void;
    width?: string | number;
    dm_coso_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearButton?: boolean,
    fieldValue?: number;
    align?: "left" | "right"
};

const CrmSurveyComboBox: React.FC<CrmSurveyComboBoxProps> = (props) => {
    const [dataSource, setDataSource] = useState<ICrmSurveyFormQuestionDataSource[]>([]);
    const [filteredData, setFilteredData] = useState<ICrmSurveyFormQuestionDataSource[]>([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')


    const filteredItems = useMemo(() => {
        return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [dataSource, filter])
    //nếu selected= obj -> chọn 1, = array -> chọn nhiều
    const selected = useMemo(() => {
        return dataSource.find(x => x.id == props.value)
    }, [dataSource, props.value])
    const setSelected = (selecteds: any) => {
        if (selecteds)
            props.onValueChanged(selecteds.id, selecteds)
    }
    useEffect(() => {
        handleReload();
    }, [props.crm_survey_form_question_id, props.fieldValue]);

    useEffect(() => {
        // Lọc `dataSource` dựa trên `searchTerm`
        setFilteredData(
            dataSource.filter(x =>
                x.text.toLowerCase().includes(searchTerm.toLowerCase())
            )
        );
    }, [searchTerm, dataSource]);

    const handleReload = async () => {
        const res = await crmSurveyFormQuestionApi.select_datasource(props.crm_survey_form_question_id, props.dm_coso_id ?? 1, props.fieldValue ?? 0);
        if (res.is_success) {
            setDataSource(res.data);
            setFilteredData(res.data); // Khởi tạo `filteredData` bằng toàn bộ `dataSource`
        }
    };

    return (

        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Box sx={{ display: "flex" }}>
                    <Button
                        sx={{
                            width: "-webkit-fill-available",
                        }}
                        trailingAction={ChevronDownIcon}
                        aria-labelledby={`${ariaLabelledBy}`}
                        {...anchorProps}
                        className={props.align ? `button-${props.align}` : ''}
                        // className='button-left'
                    >
                        {/* <Box
                            sx={{
                                flex: 'none'
                            }}
                        >
                            {children}
                        </Box> */}
                        <p
                            style={{
                                // maxWidth: props.maxWidth,
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                            }}
                        >
                            {children}
                        </p>
                    </Button>
                </Box>
            )}
            title={<Box sx={{ display: "flex", flexDirection: "row-reverse" }}>
                {props.isShowClearButton && props.value !== undefined && props.value > 0 &&
                    <IconButton aria-label={"Clear"} icon={XCircleFillIcon}
                        variant="invisible"
                        onClick={() => {
                            props.onValueChanged(0, undefined);
                        }}
                    />
                }
            </Box>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filteredItems}
            selected={selected}
            onSelectedChange={setSelected}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'medium', height: 'medium' }}
        />

    );
};

export default CrmSurveyComboBox;
