import { DownloadIcon, SearchIcon, SortAscIcon, SortDescIcon } from "@primer/octicons-react";
import { ActionList, ActionMenu, IconButton, TextInput } from '@primer/react';
import { DataTable, Table } from '@primer/react/drafts';
import clsx from 'clsx';
import React, { useEffect, useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';
import styles from "./DataTableRemotePaging.module.css";
import { eSortMode } from "../../models/eSortMode";
interface ISearchConfig {
    enable: boolean,
    search_key?: string,
    onValueChanged: (key: string) => void
}
interface ISortConfig {
    enable: boolean,
    field?: string,
    mode: eSortMode,
    onValueChanged: (field: string, mode: eSortMode) => void
}
interface IPaging {
    pageSize: number,
    pageIndex: number,
    pageCount: number,
    onPageIndexChanged: (pageIndex: number) => void
}
interface IColumn {
    id?: string,
    header: string | React.ReactNode,
    field?: string,
    rowHeader?: boolean,
    renderCell?: any,
    sortBy?: "alphanumeric" | "datetime" | any,
}
interface IDataTableRemotePagingProps {
    title?: string,
    titleComponent?: React.ReactNode,
    subTitle?: string,
    subTitleComponent?: React.ReactNode,
    data: any[],
    columns: IColumn[],
    actionComponent?: React.ReactNode,
    isLoading?: boolean,
    paging?: IPaging,
    height?: any,
    searchConfig?: ISearchConfig,
    sortConfig?: ISortConfig,
    exportEnable?: boolean

}
const DataTableRemotePaging = (props: IDataTableRemotePagingProps) => {
    const [search_key, setSearch_key] = useState("");
    const [searchKeyDelayed] = useDebounce(search_key, 1000);

    const { data, columns, title, titleComponent, subTitle, subTitleComponent,
        actionComponent,
        isLoading,
        paging,
        height,
        sortConfig,
        searchConfig,
        exportEnable
    } = props;
    const titleId = "repositories";
    const subTitileId: string = "repositories-subtitle";
    useEffect(() => {
        // if (searchKeyDelayed != (searchConfig?.search_key ?? "")) {
        props.searchConfig?.onValueChanged(searchKeyDelayed);
        // }
    }, [searchKeyDelayed])
    const sort_by = useMemo(() => {
        return sortConfig?.field ?? "";
    }, [sortConfig])
    const sort_by_name = useMemo(() => {
        return columns.filter(x => x.field == sort_by).map(x => x.header).join(",");
    }, [columns, sort_by])
    return (
        <div className={clsx(styles.table, isLoading ? styles.isLoading : "")}>
            <Table.Container sx={{
                // height: height,
                // minHeight: height,
                maxHeight: height
            }}>
                {props.title &&
                    <Table.Title as="h2" id={titleId}>
                        {title}
                    </Table.Title>
                }
                {props.titleComponent &&
                    <Table.Title as="h2" id={titleId}>
                        {titleComponent}
                    </Table.Title>
                }
                {props.subTitle &&
                    <Table.Subtitle as="p" id={subTitileId}>
                        {subTitle}
                    </Table.Subtitle>
                }
                {props.subTitleComponent &&
                    <Table.Subtitle as="p" id={subTitileId}>
                        {subTitleComponent}
                    </Table.Subtitle>
                }

                <Table.Actions>
                    {actionComponent}
                    {searchConfig && searchConfig.enable &&
                        <TextInput leadingVisual={SearchIcon}
                            placeholder='Tìm kiếm'
                            value={searchConfig.search_key}
                            onChange={(e) => {
                                setSearch_key(e.target.value);
                                // console.log({
                                //     e
                                // });

                            }}
                        >
                        </TextInput>
                    }
                    {sortConfig && sortConfig.enable &&
                        <ActionMenu>
                            <ActionMenu.Button leadingVisual={sortConfig?.mode === eSortMode.DESC ? SortDescIcon : SortAscIcon}>
                                Sort by <b>{sort_by_name}</b>
                            </ActionMenu.Button>
                            <ActionMenu.Overlay>

                                <ActionList selectionVariant="single" role='menu' aria-label="">
                                    {columns.filter(x => x.field).map(x => {
                                        return (
                                            <ActionList.Item key={x.id} role="menuitemcheckbox" selected={x.field === sortConfig.field}
                                                onSelect={() => {
                                                    props.sortConfig?.onValueChanged(x.field ?? "", sortConfig.mode)
                                                }}
                                            >{x.header}</ActionList.Item>
                                        );
                                    })}

                                    <ActionList.Divider></ActionList.Divider>
                                    <ActionList.Item role="menuitemcheckbox" selected={sortConfig.mode === eSortMode.ASC}
                                        onSelect={() => {
                                            props.sortConfig?.onValueChanged(sortConfig.field ?? "", eSortMode.ASC)
                                        }}
                                    >
                                        <ActionList.LeadingVisual>
                                            <SortAscIcon />
                                        </ActionList.LeadingVisual>
                                        Ascending
                                    </ActionList.Item>
                                    <ActionList.Item role="menuitemcheckbox" selected={sortConfig.mode === eSortMode.DESC}
                                        onSelect={() => {
                                            props.sortConfig?.onValueChanged(sortConfig.field ?? "", eSortMode.DESC)
                                        }}
                                    >
                                        <ActionList.LeadingVisual>
                                            <SortDescIcon />
                                        </ActionList.LeadingVisual>
                                        Descending
                                    </ActionList.Item>
                                </ActionList>
                            </ActionMenu.Overlay>
                        </ActionMenu>
                    }
                    {exportEnable &&
                        <IconButton icon={DownloadIcon} variant="default" aria-label="Download" />
                    }
                </Table.Actions>

                {(isLoading) &&
                    <Table.Skeleton
                        aria-labelledby={titleId}
                        aria-describedby={subTitileId}
                        columns={columns.map(x => {
                            let item: any = { ...x };
                            return item;
                        })}
                        rows={100}
                    />
                }
                {!isLoading &&
                    <DataTable

                        aria-labelledby={titleId}
                        aria-describedby={subTitileId}
                        data={data}
                        columns={columns.map(x => {
                            let item: any = { ...x };
                            return item;
                        })}
                    // initialSortColumn={2}
                    // initialSortDirection={"DESC"}
                    />
                }
                {paging &&
                    <Table.Pagination
                        // key={searchKeyDelayed}
                        aria-label="Pagination"
                        pageSize={paging.pageSize}
                        totalCount={paging.pageCount}
                        onChange={({ pageIndex }) => {
                            paging.onPageIndexChanged(pageIndex)
                        }}

                        defaultPageIndex={paging.pageIndex}
                    >

                    </Table.Pagination>
                }

            </Table.Container>
        </div>
    );
};

export default DataTableRemotePaging;