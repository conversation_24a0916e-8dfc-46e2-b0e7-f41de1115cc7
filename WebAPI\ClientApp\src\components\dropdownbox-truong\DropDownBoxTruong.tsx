import { TriangleDownIcon, XCircleFillIcon } from "@primer/octicons-react";
import { Box, Button, IconButton, SelectPanel } from "@primer/react";
import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { RootState } from '../../state/reducers';
interface IDropDownTruongProps {
	dm_truong_id?: number;
	value: any[];
	onValueChanged: (dm_truong_ids: any[]) => void;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
	isShowClearButton?: boolean
}

const DropDownTruong = (props: IDropDownTruongProps) => {
	const { translate } = useCommonContext();
	const { language } = useSelector((x: RootState) => x.common);
	const dm_truongs = useSelector((state: RootState) => state.auth.user_info?.campus);
	const { dm_truong_id, value, onValueChanged, stylingMode } = props;
	const [open, setOpen] = useState(false)
	const [filter, setFilter] = useState('')
	const dm_truong_selects = useMemo(() => {
		return dm_truongs?.filter((x) => x.id !== dm_truong_id);
	}, [dm_truongs, dm_truong_id]);
	const dataSource = useMemo(() => {
		if (dm_truong_selects)
			return dm_truong_selects.map(x => ({ id: x.id, text: x.ten_truong }))
		return [];
	}, [dm_truong_selects])
	const filteredItems = useMemo(() => {
		return dataSource.filter(item => item.text.toLowerCase().includes(filter.toLowerCase()))
	}, [dataSource, filter])
	//nếu selected= obj -> chọn 1, = array -> chọn nhiều
	const selecteds = useMemo(() => {
		return dataSource.filter(x => props.value.includes(x.id))
	}, [dataSource, props.value])
	const setSelected = (selecteds: any) => {
		if (selecteds)
			props.onValueChanged(selecteds.map((x: any) => x.id))
	}
	return (
		<>
			<SelectPanel
				renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
					<Box sx={{
						display: "flex"
					}}>
						<Button sx={{ flex: 1 }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
							{children || translate("DropDownTruong.PlaceHolder")}
						</Button>
						{props.isShowClearButton && props.value != undefined && props.value.length > 0 &&
							<IconButton aria-label={"Clear"} icon={XCircleFillIcon}
								variant="invisible"
								onClick={() => {
									props.onValueChanged([])
								}}
							></IconButton>
						}
					</Box>


				)}
				title={""}
				placeholderText="Search"
				open={open}
				onOpenChange={setOpen}
				items={filteredItems}
				selected={selecteds}
				onSelectedChange={setSelected}
				onFilterChange={setFilter}
				showItemDividers={true}
				overlayProps={{ width: 'large', height: 'medium' }}
			/>
			{/* <TagBox
				items={dm_truong_selects}
				showSelectionControls={true}
				value={value}
				stylingMode={stylingMode || 'outlined'}
				displayExpr={language === 'en' ? 'ten_truong_en' : 'ten_truong'}
				valueExpr='id'
                placeholder={translate('DropDownTruong.Label.ChooseSchool')}
				applyValueMode='useButtons'
				onValueChanged={(e) => {
					if (e.event) onValueChanged(e.value);
				}}
			/> */}
		</>
	);
};

export default DropDownTruong;
