import { Box, IconButton, Tooltip } from "@primer/react";
import { ChevronDownIcon, ChevronUpIcon } from "@primer/octicons-react";
import { useLanguage } from "../../hooks/useLanguage";
import { useEffect, useState } from "react";

const data = [
    {
        name: "A",
        description: "<PERSON><PERSON><PERSON> tục thể hiện cam kết, quyết tâm và kiên định cao",
        description_en: "Consistently demonstrates exceptional commitment, dedication, and perseverance"
    },
    {
        name: "B",
        description: "<PERSON> kết và quyết tâm đáng khen ngợi đối với việc học",
        description_en: "Displays commendable commitment and dedication to academic responsibilities"
    },
    {
        name: "C",
        description: "Cam kết và quyết tâm đạt yêu cầu",
        description_en: "Exhibits an adequate level of commitment",
    },
    {
        name: "D",
        description: "<PERSON> kết và nỗ lực hạn chế, dướ<PERSON> mức yêu cầu",
        description_en: "Demonstrates limited commitment and effort, falling short of expectations"
    },
    {
        name: "F",
        description: "Nỗ lực thấp hoặc không nỗ lực trong học tập",
        description_en: "Shows minimal or no effort in meeting academic responsibilities"
    }
]
const EffortGradeDescription = () => {
    const { language } = useLanguage();
    const cacheKey = "EffortGradeDescriptionExpanded";
    const [isExpanded, setIsExpanded] = useState(localStorage.getItem(cacheKey) === "false" ? false : true);
    useEffect(() => {
        localStorage.setItem(cacheKey, isExpanded.toString());
    }, [isExpanded])
    return (
        <Box sx={{
            border: "1px solid",
            borderColor: "border.default",
            pt: 1,
            pl: 2,
            pb: 1,
            pr: 2,
            mb: 2,
            borderRadius: 2,
        }}>
            <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box sx={{ flex: 1, fontWeight: 600 }}>Effort Grade Description</Box>
                <Box>
                    <IconButton aria-label="" icon={isExpanded ? ChevronUpIcon : ChevronDownIcon} variant="invisible"
                        onClick={() => {
                            setIsExpanded(!isExpanded)
                        }}
                    />
                </Box>
            </Box>
            {isExpanded &&
                <Box sx={{ display: "flex", flexWrap: "wrap" }}>
                    {data.map((x, idx) => {
                        return (
                            <Box key={idx} sx={{ display: "flex", mr: 3, mb: 1 }}>
                                <Tooltip text={language === "en" ? x.description_en : x.description}
                                    // direction="e"
                                    sx={{
                                        // whiteSpace: "pre-line",
                                        // width: "200px"
                                    }}>
                                    <Box sx={{ display: "flex", width: "350px" }} className="limit1Line">
                                        <Box sx={{
                                            fontWeight: "600",
                                            mr: 1
                                        }}>{x.name}: </Box>
                                        <Box className="limit1Line">{language === "en" ? x.description_en : x.description}</Box>
                                    </Box>
                                </Tooltip>
                            </Box>
                        );
                    })}
                </Box>
            }
        </Box>
    );
};

export default EffortGradeDescription;