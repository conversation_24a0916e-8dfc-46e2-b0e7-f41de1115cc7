import { UploadIcon } from '@primer/octicons-react';
import { Box } from '@primer/react';
import { useState } from 'react';
import { uploadApi } from '../../api/uploadApi';
import { IUploadRespone } from '../../models/response/upload/IUploadRespone';
import Files from '../files/Files';
import Button from '../ui/button';
interface IFileViewAndUploadProps {
    files: IUploadRespone[]
    onValueChanged: (files: IUploadRespone[]) => void,
    accept?: string
}
const FileViewAndUpload = (props: IFileViewAndUploadProps) => {
    const [isUploading, setIsUploading] = useState(false);

    const handleSelectFile = () => {
        const input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.setAttribute('multiple', 'true');
        input.setAttribute('accept', props.accept ?? 'image/png, image/gif, image/jpeg');
        input.click();
        input.onchange = async () => {
            if (input.files) {
                setIsUploading(true)
                const res = await uploadApi.uploadImages(input.files)
                setIsUploading(false)
                if (res.is_success) {
                    console.log({
                        files: res.data
                    });
                    props.onValueChanged([...props.files, ...res.data.map((x: any) => {
                        return {
                            id: 0,
                            url: x.url,
                            file_extension: x.file_name.split(".").pop(),
                            sort_idx: "",
                            file_name: x.file_name,

                        } as IUploadRespone
                    })]);

                }
            }


        };
    };
    return (
        <Box>
            <Button variant='invisible'
                text='Upload'
                size='medium'
                isLoading={isUploading}
                leadingVisual={UploadIcon}
                onClick={handleSelectFile} />
            <Box>
                <Files
                    files={props.files}
                    onFileRemove={(url) => {
                        props.onValueChanged(props.files.filter(x => x.url !== url))
                    }}
                />
            </Box>
        </Box>
    );
};

export default FileViewAndUpload;