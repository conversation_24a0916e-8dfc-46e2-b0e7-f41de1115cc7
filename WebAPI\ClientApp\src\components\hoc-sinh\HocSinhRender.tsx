import { Box } from "@primer/react";

export interface Infomation {
    title: string;
    data: any;
    icon: any;
    fontWeight?: string | number; // Thêm fontWeight là optional
};

type HocSinhRenderProps = {
    infors: Infomation[],
}

export const HocSinhRender = (props: HocSinhRenderProps) => {
    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                whiteSpace: "nowrap",
                width: "100%",
            }}
        >
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "flex-start",
                }}
            >
                {props.infors.map((info, index) => (
                    <Box key={index} style={{ display: "flex", alignItems: "center", marginBottom: "5px" }}>
                        <Box style={{ color: "#133E87" }}>
                            {info.icon}
                        </Box>
                        {info.title ? (
                            <span style={{ marginLeft: "5px", fontWeight: info.fontWeight }}>
                                {info.title}: {info.data}
                            </span>
                        ) : (
                            <span style={{ marginLeft: "5px", fontWeight: info.fontWeight }}>
                                {info.data}
                            </span>
                        )}
                    </Box>
                ))}
            </Box>
        </Box>
    )
}