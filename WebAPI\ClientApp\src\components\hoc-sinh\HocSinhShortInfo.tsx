import { Box } from "@primer/react";
import PreviewableAvatar from "../hocsinh-avatar-preview/PreviewableAvatar";

interface IHocSinhShortInfoProps {
    data: any
}
const HocSinhShortInfo = (props: IHocSinhShortInfoProps) => {
    const { data } = props;
    return (
        <Box sx={{ display: "flex" }}>
            <Box sx={{ mr: 2, mt: 1 }}>
                <PreviewableAvatar
                    avatarUrl={data.avatar}
                    fullName={data.ho_ten}
                    size="large"
                />
            </Box>
            <Box sx={{ display: "grid", flex: 1 }}>
                <Box sx={{ fontWeight: 600 }}>{data.ho_ten}</Box>
                <Box sx={{ color: "fg.muted" }}>{data.ma_hs}</Box>
            </Box>
        </Box>
    );
};

export default HocSinhShortInfo;