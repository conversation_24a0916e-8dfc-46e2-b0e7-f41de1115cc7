// components/PreviewableAvatar.tsx
import { AnchoredOverlay, Box, Dialog } from '@primer/react';
import React, { useState } from 'react';
import styled from 'styled-components';
import UserAvatar from '../user-avatar';



const PreviewImage = styled.img`
 max-width: 600px;
 max-height: 600px;
 width: 100%;
 height: auto;
 object-fit: contain;
 border-radius: 4px;
 display: block;
 margin: 0 auto;
`;

interface PreviewableAvatarProps {
  avatarUrl?: string;
  fullName: string;
  size?: 'small' | 'medium' | 'large' | 'xlarge';
}

export const PreviewableAvatar: React.FC<PreviewableAvatarProps> = ({
  avatarUrl,
  fullName,
  size = 'xlarge'
}) => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  return (

    <AnchoredOverlay
      open={isPreviewOpen}
      onClose={() => {
        setIsPreviewOpen(false)
      }}
      overlayProps={{
        role: 'dialog',
        'aria-modal': true,
        'aria-label': 'User Card Overlay',

      }}
      focusZoneSettings={{
        disabled: true
      }}
      renderAnchor={(anchorProps) => {

        return (
          <Box
            {...anchorProps}
            onKeyDown={undefined}
            onClick={() => {
              if (avatarUrl)
                setIsPreviewOpen(true)
            }}
            sx={{
              cursor:"pointer"
            }}
          >
            <UserAvatar fullName={fullName} size={size} url={avatarUrl} />
          </Box>
        );
      }}

    >
      <Box onClick={() => {
        setIsPreviewOpen(false)
      }}>
        <PreviewImage src={avatarUrl} alt="Avatar Preview" />
      </Box>


    </AnchoredOverlay >
    
  );
};

export default PreviewableAvatar;