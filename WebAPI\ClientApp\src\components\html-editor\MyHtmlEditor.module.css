/* <PERSON><PERSON><PERSON> bảo dropdown hiển thị đúng vị trí */
.mentionDropdown {
  position: absolute;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 5px 0;
  max-height: 220px; /* Không quá dài */
  overflow-y: auto;
  z-index: 200;
  width: 220px;
  animation: fadeIn 0.2s ease-in-out;
  border: 1px solid #ddd;
}

/* Hiệu ứng hover */
.mentionItem {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.mentionItem:hover {
  background-color: #f5f5f5;
}

/* Hiệu ứng xuất hiện */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.sun-editor {
  min-height: 300px !important; /* <PERSON><PERSON><PERSON> bảo min-height đư<PERSON>c áp dụng */
}