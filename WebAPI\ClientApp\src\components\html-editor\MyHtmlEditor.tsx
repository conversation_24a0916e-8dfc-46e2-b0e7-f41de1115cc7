import React, { useRef, useState } from "react";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css"; // Import Sun Editor's CSS File
import SunEditorCore from "suneditor/src/lib/core";
import { uploadApi } from "../../api/uploadApi";
import styles from "./MyHtmlEditor.module.css"; // Import CSS module
import { Box } from "@primer/react";

const defaultFonts = [
  "Arial",
  "Comic Sans MS",
  "Courier New",
  "Impact",
  "Georgia",
  "Tahoma",
  "Trebuchet MS",
  "Verdana",
];
const sortedFontOptions = [
  "Logical",
  "Salesforce Sans",
  "Garamond",
  "Sans-Serif",
  "Serif",
  "Times New Roman",
  "Helvetica",
  ...defaultFonts,
].sort();

interface IMyHtmlEditorProps {
  value: string;
  onValueChanged: (htmlValue: string) => void;
  height?: number;
}

const mentionsList = ['nam_hoc', 'ma_hoc_sinh', 'ho_ten_hoc_sinh', 'ngay_sinh', 'ten_truong', 'ten_he', 'ten_khoi', 'ten_lop', 'gioi_tinh','ngay', 'thang', 'nam','ho_ten_cha','dien_thoai_cha','email_cha', 'ho_ten_me','dien_thoai_me','email_me'];

const MyHtmlEditor: React.FC<IMyHtmlEditorProps> = (props) => {
  const [showMentions, setShowMentions] = useState(false);
  const [mentionSuggestions, setMentionSuggestions] = useState<string[]>([]);
  const editorRef = useRef<SunEditorCore | null>(null);
  const { value, height, onValueChanged } = props;
  const [dropdownPosition, setDropdownPosition] = useState<any>({ top: 0, left: 0 });

  const getSunEditorInstance = (sunEditor: SunEditorCore) => {
    editorRef.current = sunEditor;
  };

  const handleChange = (content: string) => {
    onValueChanged(content);
  };

  const handleImageUploadBefore = (files: File[], info: object, uploadHandler: Function): any => {
    var file = files ? files[0] : undefined;
    if (file) {
      try {
        uploadApi.uploadImage(file).then(response => {
          if (response && response.is_success) {
            const imageUrl = response?.data?.url;
            if (imageUrl) {
              const responseResult = {
                result: [
                  {
                    url: imageUrl,
                    name: file?.name,
                    size: file?.size,
                  },
                ],
              };
              uploadHandler(responseResult);
            }
          }
        });
      } catch (error) {
        uploadHandler();
      }
    }
  };

  const handleInput = () => {
    debugger

    if (editorRef.current) {
      const content = editorRef.current.getContents(true);
      const div = document.createElement('div');
      div.innerHTML = content;
      const textContent = div.textContent || div.innerText || '';
      const lastAtIndex = textContent.lastIndexOf('@');

      if (lastAtIndex !== -1) {
        const query = textContent.slice(lastAtIndex + 1).toLowerCase();
        const suggestions = mentionsList.filter((mention) =>
          mention.toLowerCase().includes(query)
        );
        setMentionSuggestions(suggestions);
        setShowMentions(true);
        const editorElement = editorRef.current.core.context.element;
        if (editorElement instanceof HTMLElement) {
          const editorBounds = editorElement.getBoundingClientRect();
          const selection = window.getSelection();

          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            const rect = range.getBoundingClientRect();
            setDropdownPosition({
              top: rect.top + rect.height + window.scrollY,
              left: rect.left + window.scrollX,
            });
          }
        }
      } else {
        setShowMentions(false);
      }
    }
  };



  const insertMention = (mention: string) => {
    if (editorRef.current) {
      editorRef.current.insertHTML(mention + " ");
      setShowMentions(false);
    }
  };

  return (
    <React.Fragment>
      {/* <div style={{ width: "100%" }}>
        <SunEditor
          getSunEditorInstance={getSunEditorInstance}
          setContents={value}
          onChange={handleChange}
          onImageUploadBefore={handleImageUploadBefore}
          setOptions={{
            buttonList: [
              ["undo", "redo"],
              ["font", "fontSize"],
              [
                "bold",
                "underline",
                "italic",
                "strike",
              ],
              ["fontColor", "hiliteColor"],
              ["align", "list", "lineHeight"],
              ["outdent", "indent"],
              ["table", "horizontalRule", "link", "image"],
              ["fullScreen", "showBlocks", "codeView"],
              ["removeFormat"],
            ],
            defaultTag: "div",
            height: "auto",
            showPathLabel: false,
            font: sortedFontOptions,
          }}
        />
      </div> */}
      <Box sx={{ overflowY: "auto", width: "100%", position: "relative" }}>
        <SunEditor
          height="auto"
          getSunEditorInstance={getSunEditorInstance}
          setContents={value}
          onChange={(content) => handleChange(content)}
          onImageUploadBefore={(files, info, uploadHandler) =>
            handleImageUploadBefore(files, info, uploadHandler)
          }
          onInput={() => handleInput()}
          setOptions={{
            minHeight: "300px",
            buttonList: [
              ["undo", "redo"],
              ["font", "fontSize"],
              ["bold", "underline", "italic", "strike", "subscript", "superscript"],
              ["fontColor", "hiliteColor"],
              ["align", "list", "lineHeight"],
              ["outdent", "indent"],
              ["table", "horizontalRule", "link", "image"],
              ["preview", "print"],
              ["removeFormat"],
            ],
            defaultTag: "div",
            height: "auto",
            font: sortedFontOptions,
            addTagsWhitelist: "table|td|th",
            attributesWhitelist: {
              table: "style",
              td: "style",
              th: "style",
            },
            showPathLabel: false,
          }}
        />
        {showMentions && mentionSuggestions.length > 0 && (
          <div
            className={styles.mentionDropdown}
            style={{
              top: Math.min(dropdownPosition.top, window.innerHeight - 220), // Không bị vượt màn hình
              left: dropdownPosition.left,
            }}
          >
            {mentionSuggestions.map((mention, index) => (
              <div
                key={index}
                onClick={() => insertMention(mention)}
                className={styles.mentionItem}
              >
                {mention}
              </div>
            ))}
          </div>
        )}
      </Box>

    </React.Fragment>
  );
};

export default MyHtmlEditor;
