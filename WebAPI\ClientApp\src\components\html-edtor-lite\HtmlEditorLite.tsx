import React, { useRef } from "react";

import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css"; // Import Sun Editor's CSS File
import SunEditorCore from "suneditor/src/lib/core";
import styles from "./HtmlEditorLite.module.css";
const defaultFonts = [
  "Arial",
  "Comic Sans MS",
  "Courier New",
  "Impact",
  "Georgia",
  "Tahoma",
  "Trebuchet MS",
  "Verdana",
];
const sortedFontOptions = [
  "Logical",
  "Salesforce Sans",
  "Garamond",
  "Sans-Serif",
  "Serif",
  "Times New Roman",
  "Helvetica",
  ...defaultFonts,
].sort();
interface IHtmlEditorLiteProps {
  value: string;
  onValueChanged: (htmlValue: string) => void;
  height?: number;
  isReadOnly: boolean,
  width?: any
}
const HtmlEditorLite = (props: IHtmlEditorLiteProps) => {
  const { value, height, onValueChanged } = props;
  const editor = useRef<SunEditorCore>();

  const getSunEditorInstance = (sunEditor: SunEditorCore) => {
    editor.current = sunEditor;
  };

  const handleChange = (content: string) => {
    onValueChanged(content);
  };


  return (
    <div className={styles.container} style={{
      width: props.width ?? "100%"
    }}>
      <SunEditor
        readOnly={props.isReadOnly}
        getSunEditorInstance={getSunEditorInstance}
        setContents={value}
        onChange={(content) => handleChange(content)}
        // onImageUploadBefore={(files, info, uploadHandler) =>
        //   handleImageUploadBefore(files, info, uploadHandler)
        // }
        setOptions={{
          mode: "balloon",
          buttonList: [
            ["undo", "redo"],
            // ["font", "fontSize"],
            // ['paragraphStyle', 'blockquote'],
            [
              "bold",
              "underline",
              "italic",
              // "strike",
              // "subscript",
              // "superscript",
            ],
            ["fontColor", "hiliteColor"],
            // ["align", "list", "lineHeight"],
            ["list"],
            // ["outdent", "indent"],
            ["link"],
            // ["table", "horizontalRule", "link", "image", "video"],
            // ['math'] //You must add the 'katex' library at options to use the 'math' plugin.
            // ['imageGallery'], // You must add the "imageGalleryUrl".
            // ["fullScreen", "showBlocks", "codeView"],
            // ["preview", "print"],
            ["removeFormat"],

            // ['save', 'template'],
            // '/', Line break
          ], // Or Array of button list, eg. [['font', 'align'], ['image']]
          defaultTag: "div",
          // minHeight: "300px",
          height: "auto",
          showPathLabel: false,
          font: sortedFontOptions,
        }}
      />
    </div>
  );
};

export default HtmlEditorLite;
