import React from 'react';
import styles from './HtmlEditor.module.css';
interface IHtmlViewProps {
    value: string,
    height?: number,
    onFocusIn?: () => void
}
const HtmlView = (props: IHtmlViewProps) => {
    const { value, height, onFocusIn } = props;
    return (
        <div className={styles.htmlView} onClick={onFocusIn}>
            {/* <HtmlEditor
                height={height ?? window.innerHeight - 150}
                value={value}
                onFocusIn={onFocusIn}
            >
            </HtmlEditor> */}
            <div dangerouslySetInnerHTML={{ __html: value }} />
        </div>
    );
};

export default HtmlView;