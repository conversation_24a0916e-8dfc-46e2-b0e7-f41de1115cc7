.loader {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #ecf0f1;
}

.loader .loader_inner {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
}

.loader span {
    position: absolute;
    width: 40%;
    height: 40%;
    border-radius: 50%;
    animation: loading 2s ease infinite;
}

.loader span:nth-child(1) {
    background: #19a6bc;
    animation-delay: .5s;
}

.loader span:nth-child(2) {
    background: #f63d3a;
    animation-delay: 1s;
}

.loader span:nth-child(3) {
    background: #f0a543;
    animation-delay: 1.5s;
}

.loader span:nth-child(4) {
    background: #193848;
    animation-delay: 2s;
}

@keyframes loading {

    0%,
    100% {
        transform: translate(0);
    }

    25% {
        transform: translate(80px);
    }

    50% {
        transform: translate(80px, 80px);
    }

    75% {
        transform: translate(0, 80px);
    }
}

.loader.fadeOut {
    animation: fadeOut 1s alternate;
}

@keyframes fadeOut {
    to {
        opacity: 0;
    }
}



.logoContainer {
    position: relative;
    display: inline-block;
    overflow: hidden;
    /* Giúp phần ánh sáng không tràn ra ngoài */
}

.logo {
    display: block;
    /* width: 200px; */
    /* Điều chỉnh kích thước theo logo của bạn */
}

.lightOverlay {
    position: absolute;
    top: 0;
    left: 0%;
    /* Bắt đầu từ ngoài khung */
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 100%);
    transform: skewX(-30deg);
    animation: slide-light 1.5s infinite;
}

@keyframes slide-light {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}