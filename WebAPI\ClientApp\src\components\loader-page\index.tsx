import React from 'react'
import styles from "./LoaderPage.module.css"
import { Box } from '@primer/react'
export const LoaderPage = () => {
    return (
        <>
            <div style={{
                height: window.innerHeight - 100,
                display: "flex",
                alignItems: "center",
                width: "100%",
                background: "transparent",
                flexDirection: "column",
                justifyContent: "center"

            }}>
                <Box className={styles.logoContainer}>
                    <img alt='logo' className={styles.logo} style={{ height: "120px" }} src="https://auth.1school.edu.vn/logo.png" />
                    <div className={styles.lightOverlay}></div>
                </Box>

            </div>
        </>
    )
}

export default LoaderPage