
import { FileDirectoryFillIcon, SearchIcon } from "@primer/octicons-react";
import { Box, TextInput, TreeView } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { lopApi } from '../../api/lopApi';
import { ILopTreeViewItemResponse } from '../../models/response/dm-lop/ILopTreeViewItemResponse';
import { RootState } from '../../state/reducers';
import styles from './LopHanhChinhTreeView.module.css';
import { useDebounce } from "use-debounce";
interface ILoppHanhChinhTreeViewProps {
	height?: any;
	nam_hoc: string;
	dm_truong_id: number;
	dm_he_id?: number;
	onSelectionChanged: (lopIds: number[]) => void;
	selectedValue?: number;
	is_add_lop_from_tkb?: boolean
}
const LopHanhChinhTreeView = (props: ILoppHanhChinhTreeViewProps) => {
	const { language } = useSelector((x: RootState) => x.common);
	const [dataSource, setDataSource] = useState<ILopTreeViewItemResponse[]>([]);
	const [searchKey, setSearchKey] = useState<string>("");
	const [searchKeyDelayed] = useDebounce(searchKey, 300)
	useEffect(() => {
		if (props.nam_hoc != '' && props.dm_truong_id > 0) {
			handleReloadDatasource();
		}
	}, [props.nam_hoc, props.dm_truong_id, props.dm_he_id]);
	const handleReloadDatasource = async () => {
		const res = await lopApi.SelectTreeView({
			nam_hoc: props.nam_hoc,
			dm_truong_id: props.dm_truong_id,
			// dm_he_id: 0,
			dm_khoi_id: 0,
			dm_he_id: props.dm_he_id ?? 0,
			is_add_lop_from_tkb: props.is_add_lop_from_tkb ?? false
			// is_show_all: true
		});
		if (res.is_success) {
			setDataSource(res.data);
		}
	};
	// const selectedItemChanged = (e: any) => {
	// 	const treeView = e.component;
	// 	const selectedNodes: ILopTreeViewItemResponse[] = treeView.getSelectedNodes().map((node: any) => node.itemData);
	// 	const lopIds = Array.from(new Set(selectedNodes.map((x) => (x.dm_lop ? x.dm_lop.id : 0)))).filter((x) => x > 0);
	// 	props.onSelectionChanged(lopIds);
	// };
	// const itemRender = (item: ILopTreeViewItemResponse) => {
	// 	return (
	// 		<div style={{ marginLeft: 10 }} className={styles.node}>
	// 			<span>{language === 'en' ? item.text_en : item.text}</span>
	// 		</div>
	// 	);
	// };
	// console.log({
	// 	dataSource
	// });
	const formatedDataSource = useMemo(() => {
		const _khoiNodes = dataSource.filter(x => x.parent_key == null)
		let result: any[] = [];
		_khoiNodes.forEach(khoi => {
			let khoiNode: any = {
				key: khoi.key,
				text: language === "en" ? khoi.text_en : khoi.text,
				items: [],
				isSearchMatch: searchKeyDelayed == ""
			}
			const _heNodes = dataSource.filter(x => x.parent_key == khoi.key);
			_heNodes.forEach(he => {
				const _lopNodes = dataSource.filter(x => x.parent_key == he.key);
				_lopNodes.forEach((lop: any) => {
					const lopNode: any = {
						key: lop.key,
						text: language === "en" ? lop.text_en : lop.text,
						items: [],
						dm_lop_id: lop.dm_lop.id,
						isSearchMatch: lop.text.toLowerCase().includes(searchKeyDelayed) || searchKeyDelayed == ""
					}
					if (lopNode.isSearchMatch)
						khoiNode.items.push(lopNode)
				})
			})
			khoiNode.isSearchMatch = khoiNode.text.toLowerCase().includes(searchKeyDelayed) ||
				khoiNode.items.find((x: any) => x.isSearchMatch)
			if (khoiNode.isSearchMatch)
				result.push(khoiNode)
		})
		return result;
	}, [dataSource, searchKeyDelayed, language])
	// console.log({
	// 	formatedDataSource
	// });

	return (
		<div className={styles.container}>
			{/* 123 */}
			<Box sx={{
				height: props.height ?? window.innerHeight - 150,
				overflow: "scroll",
				pl: 2
			}}>
				<TextInput placeholder='Search' block trailingVisual={SearchIcon} sx={{
					mb: 2
				}}
					onChange={(e) => {
						setSearchKey(e.target.value?.toLocaleLowerCase() ?? "")
					}}
				/>
				<nav aria-label="Class">
					<TreeView aria-label="Class">
						{formatedDataSource.map(khoi => {
							return (
								<TreeView.Item id={khoi.key} key={khoi.key}>
									<TreeView.LeadingVisual>
										<TreeView.DirectoryIcon />
									</TreeView.LeadingVisual>
									{khoi.text}
									<TreeView.SubTree>
										{khoi.items.map((lop: any) => {
											return (
												<TreeView.Item key={lop.key} id={`btnLop_${lop.dm_lop_id}`} onSelect={() => {
													props.onSelectionChanged([lop.dm_lop_id])
												}}

													current={lop.dm_lop_id == props.selectedValue}
												// current={true}
												>
													<TreeView.LeadingVisual>
														<FileDirectoryFillIcon />
													</TreeView.LeadingVisual>
													{lop.text}
												</TreeView.Item>
											)
										})}

									</TreeView.SubTree>
								</TreeView.Item>
							);
						})}

					</TreeView>
				</nav>
			</Box>
			{/* <TreeView
				items={dataSource}
				dataStructure='plain'
                displayExpr={language === 'en' ? 'text_en' : 'text'}
				parentIdExpr='parent_key'
				keyExpr='key'
				searchEnabled={true}
				searchExpr='text'
				height={props.height ? props.height : window.innerHeight - 180}
				onSelectionChanged={selectedItemChanged}
				selectionMode={'multiple'}
				// selectNodesRecursive={true}
				showCheckBoxesMode='normal'
				itemRender={itemRender}
				focusStateEnabled
				selectByClick={true}
				expandNodesRecursive={true}
				onItemClick={(e: any) => {
					e.component.expandItem(e.node.key);
				}}
			/> */}
		</div>
	);
};

export default LopHanhChinhTreeView;
