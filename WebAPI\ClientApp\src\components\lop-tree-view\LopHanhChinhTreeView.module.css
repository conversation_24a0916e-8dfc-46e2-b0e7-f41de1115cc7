.container {
    padding-top: 1rem;
}

.container :global(.dx-treeview-node) {
    margin-top: 0px !important;
    min-height: 25px;
}

.node :global(.dx-dropdownbutton) {
    height: 18px;
}

.node :global(.dx-button-content) {
    padding: 0px !important;
}

.container :global(.dx-treeview-item) {
    padding: 3px 0px;
    min-height: 25px;
    line-height: 13px;
    padding-left: 5px;
    border-radius: 10px;
    margin-left: 0px;
    background-color: transparent !important;
}

.container :global(.dx-state-selected) :global(.dx-treeview-item) {
    background-color: transparent !important;
    /* background-color: rgba(60, 170, 98, 0.1) !important; */
}

.container :global(.dx-treeview-item-with-checkbox .dx-checkbox) {
    top: 5px;
    left: 15px;
}

.container :global(.dx-treeview-toggle-item-visibility) {
    height: 29px;
}

.node {
    /* padding: 3px 8px; */
    /* border-radius: 8px; */
    transition: all 0.2s;
    /* min-height: 25px; */
    margin-left: 0px !important;
    margin-top: 0px !important;
    line-height: 15px;
    display: flex;
    align-items: center;
}

.node span {
    padding: 3px 8px;
    border-radius: 8px;
    /* min-height: 25px; */
}

.node:hover span {
    background-color: rgba(0, 0, 0, 0.09);

}


.container :global(.dx-state-selected) .node {
    color: var(--success) !important;
}

.container :global(.dx-state-selected) .baiTapNode i {
    color: var(--success) !important;
}