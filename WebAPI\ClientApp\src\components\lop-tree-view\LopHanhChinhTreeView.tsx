
import { FileDirectoryIcon, ProjectRoadmapIcon } from "@primer/octicons-react";
import { Box, TreeView } from '@primer/react';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useCommonContext } from "../../contexts/common";
import { ILopSelectedItemRequest } from "../../models/request/danh-muc/ILopSelectedItemRequest";
import { ILopTreeViewItemResponse } from "../../models/response/dm-lop/ILopTreeViewItemResponse";
import { RootState } from '../../state/reducers';
interface ILopHanhChinhTreeViewProps {
	nam_hoc: string;
	dm_truong_id: number;
	dataSource: ILopTreeViewItemResponse[];
	height?: number;
	selectedValue: ILopSelectedItemRequest;
	onSelectionChanged: (data: ILopSelectedItemRequest, ten_lop?: string) => void;
}

const LopHanhChinhTreeView = (props: ILopHanhChinhTreeViewProps) => {
	const { nam_hoc, dataSource, height } = props;
	const { language } = useSelector((x: RootState) => x.common);
	const { checkAccesiableTo } = useCommonContext();
	const formatedDataSource = useMemo(() => {
		const _truongNodes = dataSource.filter(x => x.parent_key == null);
		let result: any[] = [];
		_truongNodes.forEach(truong => {
			let truongNode: any = {
				key: truong.key,
				text: language === "en" ? truong.dm_truong.ten_truong_en : truong.dm_truong.ten_truong,
				items: [],
				dm_truong_id: truong.dm_truong.id
			};
			const _khoiNodes = dataSource.filter(x => x.parent_key === truong.key);
			_khoiNodes.forEach(khoi => {
				let khoiNode: any = {
					key: khoi.key,
					text: language === "en" ? khoi.dm_khoi.ten_khoi_en : khoi.dm_khoi.ten_khoi,
					items: [],
					dm_truong_id: truong.dm_truong.id,
					dm_khoi_id: khoi.dm_khoi.id
				};
				const _heNodes = dataSource.filter(x => x.parent_key == khoi.key);
				_heNodes.forEach(he => {
					let heNode: any = {
						key: he.key,
						text: language === "en" ? he.dm_he.ten_he_en : he.dm_he.ten_he,
						items: [],
						dm_truong_id: truong.dm_truong.id,
						dm_khoi_id: khoi.dm_khoi.id,
						dm_he_id: he.dm_he.id
					};
					const _lopNodes = dataSource.filter(x => x.parent_key === he.key);
					_lopNodes.forEach((lop: ILopTreeViewItemResponse) => {
						const lopNode: any = {
							key: lop.key,
							text: lop.dm_lop?.ten_lop ?? null,
							items: [],
							dm_lop_id: lop.dm_lop?.id ?? null,
							dm_truong_id: truong.dm_truong.id,
							dm_khoi_id: khoi.dm_khoi.id,
							dm_he_id: he.dm_he.id
						};
						heNode.items.push(lopNode);
					});
					khoiNode.items.push(heNode)
				});
				truongNode.items.push(khoiNode);
			});
			result.push(truongNode);
		});
		return result;
	}, [dataSource, language]);


	return (
		<React.Fragment>

			<Box sx={{
				height: props.height ?? window.innerHeight - 150 - 32,
				overflow: "scroll",
				pl: 2,
			}}>
				<nav aria-label="Class">
					<TreeView aria-label="Class">
						{formatedDataSource.map(truong => {
							return (
								<TreeView.Item id={truong.key} key={truong.key} expanded>
									<TreeView.LeadingVisual>
										<TreeView.DirectoryIcon />
									</TreeView.LeadingVisual>
									{truong.text}
									<TreeView.SubTree>
										{truong.items.map((khoi: any) => {
											return (
												<TreeView.Item key={khoi.key} id={`btnKhoi_${khoi.dm_khoi_id}`} onSelect={() => {
													props.onSelectionChanged({
														nam_hoc: nam_hoc,
														dm_truong_id: truong.dm_truong_id,
														dm_khoi_id: khoi.dm_khoi_id,
														dm_he_id: 0,
														dm_lop_id: 0,
													}, khoi.text)
												}}
													current={khoi.dm_khoi_id == props.selectedValue.dm_khoi_id}
												>
													<TreeView.LeadingVisual>
														<FileDirectoryIcon />
													</TreeView.LeadingVisual>
													{khoi.text}
													<TreeView.SubTree>
														{khoi.items.map((he: any) => {
															return (
																<TreeView.Item key={he.key} id={`btnHe_${he.dm_he_id}`} onSelect={() => {
																	props.onSelectionChanged({
																		nam_hoc: nam_hoc,
																		dm_truong_id: truong.dm_truong_id,
																		dm_khoi_id: khoi.dm_khoi_id,
																		dm_he_id: he.dm_he_id,
																		dm_lop_id: 0,
																	}, he.text)
																}}
																	current={he.dm_he_id == props.selectedValue.dm_he_id && khoi.dm_khoi_id == props.selectedValue.dm_khoi_id}
																>
																	<TreeView.LeadingVisual>
																		<FileDirectoryIcon />
																	</TreeView.LeadingVisual>
																	{he.text}
																	<TreeView.SubTree>
																		{he.items.map((lop: any) => {
																			return (
																				<TreeView.Item key={lop.key} id={`btnLop_${lop.dm_lop_id}`} onSelect={() => {
																					const data = {
																						nam_hoc: nam_hoc,
																						dm_truong_id: khoi.dm_truong_id,
																						dm_khoi_id: he.dm_khoi_id,
																						dm_he_id: lop.dm_he_id,
																						dm_lop_id: lop.dm_lop_id,
																					}
																					props.onSelectionChanged(data, lop.text)
																				}}
																					current={he.dm_he_id == props.selectedValue.dm_he_id && khoi.dm_khoi_id == props.selectedValue.dm_khoi_id && lop.dm_lop_id == props.selectedValue.dm_lop_id}
																				>
																					<TreeView.LeadingVisual>
																						<ProjectRoadmapIcon />
																					</TreeView.LeadingVisual>
																					{lop.text}
																				</TreeView.Item>
																			)
																		})}
																	</TreeView.SubTree>
																</TreeView.Item>
															)
														})}
													</TreeView.SubTree>
												</TreeView.Item>
											)
										})}

									</TreeView.SubTree>
								</TreeView.Item>
							);
						})}

					</TreeView>
				</nav>
			</Box>

		</React.Fragment >
	);
};

export default LopHanhChinhTreeView;
