import React from "react";
import Modal from "../ui/modal";


interface Window {
    animationShowPopup(animationOf: string): void;
    animationHidePopup(animationOf: string): void;
}
declare global {
    interface Window {
        animationShowPopup(animationOf: string): void;
        animationHidePopup(animationOf: string): void;
    }
}
type DefaultModalProps = {
    title?: string
    maxWidth?: any,
    minHeight?: number,
    children?: React.ReactNode,
    height?: string,
    onClose?: () => void,
    width?: "large" | "small" | "xlarge" | "medium" | any,
}
type AnimationModalProps = {
    title?: string
    animationOf?: string,
    maxWidth?: any,
    minHeight?: number,
    children?: React.ReactNode,
    height?: string,
    mustClose?: boolean,
    wrapperClass?: string,
    width?: "large" | "small" | "xlarge" | "medium" | any,
    onClose?: () => void

}
export const AnimationPopup = (props: AnimationModalProps) => {
   


    return (
        <Modal onClose={() => {
            if (props.onClose) {
                props.onClose()
            }
        }}
            title={props.title}
            isOpen={true}
            width={props.width ?? "large"}
            height={"auto"}
        >
            {props.children}
        </Modal >
        
    );
}

export const DefaultPopup = (props: DefaultModalProps) => {

    return (
        <Modal onClose={() => {
            if (props.onClose) {
                props.onClose()
            }
        }}
            title={props.title}
            isOpen={true}
            width={props.width ?? "large"}
            height={"auto"}
        >
            {props.children}
        </Modal >
        
    );
}

