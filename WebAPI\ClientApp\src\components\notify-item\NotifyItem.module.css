.container {
    display: flex;
    margin-bottom: 10px;
    border-radius: 10px;
    transition: all 0.1s;
    cursor: pointer;
    padding: 5px;
}

.container:hover {
    /* box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px; */
    background-color: rgba(0, 0, 0, 0.05);
}

.icon img {
    width: 40px;
    margin-right: 10px;
}


.content_container {
    display: block;
    flex: 1;
}

.titile {
    font-weight: 600;
    margin-bottom: 3px;
}

.content {
    margin-bottom: 3px;
    color: rgba(0, 0, 0, 0.7);
    overflow: hidden;
    /* text-overflow: ellipsis;
    max-height: 40px; */
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    display: -webkit-box;

}

.time {
    color: rgba(0, 0, 0, 0.7);
    font-size: 12px;
}

.unread_status {
    display: flex;
    margin-right: 5px;
    margin-left: 5px;
    align-items: center;
    justify-content: center;
}

.unread_dot i {
    color: #1877F2;
    font-size: 8px;
}