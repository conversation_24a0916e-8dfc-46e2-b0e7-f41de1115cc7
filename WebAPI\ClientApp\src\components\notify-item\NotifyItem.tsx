import moment from 'moment';
import React from 'react';
import { INotifyUserItemRespone } from '../../models/response/notify/INotifyUserItemRespone';
import styles from "./NotifyItem.module.css"
interface INotifyItemProps {
    data: INotifyUserItemRespone,
    onClick: () => void
}
const NotifyItem = (props: INotifyItemProps) => {
    return (
        <div className={styles.container} onClick={props.onClick}>
            <div className={styles.icon}>
                <img src='../../images/text.png' />
            </div>
            <div className={styles.content_container}>
                <p className={styles.titile}>{props.data.title}</p>
                <p className={styles.content}>{props.data.content}</p>
                <p className={styles.time}>{moment(props.data.created_at).format('DD/MM/YYYY HH:mm')}</p>
            </div>
            <div className={styles.unread_status}>
                {!props.data.seem_time && <span className={styles.unread_dot}>
                    <i className="fas fa-circle"></i></span>}
            </div>
        </div>
    );
};

export default NotifyItem;