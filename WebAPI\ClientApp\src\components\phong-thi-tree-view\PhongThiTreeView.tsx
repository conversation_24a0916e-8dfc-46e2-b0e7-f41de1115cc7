import React, { useMemo, useState } from 'react';
import { TreeView, Box, TextInput, ActionMenu, ActionList, IconButton } from '@primer/react';
import { FileDirectoryFillIcon, KebabHorizontalIcon, SearchIcon, TriangleRightIcon } from '@primer/octicons-react';
import { useDebounce } from 'use-debounce';

interface TreeItem {
  key: string | number;
  text: string;
  text_en?: string;
  items?: TreeItem[];
  parent_key?: string | number;
  [key: string]: any;
}

interface EnhancedTreeViewProps {
  dataSource: TreeItem[];
  height?: number;
  selectedValue?: string | number;
  onSelectionChanged?: (item: TreeItem) => void;
  onEditClick?: (id: number) => void;
  onDeleteClick?: (id: number) => void;
  isReadOnly?: boolean;
  defaultLanguage?: 'en' | string;
}

const EnhancedTreeView = ({
  dataSource,
  height = window.innerHeight - 200,
  selectedValue,
  onSelectionChanged,
  onEditClick,
  onDeleteClick,
  isReadOnly = false,
  defaultLanguage = 'en'
}: EnhancedTreeViewProps) => {
  const [searchKey, setSearchKey] = useState("");
  const [searchKeyDelayed] = useDebounce(searchKey, 300);

  const formatedDataSource = useMemo(() => {
    const buildTreeData = (items: TreeItem[], parentKey: string | number | null = null): TreeItem[] => {
      return items
        .filter(item => item.parent_key === parentKey)
        .map(item => {
          const children = buildTreeData(items, item.key);
          const isMatch = item.text.toLowerCase().includes(searchKeyDelayed.toLowerCase()) ||
                         (item.text_en?.toLowerCase().includes(searchKeyDelayed.toLowerCase())) ||
                         children.length > 0;

          if (searchKeyDelayed && !isMatch) {
            return null;
          }

          return {
            ...item,
            items: children,
            text: defaultLanguage === 'en' && item.text_en ? item.text_en : item.text
          };
        })
        .filter(Boolean) as TreeItem[];
    };

    return buildTreeData(dataSource);
  }, [dataSource, searchKeyDelayed, defaultLanguage]);

  const hasChildren = (item: TreeItem): boolean => {
    return Boolean(item.items && item.items.length > 0);
  };

  const renderTreeItem = (item: TreeItem, level: number = 0) => {
    const isSelected = item.key === selectedValue;

    return (
      <TreeView.Item
        key={item.key}
        id={item.key.toString()}
        onSelect={() => onSelectionChanged?.(item)}
        current={isSelected}
      >
        <Box 
           sx={{
            display: 'flex',
            alignItems: 'center',
            py: 1,
            pl: isSelected ? 2 : 0,
            bg: isSelected ? 'blue.50' : 'transparent',
            borderLeftWidth: isSelected ? 4 : 0,
          
            '&:hover': { bg: 'gray.100' }
          }}
        >
          <TreeView.LeadingVisual>
            {level === 0 ? (
              <TreeView.DirectoryIcon />
            ) : level === 1 ? (
              <Box sx={{ color: 'blue.500' , mr:'1' }}><FileDirectoryFillIcon /></Box>
            ) : (
              <Box sx={{ color: 'gray.500', mr:'1' }}><TriangleRightIcon /></Box>
            )}
          </TreeView.LeadingVisual>
          
          <Box sx={{ flex: 1, fontWeight: isSelected ? 'bold' : 'normal', ml:'1' }}>
            {item.text}
          </Box>

          {!isReadOnly && level === 2 && (
            <TreeView.TrailingVisual>
              <ActionMenu>
                <ActionMenu.Anchor>
                  <IconButton 
                    icon={KebabHorizontalIcon} 
                    variant="invisible" 
                    aria-label="Open menu"
                    sx={{ opacity: 0, '&:hover': { opacity: 1 } }}
                  />
                </ActionMenu.Anchor>
                <ActionMenu.Overlay>
                  <ActionList>
                    {onEditClick && (
                      <ActionList.Item onSelect={() => onEditClick(item.id)}>
                        Edit
                      </ActionList.Item>
                    )}
                    {onDeleteClick && (
                      <ActionList.Item 
                        variant="danger" 
                        onSelect={() => onDeleteClick(item.id)}
                      >
                        Delete
                      </ActionList.Item>
                    )}
                  </ActionList>
                </ActionMenu.Overlay>
              </ActionMenu>
            </TreeView.TrailingVisual>
          )}
        </Box>

        {hasChildren(item) && (
          <TreeView.SubTree>
            {item.items!.map(subItem => renderTreeItem(subItem, level + 1))}
          </TreeView.SubTree>
        )}
      </TreeView.Item>
    );
  };

  return (
    <Box sx={{ height, overflow: 'auto', px: 2 }}>
      <TextInput
        placeholder="Search"
        trailingVisual={SearchIcon}
        value={searchKey}
        onChange={(e) => setSearchKey(e.target.value)}
        sx={{ mb: 2, mt: 1 }}
        block
      />

      <nav>
        <TreeView aria-label="Tree View">
          {formatedDataSource.map(item => renderTreeItem(item))}
        </TreeView>
      </nav>
    </Box>
  );
};

export default EnhancedTreeView;