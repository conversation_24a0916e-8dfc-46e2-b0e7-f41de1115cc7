import React, { useEffect, useMemo, useState } from "react";
import { Box, TreeView } from "@primer/react";
import { HomeIcon, DotIcon, FileDirectoryIcon, PeopleIcon } from "@primer/octicons-react";
import { useCommonContext } from "../../contexts/common";
import { dm_phong } from "../../models/response/phong/dm_phong";

interface PhongTreeViewProps {
  dataSource: dm_phong[];
  height?: number;
  selectedValue?: { phongId: number | string };
  onSelectionChanged: (phongId: number | string, ten_phong: string) => void;
}

// Define an interface for our tree nodes to ensure type consistency
interface TreeNode {
  key: string;
  text: string;
  roomIds?: string;
  items: {
    key: string;
    id: number;
    text: string;
  }[];
}

const PhongTreeView: React.FC<PhongTreeViewProps> = ({
  dataSource,
  height,
  selectedValue,
  onSelectionChanged,
}) => {
  const { translate } = useCommonContext();

  const formattedData = useMemo(() => {
    const unassignedRoom: TreeNode = {
      key: 'unassigned',
      text: 'Chưa xếp phòng',
      roomIds: '',
      items: [],
    };

    const buildings = Array.from(new Set(dataSource.map((x) => x.toa)));
    const buildingNodes: TreeNode[] = buildings.map((toa) => {
      // Get all room IDs for this building
      const roomsInBuilding = dataSource.filter((phong) => phong.toa === toa);
      
      return {
        key: `toa-${toa}`,
        text: toa,
        // Store room IDs as comma-separated string for easier passing
        roomIds: roomsInBuilding.map(room => room.id).join(','),
        items: roomsInBuilding.map((phong) => ({
          key: `phong-${phong.id}`,
          id: phong.id,
          text: phong.ten_phong,
        })),
      };
    });

    return [unassignedRoom, ...buildingNodes];
  }, [dataSource]);

  // Custom CSS to make the entire row clickable
  // Apply this as a style tag in the component
  React.useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = `
      [data-component="TreeViewItem"] > [data-component="TreeViewItemContent"] {
        cursor: pointer !important;
        width: 100% !important;
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <Box
      sx={{
        height: height ?? "calc(100vh - 182px)",
        overflow: "auto",
        pl: 2,
      }}
    >
      <nav aria-label="Rooms">
        <TreeView aria-label="Rooms">
          {formattedData.map((building) => {
            // Check if this building is currently selected
            const isBuildingSelected = typeof selectedValue?.phongId === 'string' && 
            selectedValue.phongId.startsWith('toa:') && 
            selectedValue.phongId === `toa:${building.roomIds}`;
            
            // Check if "unassigned" is selected
            const isUnassignedSelected = building.key === 'unassigned' && 
                                         selectedValue?.phongId === -1;
                                         
            return (
              <TreeView.Item 
                key={building.key} 
                id={building.key} 
                defaultExpanded
                current={isBuildingSelected || isUnassignedSelected}
                onSelect={() => {
                  if (building.key === 'unassigned') {
                    onSelectionChanged(-1, 'Chưa xếp phòng');
                  } else if (building.key.startsWith('toa-')) {
                    onSelectionChanged(
                      `toa:${building.roomIds}`, 
                      `Tòa ${building.text}`
                    );
                  }
                }}
              >
                <TreeView.LeadingVisual>
                  {building.key === 'unassigned' ? <PeopleIcon /> : <TreeView.DirectoryIcon />}
                </TreeView.LeadingVisual>
                <Box
                  as="span"
                  sx={{ 
                    fontWeight: (isBuildingSelected || isUnassignedSelected) ? 'bold' : 'normal',
                    width: "100%",
                  }}
                >
                  {building.text}
                </Box>
                {building.items.length > 0 && (
                  <TreeView.SubTree>
                    {building.items.map((room) => (
                      <TreeView.Item
                        key={room.key}
                        id={room.key}
                        onSelect={() => onSelectionChanged(room.id, room.text)}
                        current={room.id === selectedValue?.phongId}
                      >
                        <TreeView.LeadingVisual>
                          <FileDirectoryIcon />
                        </TreeView.LeadingVisual>
                        {room.text}
                      </TreeView.Item>
                    ))}
                  </TreeView.SubTree>
                )}
              </TreeView.Item>
            );
          })}
        </TreeView>
      </nav>
    </Box>
  );
};

export default PhongTreeView;