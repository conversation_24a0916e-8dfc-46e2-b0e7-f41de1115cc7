.container {
    box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
    padding: 6px 10px;
    border-radius: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    margin-left: 1px;
    margin-right: 1px;
}
.container.isSelected {
    box-shadow: rgba(3, 102, 214, 0.3) 0px 0px 0px 3px;
}
.caption {
    font-size: 15px;
    font-weight: bolder;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
.caption .name{
    flex: 1;
}
.info_container{
    margin-bottom: 5px;
    font-size: 14px;
    display: flex;
    align-items: center;
}
.info_container p{
    margin-bottom: 0px;
    margin-right: 10px;
}
.info_container i{
    /* min-width: 20px; */
    text-align: left;
    margin-right: 5px;
}
.tag{
    font-size: 11px;
    font-weight: 400;
    padding: 0px 6px;
    background-color: #0083B5;
    border-radius: 5px;
    align-items: center;
    color: white;
}