import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Box, 
  Text, 
  IconButton, 
  ActionMenu,
  ActionList,
} from '@primer/react';
import { 
  KebabHorizontalIcon,
  PersonIcon,
  ChevronRightIcon,
  TrashIcon,
  BookIcon,
  TasklistIcon
} from '@primer/octicons-react';
import { useCommonContext } from '../../contexts/common';
import UserAvatar from '../user-avatar';

interface HocSinh {
  id: number;
  ho_ten: string;
  ma_hs: string;
  trang_thai: string;
  dm_gioitinh_id: number;
  gioi_tinh: string;
  ten_he: string;
  ten_lop: string;
}

interface PhuHuynhChildItemProps {
  hocSinh: HocSinh;
  isSelected: boolean;
  onSelected: () => void;
  onDeleted: () => void;
}

const PhuHuynhChildItem: React.FC<PhuHuynhChildItemProps> = ({
  hocSinh,
  isSelected,
  onSelected,
  onDeleted
}) => {
  const { translate } = useCommonContext();

  return (
    <ActionList showDividers>
      <ActionList.Item
        sx={{
          py: 2,
          px: 3,
          backgroundColor: 'canvas.subtle',
          '&:hover': {
            backgroundColor: 'canvas.subtle',
          },
        }}
        //onClick={onSelected}
      >
        <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
          {/* Main Content */}
          <Box sx={{ flex: 1 }}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
                mb: 1,
              }}
            >
              <Text fontWeight="bold" fontSize={2}>
                {hocSinh.ho_ten}
              </Text>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Text
                  as="span"
                  sx={{
                    px: 2,
                    py: 1,
                    borderRadius: 2,
                    fontSize: 0,
                    backgroundColor: 'sponsors.muted',
                    color: 'sponsors.fg',
                    fontWeight: "bold",
                  }}
                >
                  Học sinh
                </Text>
              </Box>
            </Box>
  
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box sx={{ mr: 1, color: "fg.muted" }}>
                  <PersonIcon size={15} />
                </Box>
                <Text fontSize={1}>{hocSinh.ma_hs} - {hocSinh.ten_he} - {hocSinh.ten_lop}</Text>
              </Box>
            </Box>
          </Box>         
         
        </Box>
      </ActionList.Item>
    </ActionList>
  );
};

export default PhuHuynhChildItem;