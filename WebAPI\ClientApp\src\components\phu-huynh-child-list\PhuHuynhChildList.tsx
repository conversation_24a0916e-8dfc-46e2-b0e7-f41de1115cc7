import React, { useEffect, useRef, useState } from "react";
import PhuHuynhChildItem from "../phu-huynh-child-item";
import styles from "./PhuHuynhChildList.module.css";
import { hocSinhApi } from "../../api/hocSinhApi";
import { IHocSinh } from "../../models/response/crm-hocsinh/IHocsinh";

interface PhuHuynhChildListProps {
  id: number;
  hocSinhSelectedId: number;
  mode?: "column" | string;
  onSelected: (hocSinhId: number) => void;
  onDeleted: (phuHuynhId: number, hocSinhId: number) => void;
  countToRerender?: number;
}

const PhuHuynhChildList: React.FC<PhuHuynhChildListProps> = ({
  id,
  hocSinhSelectedId,
  onSelected,
  countToRerender = 0,
  mode,
  onDeleted,
}) => {
  const [hocSinhs, setHocSinhs] = useState<IHocSinh[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const componentDidMount = useRef<boolean>(false);

  const handleReloadHocSinh = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const res = await hocSinhApi.SelectByParent(id);

      if (componentDidMount.current) {
        if (res.is_success) {
          setHocSinhs(res.data);
        }
      }
    } catch (error) {
      console.error("Error loading hoc sinh:", error);
    } finally {
      if (componentDidMount.current) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    componentDidMount.current = true;
    handleReloadHocSinh();

    return () => {
      componentDidMount.current = false;
    };
  }, [id, countToRerender]);

  const renderColumnMode = () => (
    <div className={styles.list_horizontal}>
      {hocSinhs.map((hocSinh, idx) => (
        <div
          key={`${hocSinh.id}-${idx}`}
          className={styles.list_horizontal_item}
        >
          <PhuHuynhChildItem
            hocSinh={hocSinh}
            isSelected={hocSinhSelectedId === hocSinh.id}
            onSelected={() => onSelected(hocSinh.id)}
            onDeleted={() => onDeleted(id, hocSinh.id)}
          />
        </div>
      ))}
    </div>
  );

  const renderListMode = () => (
    <>
      {
        <>
          {hocSinhs.map((hocSinh, idx) => (
            <PhuHuynhChildItem
              key={`${hocSinh.id}-${idx}`}
              hocSinh={hocSinh}
              isSelected={hocSinhSelectedId === hocSinh.id}
              onSelected={() => onSelected(hocSinh.id)}
              onDeleted={() => onDeleted(id, hocSinh.id)}
            />
          ))}
        </>
      }
    </>
  );

  return <div>{mode === "column" ? renderColumnMode() : renderListMode()}</div>;
};

export default PhuHuynhChildList;
