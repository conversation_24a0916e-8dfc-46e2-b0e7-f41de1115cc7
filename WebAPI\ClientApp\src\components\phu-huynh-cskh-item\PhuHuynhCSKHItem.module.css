.container {
    padding: 0.5rem 1rem;
    background-color: rgba(0, 0, 0, 0.04);
    margin-bottom: 10px;
    border-radius: 10px;
}

.container.done {
    /* background-color: #d1e7dd; */
    /* color: #0f5132; */
    border: #badbcc;
}

.container.pending {
    background-color: #f8d7da;
    color: #842029;
    border: #f5c2c7;
}

.header {
    display: flex;
    align-items: center;
}

.noi_dung {
    /* font-weight: bold; */
    /* font-size: 15px; */
    flex: 1;
}

.khach_hang_info {
    display: flex;
    margin-bottom: 5px;
}

.info_container {
    margin-right: 8px;
}

.info_container i {
    margin-right: 5px;
}

.trang_thai {
    background-color: var(--action-color);
    color: #fff;
    padding: 0px 10px;
    border-radius: 10px;
    margin-right: 10px;
}

.trang_thai.da_xu_ly {
    background-color: rgba(0, 0, 0, 0.05);
    color: #000;
}

.actions button {
    margin-left: 5px;
}