import React, { useState } from "react";
import { Box, Label, Text, IconButton, Tooltip, Octicon } from "@primer/react";
import {
  PencilIcon,
  PersonAddIcon,
  CheckIcon,
  ClockIcon,
  CommentDiscussionIcon,
  TagIcon,
  NoteIcon,
  InfoIcon,
} from "@primer/octicons-react";
import moment from "moment";
import PhuHuynhCSKHDetailModal from "./detail-modal/PhuHuynhCSKHDetailModal";

interface ChamSocKhachHang {
  id: number;
  da_xu_ly: boolean;
  ten_khach_hang: string;
  dien_thoai: string;
  ma_hs?: string;
  ho_ten_hoc_sinh?: string;
  is_request: boolean;
  ngay_hen_tra_loi?: string;
  ngay_xu_ly?: string;
  noi_dung: string;
  nguoi_tao: string;
  ngay_tao: string;
  ghi_chu?: string;
  ket_qua_xu_ly?: string;
}

interface PhuHuynhCSKHItemProps {
  ts_chamsockhachhang: ChamSocKhachHang;
  onChanged: () => void;
}

const PhuHuynhCSKHItem: React.FC<PhuHuynhCSKHItemProps> = ({
  ts_chamsockhachhang,
  onChanged,
}) => {
  const [isShowEditModal, setShowEditModal] = useState<boolean>(false);
  const { da_xu_ly, is_request } = ts_chamsockhachhang;

  // Format dates with time for processing date
  const formatDate = (dateString?: string, includeTime = false) => {
    if (!dateString) return "";
    return includeTime 
      ? moment(dateString).format("DD/MM/YYYY HH:mm") 
      : moment(dateString).format("DD/MM/YYYY");
  };

  const statusColor = da_xu_ly ? '#22863a' : '#d73a49';

  return (
    <Box
      sx={{
        border: "1px solid",
        borderColor: "border.default",
        borderRadius: 2,
        borderLeft: `3px solid ${statusColor}`,
        background: "white",
        boxShadow: "0 1px 2px rgba(0,0,0,0.05)",
        mb: 2,
        fontSize: 1,
        overflow: "hidden",
      }}
    >
      {/* Header - Compact and informative */}
      <Box 
        sx={{ 
          p: 2,
          borderBottom: "1px solid",
          borderColor: "border.muted",
          bg: "canvas.subtle",
          display: "flex", 
          justifyContent: "space-between", 
          alignItems: "center",
        }}
      >
        <Box sx={{ display: "flex", gap: 2, alignItems: "center", flexWrap: "wrap" }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <span style={{
              fontSize: '13px',
              fontWeight: 500,
              color: statusColor,
            }}>
              {da_xu_ly ? 'Đã xử lý' : 'Chưa xử lý'}
            </span>
          </Box>
          
          <Text sx={{ color: "fg.muted" }}>|</Text>
          
          <Label 
            variant={is_request ? "attention" : "accent"}
            sx={{ fontWeight: "medium" }}
          >
            {is_request ? "Yêu cầu PH" : "Ghi chú TS"}
          </Label>
          
          {ts_chamsockhachhang.ma_hs && (
            <>
              <Text sx={{ color: "fg.muted" }}>|</Text>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <PersonAddIcon size={14} />
                <Box sx={{ ml: 1, display: "flex", alignItems: "center" }}>
                  <Text sx={{ fontWeight: "medium" }}>{ts_chamsockhachhang.ma_hs}</Text>
                  {ts_chamsockhachhang.ho_ten_hoc_sinh && (
                    <Text sx={{ ml: 1, color: "#0969da" }}>
                      - {ts_chamsockhachhang.ho_ten_hoc_sinh}
                    </Text>
                  )}
                </Box>
              </Box>
            </>
          )}
        </Box>

        <IconButton
          icon={PencilIcon}
          aria-label="Chỉnh sửa"
          variant="invisible"
          size="small"
          sx={{
            color: "fg.muted",
            "&:hover": { color: "accent.fg" }
          }}
          onClick={() => setShowEditModal(true)}
        />
      </Box>

      {/* Content - More compact layout */}
      <Box sx={{ p: 2 }}>
        {/* Nội dung chính */}
        <Box 
          sx={{ 
            mb: ts_chamsockhachhang.ghi_chu ? 2 : 0,
            display: "flex",
            gap: 2,
          }}
        >
          <Box sx={{ color: "accent.fg", flexShrink: 0, mt: "2px" }}>
            <CommentDiscussionIcon size={16} />
          </Box>
          <Text sx={{ lineHeight: 1.5 }}>{ts_chamsockhachhang.noi_dung}</Text>
        </Box>

        {/* Ghi chú - Compact version */}
        {ts_chamsockhachhang.ghi_chu && (
          <Box 
            sx={{ 
              display: "flex", 
              gap: 2, 
              mt: 2,
              fontSize: 0,
              color: "fg.muted",
              borderTop: "1px dashed",
              borderColor: "border.muted",
              pt: 2,
            }}
          >
            <Box sx={{ flexShrink: 0, mt: "2px" }}>
              <NoteIcon size={14} />
            </Box>
            <Text sx={{ fontStyle: "italic" }}>
              {ts_chamsockhachhang.ghi_chu}
            </Text>
          </Box>
        )}

        {/* Additional info - Inline format */}
        {(ts_chamsockhachhang.ngay_hen_tra_loi || 
          ts_chamsockhachhang.ngay_xu_ly || 
          ts_chamsockhachhang.ket_qua_xu_ly) && (
          <Box
            sx={{
              display: "flex",
              flexWrap: "wrap",
              gap: 3,
              borderTop: "1px solid",
              borderColor: "border.muted",
              pt: 2,
              mt: 2,
              fontSize: 0,
              color: "fg.muted",
            }}
          >
            {ts_chamsockhachhang.ngay_hen_tra_loi && (
              <Tooltip aria-label="Ngày hẹn xử lý">
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <ClockIcon size={14} />
                  <Text>{formatDate(ts_chamsockhachhang.ngay_hen_tra_loi)}</Text>
                </Box>
              </Tooltip>
            )}

            {ts_chamsockhachhang.ngay_xu_ly && (
              <Tooltip aria-label="Ngày đã xử lý">
                <Box sx={{ display: "flex", alignItems: "center", gap: 1, color: "success.fg" }}>
                  <CheckIcon size={14} />
                  <Text>{formatDate(ts_chamsockhachhang.ngay_xu_ly, true)}</Text>
                </Box>
              </Tooltip>
            )}

            {ts_chamsockhachhang.ket_qua_xu_ly && (
              <Tooltip aria-label="Kết quả xử lý">
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <TagIcon size={14} />
                  <Text fontWeight="bold">{ts_chamsockhachhang.ket_qua_xu_ly}</Text>
                </Box>
              </Tooltip>
            )}

            <Box sx={{ marginLeft: "auto", display: "flex", alignItems: "center", gap: 1 }}>
              {/* <InfoIcon size={14} />
              <Text>{ts_chamsockhachhang.nguoi_tao}</Text> */}
            </Box>
          </Box>
        )}
      </Box>

      {isShowEditModal && (
        <PhuHuynhCSKHDetailModal
          id={ts_chamsockhachhang.id}
          onCancel={() => setShowEditModal(false)}
          onSuccess={() => {
            setShowEditModal(false);
            onChanged();
          }}
        />
      )}
    </Box>
  );
};

export default PhuHuynhCSKHItem;