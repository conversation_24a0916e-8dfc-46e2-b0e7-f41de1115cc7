import React, { useEffect, useState } from "react";
import {
  Dialog,
  Box,
  Text,
  TextInput,
  FormControl,
  Button,
  ButtonGroup,
  Label,
  Checkbox,
} from "@primer/react";
import {
  CheckIcon,
  XIcon,
  ClockIcon,
  PersonIcon,
  NoteIcon,
  CalendarIcon,
  BookIcon,
} from "@primer/octicons-react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import moment from "moment";
import { chamSocKhachHangApi } from "../../../api/chamSocKhachHangApi";
import { NotifyHelper } from "../../../helpers/toast";
import { useCommonContext } from "../../../contexts/common";
import { ts_chamsockhachhang } from "../../../models/response/cskh/ts_chamsockhachhang";

interface PhuHuynhCSKHDetailModalProps {
  id: number;
  onCancel: () => void;
  onSuccess: () => void;
}

interface FormData extends ts_chamsockhachhang {
  ngay_xu_ly_text?: string;
  ngay_hen_tra_loi_text?: string;
}

const defaultValues: FormData = {
  id: 0,
  ten_khach_hang: "",
  dien_thoai: "",
  noi_dung: "",
  da_xu_ly: false,
  is_request: false,
  ts_hocsinh_id: 0,
  user_xu_ly_id: 0,
  ts_phuhuynh_id: 0,
  ts_hoso_survey_id: 0,
  ngay_xu_ly: null,
  ngay_hen_tra_loi: null,
  ket_qua_xu_ly: "",
  ghi_chu: "",
  ngay_hen_tra_loi_text: "",
  //nguoi_tao: '',
  //ngay_tao: '',
};

const PhuHuynhCSKHDetailModal: React.FC<PhuHuynhCSKHDetailModalProps> = ({
  id,
  onCancel,
  onSuccess,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { translate } = useCommonContext();

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isDirty },
  } = useForm<FormData>({
    defaultValues,
  });

  const daXuLy = watch("da_xu_ly");

  useEffect(() => {
    if (id) {
      loadData();
    }
  }, [id]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const res = await chamSocKhachHangApi.detail(id);
      if (res.is_success && res.data) {
        const formattedData = {
          ...res.data,
          ngay_xu_ly_text: res.data.ngay_xu_ly
            ? moment(res.data.ngay_xu_ly).format("YYYY-MM-DDTHH:mm")
            : undefined,
          ngay_hen_tra_loi_text: res.data.ngay_hen_tra_loi
            ? moment(res.data.ngay_hen_tra_loi).format("YYYY-MM-DDTHH:mm")
            : undefined,
        };
        reset(formattedData);
      }
    } catch (error) {
      NotifyHelper.Error("Có lỗi khi tải dữ liệu");
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
  
      const requestData: ts_chamsockhachhang = {
        ...data,
        ts_hocsinh_id: data.ts_hocsinh_id || 0,
        user_xu_ly_id: data.user_xu_ly_id || 0,
        ts_phuhuynh_id: data.ts_phuhuynh_id || 0,
        ts_hoso_survey_id: data.ts_hoso_survey_id || 0,
        // Explicitly convert to ISO string or null
        ngay_xu_ly: data.ngay_xu_ly ? new Date(data.ngay_xu_ly).toISOString() : null,
        ngay_hen_tra_loi: data.ngay_hen_tra_loi ? new Date(data.ngay_hen_tra_loi).toISOString() : null,
      };
  
      const res = await chamSocKhachHangApi.update(requestData);
      
      if (res.is_success) {
        NotifyHelper.Success("Cập nhật thành công");
        onSuccess();
      } else {
        NotifyHelper.Error(res.message || "Cập nhật không thành công");
      }
    } catch (error) {
      NotifyHelper.Error("Có lỗi xảy ra khi cập nhật");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
    isOpen={true}
    onDismiss={onCancel}
    aria-labelledby="header-id"
    sx={{ 
      width: "100%", 
      maxWidth: "600px",
      maxHeight: "90vh", // Giới hạn chiều cao tối đa của modal
      display: "flex",
      flexDirection: "column" 
    }}
  >
      <Dialog.Header id="header-id">
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          {id > 0 ? <NoteIcon size={16} /> : <CheckIcon size={16} />}
          <Text sx={{ fontSize: 2, fontWeight: "bold" }}>
            {id > 0
              ? translate("Cập nhật yêu cầu")
              : translate("Thêm mới yêu cầu")}
          </Text>
        </Box>
      </Dialog.Header>

      <Box  sx={{ 
          flexGrow: 1, 
          overflowY: "auto", // Cho phép cuộn theo chiều dọc
          padding: 3 
        }}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3}}>
            {/* Customer Info Section */}
            <Box>
              <Controller
                name="ten_khach_hang"
                control={control}
                rules={{ required: "Vui lòng nhập tên khách hàng" }}
                render={({ field }) => (
                  <FormControl>
                    <FormControl.Label>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 2 }}
                      >
                        <PersonIcon size={14} />
                        <Text>Tên khách hàng</Text>
                      </Box>
                    </FormControl.Label>
                    <TextInput
                      {...field}
                      block
                      aria-label="Tên khách hàng"
                      validationStatus={
                        errors.ten_khach_hang ? "error" : undefined
                      }
                    />
                    {errors.ten_khach_hang && (
                      <FormControl.Validation variant="error">
                        {errors.ten_khach_hang.message}
                      </FormControl.Validation>
                    )}
                  </FormControl>
                )}
              />
            </Box>

            <Box>
              <Controller
                name="dien_thoai"
                control={control}
                rules={{
                  required: "Vui lòng nhập số điện thoại",
                  pattern: {
                    value: /^[0-9]{10,11}$/,
                    message: "Số điện thoại không hợp lệ",
                  },
                }}
                render={({ field }) => (
                  <FormControl>
                    <FormControl.Label>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 2 }}
                      >
                        <ClockIcon size={14} />
                        <Text>Điện thoại</Text>
                      </Box>
                    </FormControl.Label>
                    <TextInput
                      {...field}
                      block
                      aria-label="Điện thoại"
                      validationStatus={errors.dien_thoai ? "error" : undefined}
                    />
                    {errors.dien_thoai && (
                      <FormControl.Validation variant="error">
                        {errors.dien_thoai.message}
                      </FormControl.Validation>
                    )}
                  </FormControl>
                )}
              />
            </Box>

            {/* Content Section */}
            <Box>
              <Controller
                name="noi_dung"
                control={control}
                rules={{ required: "Vui lòng nhập nội dung" }}
                render={({ field }) => (
                  <FormControl>
                    <FormControl.Label>Nội dung</FormControl.Label>
                    <TextInput
                      {...field}
                      block
                      as="textarea"
                      rows={4}
                      aria-label="Nội dung"
                      validationStatus={errors.noi_dung ? "error" : undefined}
                    />
                    {errors.noi_dung && (
                      <FormControl.Validation variant="error">
                        {errors.noi_dung.message}
                      </FormControl.Validation>
                    )}
                  </FormControl>
                )}
              />
            </Box>

            {/* Status Section */}
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Controller
                name="da_xu_ly"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <FormControl>
                    <Checkbox
                      checked={value}
                      onChange={(e) => onChange(e.target.checked)}
                    />
                    <FormControl.Label sx={{ ml: 2 }}>
                      <Label
                        variant={value ? "success" : "attention"}
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        {value ? (
                          <CheckIcon size={14} />
                        ) : (
                          <ClockIcon size={14} />
                        )}
                        <Text>{value ? "Đã xử lý" : "Chưa xử lý"}</Text>
                      </Label>
                    </FormControl.Label>
                  </FormControl>
                )}
              />
            </Box>

            {/* Processing Result Section */}
            {daXuLy && (
              <>
                <Box>
                  <Controller
                    name="ngay_xu_ly"
                    control={control}
                    rules={{ required: "Vui lòng chọn ngày xử lý" }}
                    render={({ field: { value, onChange, ...rest } }) => (
                      <FormControl>
                        <FormControl.Label>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 2,
                            }}
                          >
                            <CalendarIcon size={14} />
                            <Text>Ngày xử lý</Text>
                          </Box>
                        </FormControl.Label>
                        <TextInput
                          {...rest}
                          type="datetime-local"
                          block
                          aria-label="Ngày xử lý"
                          value={
                            value
                              ? moment(value).format("YYYY-MM-DDTHH:mm")
                              : ""
                          }
                          onChange={(e) => {
                            onChange(
                              e.target.value
                                ? moment(e.target.value).toDate()
                                : null
                            );
                          }}
                          validationStatus={
                            errors.ngay_xu_ly ? "error" : undefined
                          }
                        />
                        {errors.ngay_xu_ly && (
                          <FormControl.Validation variant="error">
                            {errors.ngay_xu_ly.message}
                          </FormControl.Validation>
                        )}
                      </FormControl>
                    )}
                  />
                </Box>

                <Box>
                  <Controller
                    name="ket_qua_xu_ly"
                    control={control}
                    rules={{ required: "Vui lòng nhập kết quả xử lý" }}
                    render={({ field }) => (
                      <FormControl>
                        <FormControl.Label>Kết quả xử lý</FormControl.Label>
                        <TextInput
                          {...field}
                          block
                          as="textarea"
                          rows={4}
                          aria-label="Kết quả xử lý"
                          validationStatus={
                            errors.ket_qua_xu_ly ? "error" : undefined
                          }
                        />
                        {errors.ket_qua_xu_ly && (
                          <FormControl.Validation variant="error">
                            {errors.ket_qua_xu_ly.message}
                          </FormControl.Validation>
                        )}
                      </FormControl>
                    )}
                  />
                </Box>
              </>
            )}

            {/* Additional Info Section */}
            <Box>
              <Controller
                name="ghi_chu"
                control={control}
                render={({ field }) => (
                  <FormControl>
                    <FormControl.Label>Ghi chú</FormControl.Label>
                    <TextInput
                      {...field}
                      block
                      as="textarea"
                      rows={3}
                      aria-label="Ghi chú"
                    />
                  </FormControl>
                )}
              />
            </Box>

            <Box>
              <Controller
                name="ngay_hen_tra_loi"
                control={control}
                render={({ field: { value, onChange, ...rest } }) => (
                  <FormControl>
                    <FormControl.Label>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 2 }}
                      >
                        <CalendarIcon size={14} />
                        <Text>Ngày hẹn trả lời</Text>
                      </Box>
                    </FormControl.Label>
                    <TextInput
                      {...rest}
                      type="datetime-local"
                      block
                      aria-label="Ngày hẹn trả lời"
                      value={
                        value ? moment(value).format("YYYY-MM-DDTHH:mm") : ""
                      }
                      onChange={(e) => {
                        onChange(
                          e.target.value
                            ? moment(e.target.value).toDate()
                            : null
                        );
                      }}
                    />
                  </FormControl>
                )}
              />
            </Box>
          </Box>
        </form>
      </Box>

      <Box
        sx={{
          p: 3,
          borderTop: "1px solid",
          borderColor: "border.default",
          bg: "canvas.subtle",
          display: "flex",
          justifyContent: "flex-end",
          gap: 2,
        }}
      >
        <ButtonGroup>
          <Button variant="danger" onClick={onCancel} disabled={isLoading}>
            <XIcon size={16} />
            <Text sx={{ ml: 1 }}>Đóng</Text>
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit(onSubmit)}
            disabled={isLoading || !isDirty}
            sx={{
              bg: "success.emphasis",
              "&:hover:not([disabled])": {
                bg: "success.fg",
              },
            }}
          >
            {isLoading ? (
              <>
                <ClockIcon size={16} />
                <Text sx={{ ml: 1 }}>Đang xử lý...</Text>
              </>
            ) : (
              <>
                <CheckIcon size={16} />
                <Text sx={{ ml: 1 }}>{id > 0 ? "Cập nhật" : "Thêm mới"}</Text>
              </>
            )}
          </Button>
        </ButtonGroup>
      </Box>
    </Dialog>
  );
};

export default PhuHuynhCSKHDetailModal;
