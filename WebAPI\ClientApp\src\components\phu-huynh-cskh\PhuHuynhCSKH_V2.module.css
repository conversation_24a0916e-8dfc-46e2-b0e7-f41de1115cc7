.container_form {
    background-color: #ffff;
    box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
    padding: 20px;
    border-radius: 20px;
}

.container {
    margin-top: 10px;
    box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
    padding: 20px;
    padding-top: 10px;
    border-radius: 20px;
    /* display: flex; */
}

/* .container div {
    flex: 1;
} */

.tab_content {
    margin-top: 20px;
    margin-right: 20px;
}

.container :global(.dx-tab) {
    background-color: #fff !important;
}

.container :global(.dx-tab-text) {
    display: flex;
    align-items: center;
}

.container :global(.dx-tab-text i) {
    margin-right: 10px;
}

.tabs {
    display: flex;
    margin-top: 6px;
}

.tab {
    flex: 1;
    margin: 5px;
    box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
    border-radius: 20px;
    padding: 20px;


}

.tab_caption {
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 10px;
}