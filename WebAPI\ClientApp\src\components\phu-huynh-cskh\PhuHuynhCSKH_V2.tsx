import React, { useState } from "react";
import { TabNav, UnderlineNav } from "@primer/react";
import { FileDirectoryIcon, FileDirectoryOpenFillIcon, FileIcon } from "@primer/octicons-react";
import PhuHuynhChildList from "../phu-huynh-child-list";
import CSKHForm from "./cskh-form";
import CSKHHistory from "./cskh-history";
import CSKHDangKyHistory from "./cskh-history-dangky";

interface PhuHuynhCSKHProps {
  ts_phuhuynh_id: number;
  ts_hocsinh_id: number;
  ts_hoso_survey_id: number;
  height?: number;
  onChanged?: () => void;
  onGhiDanhSuccess?: () => void;
  isShowDanhSachCon?: boolean;
  isHideGhiDanh?: boolean;
}

interface Tab {
  id: number;
  text: string;
  icon: React.ReactNode;
}

const tabs: Tab[] = [
  {
    id: 0,
    text: "<PERSON><PERSON>ch sử chăm sóc",
    icon: <FileDirectoryIcon   size={16} />,
  },
  {
    id: 1,
    text: "<PERSON><PERSON>ch sử đăng ký",
    icon: <FileDirectoryIcon  size={16} />,
  },
];

const PhuHuynhCSKH: React.FC<PhuHuynhCSKHProps> = ({
  ts_phuhuynh_id,
  ts_hocsinh_id,
  ts_hoso_survey_id,
  height,
  onChanged,
  onGhiDanhSuccess,
  isShowDanhSachCon = false,
  isHideGhiDanh = false,
}) => {
  const [selectedTabId, setSelectedTabId] = useState<number>(0);
  const [changedCount, setChangedCount] = useState<number>(0);

  const handleFormSuccess = () => {
    if (onGhiDanhSuccess) {
      onGhiDanhSuccess();
    } else {
      setChangedCount((prev) => prev + 1);
    }
  };

  const handleHistoryChange = () => {
    setChangedCount((prev) => prev + 1);
    onChanged?.();
  };

  return (
    <div className="d-flex flex-column">
      <div className="mb-3">
        <CSKHForm
          ts_phuhuynh_id={ts_phuhuynh_id}
          onGhiDanhSuccess={handleFormSuccess}
          onSuccess={handleHistoryChange}
          isHideGhiDanh={isHideGhiDanh}
        />
      </div>

      {isShowDanhSachCon && (
        <div className="mb-3">
          <PhuHuynhChildList
            id={ts_phuhuynh_id}
            hocSinhSelectedId={0}
            countToRerender={changedCount}
            mode="column"
            onSelected={() => {}}
            onDeleted={function (phuHuynhId: number, hocSinhId: number): void {
              throw new Error("Function not implemented.");
            }}
          />
        </div>
      )}

      <div className="border rounded-2">
        <UnderlineNav aria-label="Main">
          {tabs.map((tab) => (
            <UnderlineNav.Item
              key={tab.id}
              aria-current={selectedTabId === tab.id ? "page" : undefined}
              onSelect={(
                event:
                  | React.MouseEvent<HTMLAnchorElement>
                  | React.KeyboardEvent<HTMLAnchorElement>
              ) => {
                event.preventDefault();
                setSelectedTabId(tab.id);
              }}
              sx={{
                fontWeight: selectedTabId === tab.id ? 600 : 400
              }}
            >
              {tab.icon && <span className="mr-2">{tab.icon}</span>}
              {tab.text}
            </UnderlineNav.Item>
          ))}
        </UnderlineNav>

        <div
          className="py-3"
          style={{ height: height || "auto", overflowY: "auto" }}
        >
          {selectedTabId === 0 ? (
            <CSKHHistory
              ts_phuhuynh_id={ts_phuhuynh_id}
              ts_hocsinh_id={ts_hocsinh_id}
              ts_hoso_survey_id={ts_hoso_survey_id}
              changedCount={changedCount}
              onChanged={handleHistoryChange}
            />
          ) : (
            <CSKHDangKyHistory
              ts_phuhuynh_id={ts_phuhuynh_id}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default PhuHuynhCSKH;