.caption {
    font-weight: 600;
    font-size: 18px;
}

.actions_top {
    margin-bottom: 5px;
}

.actions {
    display: flex;
    border-top: 1px solid rgba(0, 153, 250, 0.3);
    padding-top: 10px;
    align-items: center;
}

.left {
    flex: 1;
    display: flex;
    align-items: center;

}
.right button{
    margin-right: 10px;
}

.item {
    margin-right: 20px;
    color: var(--main-color);
    font-weight: 600;
}

.item :global(.dx-button-text) {
    text-transform: unset !important;
    color: var(--main-color);
    letter-spacing: unset !important
}

.item :global(.dx-icon) {
    text-transform: unset !important;
    color: var(--main-color);
}

.form :global(.dx-state-focused::before) {
    border: 0 !important;
}

.form :global(.dx-texteditor.dx-editor-underlined::after) {
    border: 0 !important;
}

.form :global(.dx-button-content) {
    padding: 1px !important;
}

.form :global(.dx-dropdownbutton) {
    border: 1px solid var(--main-color);
    border-radius: 20px;
    height: 22px !important;
}