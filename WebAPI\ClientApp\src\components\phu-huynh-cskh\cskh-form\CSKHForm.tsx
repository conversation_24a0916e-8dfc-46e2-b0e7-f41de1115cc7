import React, { useEffect, useState } from "react";
import {
  TextInput,
  FormControl,
  Button,
  Box,
  Label,
  Select,
  Checkbox,
  Text,
  ButtonGroup,
} from "@primer/react";
import {
  CheckIcon,
  ClockIcon,
  CrossReferenceIcon,
  PersonIcon,
  XIcon,
} from "@primer/octicons-react";
import moment from "moment";
import { NotifyHelper } from "../../../helpers/toast";
import { chamSocKhachHangApi } from "../../../api/chamSocKhachHangApi";
import { hocSinhApi } from "../../../api/hocSinhApi";
import { phuHuynhApi } from "../../../api/phuHuynhApi";
import { IPhuHuynh } from "../../../models/response/phu-huynh/IPhuHuynhItemRespone";

// Interfaces
interface CSKHFormProps {
  ts_phuhuynh_id: number;
  onSuccess?: () => void;
  onGhiDanhSuccess?: () => void;
  isHideGhiDanh?: boolean;
}

interface HocSinh {
  id: number;
  ho_ten: string;
}

interface FormErrors {
  noiDung?: string;
  optionSelectedId?: string;
  hocSinhSelectedId?: string;
  henXuLy?: string;
}

interface FormState {
  isExpanded: boolean;
  noiDung: string;
  optionSelectedId: number;
  hocSinhSelectedId: number;
  isDone: boolean;
  henXuLy: string;
  isLoading: boolean;
}

const OPTION_LIST = [
  { id: 1, name: "Ghi chú từ Tuyển sinh" },
  { id: 2, name: "Yêu cầu từ Phụ huynh" },
];

const DEFAULT_HOC_SINH: HocSinh = {
  id: 0,
  ho_ten: "Không gán vào học sinh",
};

const INITIAL_FORM_STATE: FormState = {
  isExpanded: false,
  noiDung: "",
  optionSelectedId: 1,
  hocSinhSelectedId: 0,
  isDone: false,
  henXuLy: "",
  isLoading: false,
};

const CSKHForm: React.FC<CSKHFormProps> = ({
  ts_phuhuynh_id,
  onSuccess,
}) => {
  // States
  const [formState, setFormState] = useState<FormState>(INITIAL_FORM_STATE);
  const [hocSinhs, setHocSinhs] = useState<HocSinh[]>([DEFAULT_HOC_SINH]);
  const [phuHuynh, setPhuHuynh] = useState<IPhuHuynh>();
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation Functions
  const validateNoiDung = (value: string): string | undefined => {
    if (!value.trim()) return "Nội dung chăm sóc là bắt buộc";
    if (value.length < 10) return "Nội dung chăm sóc phải có ít nhất 10 ký tự";
    if (value.length > 500) return "Nội dung chăm sóc không được vượt quá 500 ký tự";
    return undefined;
  };

  const validateHenXuLy = (value: string): string | undefined => {
    if (formState.optionSelectedId === 2 && !formState.isDone && !value) {
      return "Vui lòng chọn ngày hẹn xử lý";
    }
    if (value) {
      const selectedDate = moment(value);
      if (!selectedDate.isValid()) return "Ngày không hợp lệ";
      if (selectedDate.isBefore(moment().startOf("day"))) {
        return "Ngày hẹn xử lý không thể là ngày trong quá khứ";
      }
    }
    return undefined;
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    const noiDungError = validateNoiDung(formState.noiDung);
    if (noiDungError) newErrors.noiDung = noiDungError;

    if (formState.optionSelectedId === 2) {
      const henXuLyError = validateHenXuLy(formState.henXuLy);
      if (henXuLyError) newErrors.henXuLy = henXuLyError;
    }

    setErrors(newErrors);
    setTouched({
      noiDung: true,
      henXuLy: true,
    });

    return Object.keys(newErrors).length === 0;
  };

  // API Functions
  const fetchData = async () => {
    if (!ts_phuhuynh_id) return;
    
    try {
      setFormState(prev => ({ ...prev, isLoading: true }));
      
      // Load phụ huynh data
      const phRes = await phuHuynhApi.getById(ts_phuhuynh_id);
      if (phRes.is_success) setPhuHuynh(phRes.data);
      
      // Load học sinh data
      const hsRes = await hocSinhApi.SelectByParent(ts_phuhuynh_id);
      if (hsRes.is_success) {
        const hocSinhOptions = hsRes.data.map((element: any) => ({
          id: element.id,
          ho_ten: `Là yêu cầu của ${element.ho_ten}`,
        }));
        setHocSinhs([DEFAULT_HOC_SINH, ...hocSinhOptions]);
      }
    } catch (error) {
      NotifyHelper.Error("Có lỗi khi tải dữ liệu");
    } finally {
      setFormState(prev => ({ ...prev, isLoading: false }));
    }
  };

  // Event Handlers
  const handleSubmit = async () => {
    if (!validateForm()) {
      NotifyHelper.Error("Vui lòng kiểm tra lại thông tin");
      return;
    }

    try {
      setFormState(prev => ({ ...prev, isLoading: true }));
      
      const formattedHenXuLy = formState.henXuLy
        ? moment(formState.henXuLy).format("YYYY-MM-DD")
        : null;
        
      const formattedNgayXuLy = formState.isDone
        ? moment().format("YYYY-MM-DD")
        : null;
        
      const ngayHenTraLoiText = formState.henXuLy 
        ? moment(formState.henXuLy).format("DD/MM/YYYY")
        : "";
  
      const requestData = {
        id: 0,
        dien_thoai: phuHuynh?.dien_thoai || "",
        ten_khach_hang: phuHuynh?.ho_ten || "",
        noi_dung: formState.noiDung.trim(),
        ts_hocsinh_id: formState.hocSinhSelectedId,
        da_xu_ly: formState.isDone,
        ngay_xu_ly: formattedNgayXuLy,
        user_xu_ly_id: 0,
        ghi_chu: "",
        ts_phuhuynh_id,
        ts_hoso_survey_id: 0,
        ket_qua_xu_ly: "",
        is_request: formState.optionSelectedId === 2,
        ngay_hen_tra_loi: formattedHenXuLy,
        ngayHenTraLoiText: ngayHenTraLoiText,
        tsPhuHuynhAdmStatusId: phuHuynh?.ts_phuhuynh_adm_status_id || 0,
      };

      const res = await chamSocKhachHangApi.insert(requestData);

      if (res.is_success) {
        NotifyHelper.Success("Thêm mới thành công");
        resetForm();
        onSuccess?.();
      } else {
        NotifyHelper.Error("Có lỗi xảy ra khi thêm mới");
      }
    } catch (error) {
      NotifyHelper.Error("Có lỗi xảy ra khi gửi yêu cầu");
    } finally {
      setFormState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleChange = (field: keyof FormState, value: any) => {
    setFormState(prev => ({ ...prev, [field]: value }));
    
    if (touched[field]) {
      let error: string | undefined;
      if (field === 'noiDung') error = validateNoiDung(value);
      if (field === 'henXuLy') error = validateHenXuLy(value);
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const handleBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  const resetForm = () => {
    setFormState(INITIAL_FORM_STATE);
    setErrors({});
    setTouched({});
  };

  // Effects
  useEffect(() => {
    fetchData();
  }, [ts_phuhuynh_id]);

  return (
    <Box
      sx={{
        border: "1px solid",
        borderColor: "border.default",
        borderRadius: 2,
        boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
        overflow: "hidden",
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: "1px solid",
          borderColor: "border.default",
          bg: "canvas.default",
          display: "flex",
          alignItems: "center",
          gap: 2,
        }}
      >
        <CrossReferenceIcon size={16} />
        <Text sx={{ fontSize: 2, fontWeight: "bold", color: "fg.default" }}>
          Thêm lịch sử chăm sóc
        </Text>
        {phuHuynh?.ho_ten && (
          <Label variant="accent" sx={{ fontSize: 1 }}>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <PersonIcon size={12} />
              <Text sx={{ ml: 1 }}>{phuHuynh.ho_ten}</Text>
            </Box>
          </Label>
        )}
      </Box>

      {/* Form Content */}
      <Box sx={{ p: 2, bg: "canvas.subtle" }}>
        {/* Textarea */}
        <FormControl sx={{ mb: 2 }}>
          <TextInput
            as="textarea"
            rows={2}
            placeholder="Nhập nội dung chăm sóc..."
            value={formState.noiDung}
            onFocus={() => setFormState(prev => ({ ...prev, isExpanded: true }))}
            onChange={e => handleChange('noiDung', e.target.value)}
            onBlur={() => handleBlur("noiDung")}
            sx={{
              width: "100%",
              borderColor: touched.noiDung && errors.noiDung ? "danger.emphasis" : "border.default",
              "&:focus": {
                borderColor: "accent.emphasis",
                boxShadow: theme => `0 0 0 2px ${theme.colors.accent.subtle}`,
              },
            }}
          />
          {touched.noiDung && errors.noiDung && (
            <Text sx={{ color: "danger.fg", fontSize: 1, mt: 1 }}>
              {errors.noiDung}
            </Text>
          )}
        </FormControl>

        {/* Expanded Form */}
        {formState.isExpanded && (
          <Box
            sx={{
              display: "grid",
              gap: 2,
              gridTemplateColumns: ["1fr", "1fr 1fr"],
            }}
          >
            {/* First Row */}
            <FormControl>
              <FormControl.Label sx={{ fontWeight: "bold", fontSize: 1, mb: 1 }}>
                Loại yêu cầu
              </FormControl.Label>
              <Select
                value={formState.optionSelectedId.toString()}
                onChange={e => handleChange('optionSelectedId', Number(e.target.value))}
                sx={{ width: "100%" }}
              >
                {OPTION_LIST.map(option => (
                  <Select.Option key={option.id} value={option.id.toString()}>
                    {option.name}
                  </Select.Option>
                ))}
              </Select>
            </FormControl>

            <FormControl>
              <FormControl.Label sx={{ fontWeight: "bold", fontSize: 1, mb: 1 }}>
                Học sinh
              </FormControl.Label>
              <Select
                value={formState.hocSinhSelectedId.toString()}
                onChange={e => handleChange('hocSinhSelectedId', Number(e.target.value))}
                disabled={formState.isLoading}
                sx={{ width: "100%" }}
              >
                {hocSinhs.map(hocSinh => (
                  <Select.Option key={hocSinh.id} value={hocSinh.id.toString()}>
                    {hocSinh.ho_ten}
                  </Select.Option>
                ))}
              </Select>
            </FormControl>

            {/* Second Row - Only shows for Yêu cầu từ Phụ huynh */}
            {formState.optionSelectedId === 2 && (
              <>
                <FormControl>
                  <FormControl.Label sx={{ fontWeight: "bold", fontSize: 1, mb: 1 }}>
                    Trạng thái xử lý
                  </FormControl.Label>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                    <Checkbox
                      checked={formState.isDone}
                      onChange={e => {
                        const checked = e.target.checked;
                        setFormState(prev => ({
                          ...prev,
                          isDone: checked,
                          henXuLy: checked ? "" : prev.henXuLy,
                        }));
                        if (checked) {
                          setErrors(prev => ({ ...prev, henXuLy: undefined }));
                        }
                      }}
                    />
                    <Label variant={formState.isDone ? "success" : "attention"}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        {formState.isDone ? <CheckIcon size={14} /> : <ClockIcon size={14} />}
                        <Text>{formState.isDone ? "Đã xử lý" : "Chưa xử lý"}</Text>
                      </Box>
                    </Label>
                  </Box>
                </FormControl>

                {!formState.isDone && (
                  <FormControl>
                    <FormControl.Label sx={{ fontWeight: "bold", fontSize: 1, mb: 1 }}>
                      Ngày hẹn xử lý
                    </FormControl.Label>
                    <TextInput
                      type="date"
                      value={formState.henXuLy}
                      onChange={e => handleChange('henXuLy', e.target.value)}
                      onBlur={() => handleBlur("henXuLy")}
                      sx={{
                        width: "100%",
                        borderColor: touched.henXuLy && errors.henXuLy ? "danger.emphasis" : "border.default",
                        "&:focus": {
                          borderColor: "accent.emphasis",
                          boxShadow: theme => `0 0 0 2px ${theme.colors.accent.subtle}`,
                        },
                      }}
                    />
                    {touched.henXuLy && errors.henXuLy && (
                      <Text sx={{ color: "danger.fg", fontSize: 1, mt: 1 }}>
                        {errors.henXuLy}
                      </Text>
                    )}
                  </FormControl>
                )}
              </>
            )}
          </Box>
        )}
      </Box>

      {/* Footer Actions */}
      <Box
        sx={{
          p: 2,
          borderTop: "1px solid",
          borderColor: "border.default",
          bg: "canvas.default",
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          gap: 2,
        }}
      >
        {formState.isLoading && (
          <Text
            sx={{
              fontSize: 1,
              color: "attention.fg",
              display: "flex",
              alignItems: "center",
              gap: 1,
              marginRight: "auto",
            }}
          >
            <ClockIcon size={14} />
            Đang xử lý...
          </Text>
        )}

        <Button
          variant="invisible"
          onClick={resetForm}
          disabled={formState.isLoading}
          sx={{
            color: "danger.fg",
            "&:hover": { color: "danger.emphasis" },
          }}
        >
          <XIcon size={16} />
          <Text sx={{ ml: 1 }}>Hủy</Text>
        </Button>
        
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={formState.isLoading}
          sx={{
            bg: "success.emphasis",
            "&:hover": { bg: "success.fg" },
          }}
        >
          <CheckIcon size={16} />
          <Text sx={{ ml: 1 }}>Lưu yêu cầu</Text>
        </Button>
      </Box>
    </Box>
  );
};

export default CSKHForm;