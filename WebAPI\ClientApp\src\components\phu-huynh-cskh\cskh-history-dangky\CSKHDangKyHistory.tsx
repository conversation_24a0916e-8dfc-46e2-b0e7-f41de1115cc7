import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { phuHuynhApi } from '../../../api/phuHuynhApi';
import styles from './CSKHDangKyHistory.module.css';

interface DangKyHistoryItem {
    id: number;
    ten_chien_dich: string;
    register_date: string;
    ho_ten_hoc_sinh: string;
    khoi_lop: string;
    ghi_chu: string;
}

interface CSKHDangKyHistoryProps {
    ts_phuhuynh_id: number;
}

interface ApiResponse {
    is_success: boolean;
    data: DangKyHistoryItem[];
}

const CSKHDangKyHistory: React.FC<CSKHDangKyHistoryProps> = ({ ts_phuhuynh_id }) => {
    const [dataSource, setDataSource] = useState<DangKyHistoryItem[]>([]);

    useEffect(() => {
        handleReload();
    }, [ts_phuhuynh_id]);

    const handleReload = async (): Promise<void> => {
        const res = await phuHuynhApi.selectLichSuDangKy(ts_phuhuynh_id);
        if (res.is_success) {
            setDataSource(res.data);
        }
    };

    return (
        <div className={styles.container}>
          {dataSource.length === 0 ? (
            <div
              style={{
                padding: "24px",
                textAlign: "center",
                border: "1px dashed #e1e4e8",
                borderRadius: "6px",
                backgroundColor: "#f6f8fa",
                color: "#586069",
                marginBottom: "16px"
              }}
            >
              <svg 
                width="30" 
                height="30" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
                style={{ margin: "0 auto 16px", display: "block", marginTop:"12px" }}
              >
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
              <h3 style={{ margin: "0 0 8px", fontSize: "16px", fontWeight: "500" }}>
                Chưa có dữ liệu đăng ký
              </h3>
              </div>
          ) : (
            dataSource.map((x) => (
              <div 
                className={styles.item} 
                key={x.id}
                style={{
                  padding: "16px",
                  borderRadius: "6px",
                  border: "1px solid #e1e4e8",
                  marginBottom: "12px",
                  boxShadow: "0 1px 3px rgba(0,0,0,0.05)",
                  backgroundColor: "#fff"
                }}
              >
                <div style={{ display: "flex", alignItems: "center", marginBottom: "8px" }}>
                  <svg 
                    width="16" 
                    height="16" 
                    viewBox="0 0 16 16" 
                    fill="currentColor" 
                    style={{ marginRight: "8px", color: "#2188ff" }}
                  >
                    <path fillRule="evenodd" d="M1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0zM8 0a8 8 0 100 16A8 8 0 008 0zM6.5 7.5a.5.5 0 01.5-.5h2a.5.5 0 01.5.5v2a.5.5 0 01-.5.5h-2a.5.5 0 01-.5-.5v-2z"></path>
                  </svg>
                  <span style={{ fontWeight: "500" }}>Chiến dịch: </span>
                  <span style={{ fontWeight: "bold", marginLeft: "4px" }}>{x.ten_chien_dich}</span>
                  <span style={{ margin: "0 8px", color: "#586069" }}>•</span>
                  <span style={{ color: "#586069", fontSize: "14px" }}>
                    Ngày đăng ký: <b>{moment(x.register_date).format('DD/MM/YYYY')}</b>
                  </span>
                </div>
                
                <div style={{ display: "flex", alignItems: "center", marginBottom: "8px" }}>
                  <svg 
                    width="16" 
                    height="16" 
                    viewBox="0 0 16 16" 
                    fill="currentColor" 
                    style={{ marginRight: "8px", color: "#34d058" }}
                  >
                    <path fillRule="evenodd" d="M8 0a8 8 0 100 16A8 8 0 008 0zM7 11.75a.75.75 0 001.5 0v-5.5a.75.75 0 00-1.5 0v5.5zm0-8.25a.75.75 0 001.5 0v-1a.75.75 0 00-1.5 0v1z"></path>
                  </svg>
                  <span style={{ fontWeight: "500" }}>Học sinh: </span>
                  <span style={{ fontWeight: "bold", marginLeft: "4px" }}>{x.ho_ten_hoc_sinh}</span>
                  <span style={{ margin: "0 8px", color: "#586069" }}>•</span>
                  <span style={{ color: "#586069" }}>
                    Khối <b>{x.khoi_lop}</b>
                  </span>
                </div>
                
                {x.ghi_chu && (
                  <div style={{ 
                    display: "flex", 
                    alignItems: "flex-start", 
                    padding: "8px", 
                    backgroundColor: "#f6f8fa", 
                    borderRadius: "4px", 
                    fontSize: "14px"
                  }}>
                    <svg 
                      width="16" 
                      height="16" 
                      viewBox="0 0 16 16" 
                      fill="currentColor" 
                      style={{ marginRight: "8px", marginTop: "2px", flexShrink: 0, color: "#586069" }}
                    >
                      <path fillRule="evenodd" d="M15 1H2c-.55 0-1 .45-1 1v12c0 .55.45 1 1 1h13c.55 0 1-.45 1-1V2c0-.55-.45-1-1-1zM1 2c0-.55.45-1 1-1h13c.55 0 1 .45 1 1v12c0 .55-.45 1-1 1H2c-.55 0-1-.45-1-1V2zm14 12H2V2h13v12zM7 4H3v2h4V4zm0 3H3v2h4V7zm0 3H3v2h4v-2zm7-6h-4v2h4V4zm0 3h-4v2h4V7zm0 3h-4v2h4v-2z"></path>
                    </svg>
                    <div>
                      <span style={{ fontWeight: "500" }}>Ghi chú: </span>
                      <span>{x.ghi_chu}</span>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      );
};

export default CSKHDangKyHistory;