import React, { useEffect, useState } from "react";
import { useCommonContext } from "../../../contexts/common";
import styles from "./CSKHHistory.module.css";
import { chamSocKhachHangApi } from "../../../api/chamSocKhachHangApi";
import PhuHuynhCSKHItem from "../../phu-huynh-cskh-item";
import { Box, Text } from "@primer/react";
import {CommentDiscussionIcon} from  "@primer/octicons-react";
interface CSKHHistoryProps {
  ts_phuhuynh_id: number;
  ts_hocsinh_id: number;
  ts_hoso_survey_id: number;
  changedCount: number;
  onChanged?: () => void;
}

interface ChamSocKhachHang {
  id: number;
  da_xu_ly: boolean;
  ten_khach_hang: string;
  dien_thoai: string;
  ma_hs?: string;
  ho_ten_hoc_sinh?: string;
  is_request: boolean;
  ngay_hen_tra_loi?: string;
  ngay_xu_ly?: string;
  noi_dung: string;
  nguoi_tao: string;
  ngay_tao: string;
  ghi_chu?: string;
  ket_qua_xu_ly?: string;
}

interface ApiResponse {
  is_success: boolean;
  data: ChamSocKhachHang[];
  message?: string;
}

interface CommonContextType {
  translate: (key: string) => string;
}

const CSKHHistory: React.FC<CSKHHistoryProps> = ({
  ts_phuhuynh_id,
  ts_hocsinh_id,
  ts_hoso_survey_id,
  changedCount,
  onChanged,
}) => {
  const [chamSocKhachHangs, setChamSocKhachHangs] = useState<ChamSocKhachHang[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { translate } = useCommonContext() as CommonContextType;

  useEffect(() => {
    handleReloadData();
  }, [ts_phuhuynh_id, ts_hocsinh_id, ts_hoso_survey_id, changedCount]);

  const handleReloadData = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const res = await chamSocKhachHangApi.select({
        ts_phuhuynh_id,
        ts_hocsinh_id,
        ts_hoso_survey_id,
      });

      if (res.is_success && Array.isArray(res.data)) {
        console.log(res.data)
        setChamSocKhachHangs(res.data);
      } else {
        setChamSocKhachHangs([]);
        setError(res.message || 'Failed to load data');
      }
    } catch (err) {
      console.error('Error loading CSKH data:', err);
      setError('Error loading data');
      setChamSocKhachHangs([]);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Box className={styles.no_data}>
        <Text>{translate("Đang tải dữ liệu...")}</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box className={styles.no_data}>
        <Text color="danger.emphasis">{translate(error)}</Text>
      </Box>
    );
  }

  return (
    <Box>
      {(!chamSocKhachHangs || chamSocKhachHangs.length === 0) ? (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            py: 6,
            px: 3,
            borderRadius: 2,
            bg: "canvas.subtle",
            border: "1px dashed",
            borderColor: "border.muted",
            color: "fg.muted",
            textAlign: "center"
          }}
        >
          <Box
            sx={{
              bg: "neutral.subtle",
              borderRadius: "50%",
              p: 3,
              mb: 2,
            }}
          >
            <CommentDiscussionIcon size={24} />
          </Box>
          <Text sx={{ fontSize: 2, fontWeight: "bold", mb: 1 }}>
            {translate("Chưa có lịch sử chăm sóc")}
          </Text>       
        </Box>
      ) : (
        <Box className={styles.list_container}>
          {chamSocKhachHangs.map((ts_chamsockhachhang, idx) => (
            <PhuHuynhCSKHItem
              key={ts_chamsockhachhang.id || idx}
              ts_chamsockhachhang={ts_chamsockhachhang}
              onChanged={() => {
                handleReloadData();
                onChanged?.();
              }}
            />
          ))}
        </Box>
      )}
    </Box>
  );
};

export default CSKHHistory;