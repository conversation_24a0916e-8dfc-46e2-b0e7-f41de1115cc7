.container {
    margin-top: 20px;
    padding-left: 1px;
    padding-right: 1px;
}

.container_caption {
    color: rgba(0, 0, 0, 0.5);
    font-size: 15px;
    font-weight: bolder;
    margin-bottom: 5px;
}

.phu_huynh {
    /* box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px; */
    box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
    padding: 6px 10px;
    border-radius: 10px;
    cursor: pointer;
    margin-bottom: 10px;
}

.caption {
    font-size: 15px;
    font-weight: bolder;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.caption .name {
    flex: 1;
}

.info_container {
    margin-bottom: 5px;
    font-size: 14px;
    white-space: pre-wrap;
    display: -webkit-box;
    text-overflow: ellipsis;
    /* enables ellipsis */
    white-space: nowrap;
    /* keeps the text in a single line */
    overflow: hidden;
    /* keeps the element from overflowing its parent */

}

.info_container i {
    /* min-width: 30px; */
    text-align: left;
    margin-right: 3px;
}

.tag {
    font-size: 11px;
    font-weight: 400;
    padding: 0px 6px;
    background-color: #002a69;
    border-radius: 5px;
    align-items: center;
    color: white;
}