import React, { useEffect, useRef, useState } from "react";
import { Box, Text, ActionList } from "@primer/react";
import {
  MailIcon,
  DeviceMobileIcon,
  ChevronRightIcon,
} from "@primer/octicons-react";
import { phuHuynhApi } from "../../api/phuHuynhApi";
import UserAvatar from "../user-avatar";

interface PhuHuynhFamilyMember {
  id: string | number;
  ho_ten: string;
  quan_he: string;
  email: string;
  dien_thoai: string;
}

interface PhuHuynhFamilyProps {
  id: number;
  onMemberSelect?: (memberId: number) => void;
}

const PhuHuynhFamily: React.FC<PhuHuynhFamilyProps> = ({
  id,
  onMemberSelect,
}) => {
  const [phuHuynhFamilys, setPhuHuynhFamily] = useState<PhuHuynhFamilyMember[]>(
    []
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const componentDidMount = useRef<boolean>(false);

  useEffect(() => {
    componentDidMount.current = true;
    handleReloadPhuHuynhFamily();
    return () => {
      componentDidMount.current = false;
    };
  }, [id]);

  const handleReloadPhuHuynhFamily = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const res = await phuHuynhApi.selectFamily(id);
      if (componentDidMount.current) {
        setIsLoading(false);
        if (res.is_success) {
          setPhuHuynhFamily(res.data);
        }
      }
    } catch (error) {
      setIsLoading(false);
      console.error("Error loading family data:", error);
    }
  };

  if (isLoading || phuHuynhFamilys.length === 0) return null;

  return (
    <ActionList showDividers>
      <ActionList.GroupHeading as="h3">Thành viên khác</ActionList.GroupHeading>

      {phuHuynhFamilys.map((member, index) => (
        <ActionList.Item
          key={index}
          sx={{
            py: 2,
            px: 3,
            backgroundColor: "canvas.subtle",
            cursor: "pointer",
          }}
          onClick={() => onMemberSelect?.(Number(member.id))}
        >
          <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
            {/* Main Content */}
            <Box sx={{ flex: 1 }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  width: "100%",
                  mb: 1,
                }}
              >
                <Text fontWeight="bold" fontSize={2}>
                  {member.ho_ten}
                </Text>
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                  {member.quan_he && (
                    <Text
                      as="span"
                      sx={{
                        px: 2,
                        py: 1,
                        borderRadius: 2,
                        fontSize: 0,
                        backgroundColor: "accent.muted",
                        color: "accent.fg",
                        fontWeight: "bold",
                      }}
                    >
                      {member.quan_he}
                    </Text>
                  )}
                </Box>
              </Box>

              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  width: "100%",
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Box sx={{ mr: 1, color: "fg.muted" }}>
                    <MailIcon size={15} />
                  </Box>
                  <Text fontSize={1}>{member.email}</Text>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Box sx={{ mr: 1, color: "fg.muted" }}>
                    <DeviceMobileIcon size={15} />
                  </Box>
                  <Text fontSize={1}>{member.dien_thoai}</Text>
                </Box>
              </Box>
            </Box>
          </Box>
        </ActionList.Item>
      ))}
    </ActionList>
  );
};

export default PhuHuynhFamily;
