import React, { useEffect, useRef, useState } from "react";
import { Box, Text, <PERSON><PERSON><PERSON><PERSON>on, ActionList, ActionMenu } from "@primer/react";
import { PencilIcon, MailIcon, DeviceMobileIcon } from "@primer/octicons-react";
import { phuHuynh<PERSON><PERSON> } from "../../api/phuHuynhApi";
import { useCommonContext } from "../../contexts/common";
import clsx from "clsx";
import UserAvatar from "../user-avatar";
import {
  KebabHorizontalIcon,
  PersonIcon,
  ChevronRightIcon,
  TrashIcon,
  BookIcon,
  TasklistIcon,
} from "@primer/octicons-react";

interface PhuHuynhInfoProps {
  id: number;
  isSelected: boolean;
  onSelected: () => void;
}

interface PhuHuynhData {
  id: number;
  ho_ten: string;
  email: string;
  dien_thoai: string;
  quan_he?: string;
}

const PhuHuynhInfo: React.FC<PhuHuynhInfoProps> = ({
  id,
  isSelected,
  onSelected,
}) => {
  const [formData, setFormData] = useState<PhuHuynhData | null>(null);
  const { translate } = useCommonContext();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const componentDidMount = useRef<boolean>(false);
  const [isShowEditModal, setIsShowEditModal] = useState<boolean>(false);

  useEffect(() => {
    componentDidMount.current = true;
    handleReloadData();
    return () => {
      componentDidMount.current = false;
    };
  }, [id]);

  const handleReloadData = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const res = await phuHuynhApi.getById(id);
      if (componentDidMount.current) {
        setIsLoading(false);
        if (res.is_success) {
          setFormData(res.data);
        }
      }
    } catch (error) {
      setIsLoading(false);
      console.error("Error loading phu huynh details:", error);
    }
  };

  if (isLoading || !formData) return null;

  return (
    <ActionList>
      <ActionList.Item
        sx={{
          py: 2,
          px: 3,
          backgroundColor: "accent.muted",
          cursor: isSelected ? "default" : "pointer",
          "&:hover": {
            backgroundColor: isSelected ? "accent.emphasis" : "bg.primary",
          },
          pointerEvents: isSelected ? "none" : "auto",
          borderRadius: 2,
          boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
        }}
        onClick={isSelected ? undefined : onSelected}
      >
        <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
          {/* Main Content */}
          <Box sx={{ flex: 1 }}>
          <Box sx={{ 
              display: "flex", 
              alignItems: "center", 
              justifyContent: "space-between",
              width: "100%",   
              mb:1         
            }}>
             <Text fontWeight="bold" fontSize={2}>{formData.ho_ten}</Text>
            {formData.quan_he && (
                <Text
                  as="span"
                  sx={{
                    px: 2,
                    py: 1,
                    borderRadius: 2,
                    fontSize: 0,
                    backgroundColor: "success.muted",
                    color: "success.fg",
                    fontWeight: "bold",
                  }}
                >
                  {formData.quan_he}
                </Text>
              )}
            </Box>


            
            <Box sx={{ 
              display: "flex", 
              alignItems: "center", 
              justifyContent: "space-between",
              width: "100%",            
            }}>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box sx={{ mr: 1, color: "fg.muted" }}><MailIcon size={15} /></Box>
                <Text fontSize={1}>{formData.email}</Text>
              </Box>
                        
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box sx={{ mr: 1, color: "fg.muted" }}><DeviceMobileIcon size={15} /></Box>
                <Text fontSize={1}>{formData.dien_thoai}</Text>
              </Box>
            </Box>
          </Box>
        </Box>
      </ActionList.Item>
    </ActionList>
  );
};

export default PhuHuynhInfo;