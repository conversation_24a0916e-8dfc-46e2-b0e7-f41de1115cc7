.container {
    display: flex;
    /* padding-left: 1rem; */
    padding-bottom: 5px;
    flex-wrap: wrap;
}

.item_container {
    margin-right: 5px;
    padding: 5px 10px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, .04);
    cursor: pointer;
    margin-bottom: 5px;
}

.item_container.selected {
    background-color: #002a69;
    color: #fff;
}

.count {
    background-color: #a9a9a9;
    border-radius: 5px;
    font-size: 11px;
    font-weight: 700;
    color: #fff;
    margin-left: 5px;
    padding: 2px 3px;
}

.selected .count {
    background-color: #fff;
    color:#002a69;

}