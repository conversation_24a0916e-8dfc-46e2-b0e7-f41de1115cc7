import clsx from "clsx";
import React, { useEffect, useMemo, useState } from 'react';
import { phuHuynhApi } from '../../api/phuHuynhApi';
import { trangThaiPhuHuynhApi } from '../../api/trangThaiPhuHuynhApi';
import styles from './PhuHuynhStatusSummary.module.css';
import { useSelector } from 'react-redux';
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";

interface TrangThaiPhuHuynh {
    ts_phuhuynh_adm_status_id: number;
    name: string;
}

interface PhuHuynh {
    ts_phuhuynh_adm_status_id: number;
    // Add other PhuHuynh properties as needed
}

interface StatusSummaryItem extends TrangThaiPhuHuynh {
    so_luong: number;
}

interface RootState {
    coSo: {
        dm_coso_id: number;
    };
}

interface PhuHuynhStatusSummaryProps {
    onSelectionChanged: (id: number) => void;
    ts_phuhuynhs: PhuHuynh[];
}

const PhuHuynhStatusSummary: React.FC<PhuHuynhStatusSummaryProps> = ({ onSelectionChanged, ts_phuhuynhs }) => {
    const [selectedId, setSelectedId] = useState<number>(0);
    const [status, setStatus] = useState<TrangThaiPhuHuynh[]>([]);
    //const { dm_coso_id } = useSelector((x: RootState) => x.coSo);
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

    useEffect(() => {
        handleReload();
    }, []);

    const handleReload = async (): Promise<void> => {
        const res = await trangThaiPhuHuynhApi.selectByCoSo(dm_coso_id);
        if (res.is_success) {
            setStatus(res.data);
        }
    };

    const dataSource: StatusSummaryItem[] = useMemo(() => {
        return status.map(x => {
            const soLuong = ts_phuhuynhs.filter(y => 
                y.ts_phuhuynh_adm_status_id === x.ts_phuhuynh_adm_status_id
            ).length;
            return {
                ...x,
                so_luong: soLuong
            };
        });
    }, [status, ts_phuhuynhs]);

    const total: number = useMemo(() => {
        return dataSource.reduce((a, b) => a + b.so_luong, 0);
    }, [dataSource]);

    return (
        <div className={styles.container}>
            <div 
                key={0} 
                className={clsx(styles.item_container, selectedId === 0 ? styles.selected : "")}
                onClick={() => {
                    setSelectedId(0);
                    onSelectionChanged(0);
                }}
            >
                Tất cả
                {/* <span className={styles.count}>
                    {total}
                </span> */}
            </div>
            {dataSource.map((x, idx) => (
                <div 
                    key={idx}
                    className={clsx(
                        styles.item_container, 
                        selectedId === x.ts_phuhuynh_adm_status_id ? styles.selected : ""
                    )}
                    onClick={() => {
                        setSelectedId(x.ts_phuhuynh_adm_status_id);
                        onSelectionChanged(x.ts_phuhuynh_adm_status_id);
                    }}
                >
                    {x.name}
                    <span className={styles.count}>
                        {x.so_luong}
                    </span>
                </div>
            ))}
        </div>
    );
};

export default PhuHuynhStatusSummary;