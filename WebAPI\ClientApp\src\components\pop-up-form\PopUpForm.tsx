import React from 'react';
import styles from "./PopUpForm.module.css"
import { Box } from '@primer/react';
interface IPopUpFormProps {
    onSubmit: () => void,
    children?: React.ReactNode
}
interface IPopUpFormActions {
    children?: React.ReactNode
}

export const PopUpForm = (props: IPopUpFormProps) => {
    return (
        <form onSubmit={props.onSubmit}>
            <div className={styles.form}>
                {props.children}
            </div>
        </form>
    );
};

export const PopUpFormActions = (props: IPopUpFormActions) => {
    return (
        <Box
            className={styles.actions}
            sx={{
                p: 3,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderTop: "1px",
                borderTopStyle: "solid",
                borderTopColor: "border.default",
                mb: -3,
                ml: -3,
                mr: -3,
                mt: 3
            }}
        >
            {props.children}
        </Box>

        // <div className={styles.actions}>
        //     {props.children}
        // </div>
    );
};
