@media all {
  .page-break {
    display: none;
  }
}

@media print {

  html,
  body {
    height: initial !important;
    overflow: initial !important;
    -webkit-print-color-adjust: exact;
  }
}

@media print {
  .page-break {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    display: block;
    page-break-before: auto;
    page-break-after: always;
  }
}

@page {
  size: auto;
  /* margin: 10mm; */
}

.print-component {
  @media print {
    display: table;

    .table-footer>tr>td {
      text-align: center;
      /* background-color: grey; */
      color: white;
      height: 3cm;
    }
  }
}

/* .print-component {
  display: table;
  .table-footer>tr>td {
    text-align: center;
    background-color: grey;
    color: white;
    height: 50px;
  }

} */

.temp-class-for-height {
  @media not print {
    /* A4 dọc */
    width: 720px;
    visibility: hidden;
    display: table;
  }
}