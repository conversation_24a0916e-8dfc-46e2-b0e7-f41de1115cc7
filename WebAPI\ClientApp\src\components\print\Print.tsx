import React, { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import "./Print.css"
import { Button } from '../buttons';
interface IPrintProps {
    pagesHtml: string[]
}
const Print = (props: IPrintProps) => {
    const componentRef = useRef<any>();
    
    const handlePrint = useReactToPrint({
        content: () => componentRef.current,
    });
    return (
        <div>
            <div style={{ margin: "1rem" }}>
                <Button text='Print'
                    icon='fas fa-print'
                    style='filled'
                    type='success'
                    onClick={handlePrint}
                />
            </div>
            <div ref={componentRef}
                className="print-container"
            >
                {props.pagesHtml.map((x, idx) => {
                    return (
                        <div key={idx}>
                            <div dangerouslySetInnerHTML={{ __html: x }} />
                            <div className="page-break" />

                            {/* <table className="print-component">
                                <thead>
                                    <tr>
                                        <th>
                                            <img src={logo} height={"40px"} width={"40px"} alt="logo" />
                                            <div>
                                                {"Page Header"}
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div dangerouslySetInnerHTML={{ __html: x }} />
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot className="table-footer">
                                    <tr>
                                        <td>
                                            {"Page footer"}
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>

                            <div className="page-break" /> */}
                        </div>
                    );

                })}

            </div>
        </div>
    );
};

export default Print;