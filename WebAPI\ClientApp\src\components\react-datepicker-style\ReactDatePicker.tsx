import React, { forwardRef } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { CalendarIcon } from "@primer/octicons-react";
import { Text, TextInput } from "@primer/react";
import styles from "./StyledDatePicker.module.css";

interface CustomInputProps {
  value?: string;
  onClick?: () => void;
  "aria-invalid"?: boolean;
  [key: string]: any;
}

const CustomInput = forwardRef<HTMLInputElement, CustomInputProps>(
  ({ value, onClick, ...props }, ref) => (
    <TextInput
      ref={ref}
      value={value}
      onClick={onClick}
      {...props}
      readOnly
      leadingVisual={CalendarIcon}
      block
    />
  )
);

export interface StyledDatePickerProps {
  selected: Date | null;
  onChange: (date: Date | null) => void;
  dateFormat?: string;
  disabled?: boolean;
  error?: string;
  name?: string;
  [key: string]: any;
}

const StyledDatePicker: React.FC<StyledDatePickerProps> = ({
  selected,
  onChange,
  dateFormat = "dd/MM/yyyy",
  disabled = false,
  error,
  ...props
}) => {
  return (
    <>
      <div className={styles.datePickerWrapper}>
        <DatePicker
          selected={selected}
          onChange={onChange}
          dateFormat={dateFormat}
          disabled={disabled}
          customInput={<CustomInput aria-invalid={!!error} />}
          showYearDropdown
          scrollableYearDropdown
          yearDropdownItemNumber={100}
          showMonthDropdown
          dropdownMode="select"
          calendarClassName={styles.calendar}
          {...props}
        />
      </div>
      {error && (
        <Text sx={{ color: "danger.emphasis", fontSize: "12px", mt: 1 }}>
          {error}
        </Text>
      )}
    </>
  );
};

export default StyledDatePicker;