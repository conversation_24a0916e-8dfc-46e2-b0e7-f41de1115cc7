.datePickerWrapper :global(.react-datepicker) {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    box-shadow: 0 8px 24px rgba(149, 157, 165, 0.2);
    background-color: white;
    font-size: 0.9rem;
  }
  
  .datePickerWrapper :global(.react-datepicker__header) {
    background-color: #f6f8fa;
    border-bottom: 1px solid #e1e4e8;
    padding: 10px 0;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    text-align: center;
  }
  
  .datePickerWrapper :global(.react-datepicker__current-month) {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 8px;
  }
  
  .datePickerWrapper :global(.react-datepicker__day-names) {
    display: flex;
    justify-content: space-around;
    margin-top: 8px;
  }
  
  .datePickerWrapper :global(.react-datepicker__day-name) {
    width: 2rem;
    margin: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: #586069;
  }
  
  .datePickerWrapper :global(.react-datepicker__month) {
    margin: 0.4rem;
  }
  
  .datePickerWrapper :global(.react-datepicker__week) {
    display: flex;
    justify-content: space-around;
  }
  
  .datePickerWrapper :global(.react-datepicker__day) {
    margin: 0;
    border-radius: 4px;
    width: 2rem;
    height: 2rem;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }
  
  .datePickerWrapper :global(.react-datepicker__day:hover) {
    background-color: #f6f8fa;
  }
  
  .datePickerWrapper :global(.react-datepicker__day--selected) {
    background-color: #0366d6 !important;
    color: white;
    font-weight: 600;
  }
  
  .datePickerWrapper :global(.react-datepicker__day--keyboard-selected) {
    background-color: rgba(3, 102, 214, 0.2) !important;
  }
  
  .datePickerWrapper :global(.react-datepicker__day--today) {
    font-weight: bold;
    color: #0366d6;
    position: relative;
  }
  
  .datePickerWrapper :global(.react-datepicker__month-select),
  .datePickerWrapper :global(.react-datepicker__year-select) {
    border-radius: 4px;
    border: 1px solid #e1e4e8;
    padding: 5px 8px;
    font-size: 14px;
    margin: 0 4px;
    background-color: white;
    font-weight: 500;
    cursor: pointer;
    height: 30px;
  }
  
  .datePickerWrapper :global(.react-datepicker__month-dropdown-container),
  .datePickerWrapper :global(.react-datepicker__year-dropdown-container) {
    display: inline-block;
    margin: 0 5px;
  }
  
  .datePickerWrapper :global(.react-datepicker__navigation) {
    top: 13px;
  }
  
  .datePickerWrapper :global(.react-datepicker__navigation--previous) {
    left: 12px;
  }
  
  .datePickerWrapper :global(.react-datepicker__navigation--next) {
    right: 12px;
  }