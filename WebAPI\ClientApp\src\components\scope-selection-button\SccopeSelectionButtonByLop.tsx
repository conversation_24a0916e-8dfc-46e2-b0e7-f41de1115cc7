import { TriangleDownIcon } from '@primer/octicons-react';
import { Box } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { useLanguage } from '../../hooks/useLanguage';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
import SccopeSelectionByLop from '../scope-selection/SccopeSelectionByLop';
import Button from '../ui/button';
import Modal from '../ui/modal';
import ModalActions from '../ui/modal/ModalActions';
interface ISccopeSelectionButtonByLopProps {
    lopIdsSelected: number[],
    onValueChanged: (ids: number[]) => void,
    placeHolder?: string
}
const SccopeSelectionButtonByLop = (props: ISccopeSelectionButtonByLopProps) => {
    const [isShowSelectionModal, setIsShowSelectionModal] = useState(false);
    const categorySource = useSelector((state: RootState) => state.categorySource);
    const { nam_hoc } = useCommonSelectedHook();
    const dm_khois = useSelector((state: RootState) => state.categorySource.dm_khois);
    const dm_truongs = useSelector((state: RootState) => state.auth.user_info?.campus);
    const { dm_lops, status } = useSelector((state: RootState) => state.danhMucWrapper.lop);
    const { language } = useLanguage();
    const dispatch = useDispatch();
    const [tempLopSelectedId, setTempLopSelectedId] = useState(props.lopIdsSelected);

    const displayText = useMemo(() => {
        const lopsSelected = dm_lops.filter(x => props.lopIdsSelected.includes(x.id));
        if (lopsSelected.length === 0) return props.placeHolder ?? "Select";
        const count = lopsSelected.length;
        const firsts = lopsSelected.slice(0, 3);
        let result = firsts.map(x => x.ten_lop).join(", ");
        if (count > 3) {
            result += ", +" + (count - firsts.length) + " more";
        }
        return result;
    }, [dm_lops, props.lopIdsSelected])
    useEffect(() => {
        setTempLopSelectedId(props.lopIdsSelected)
    }, [props.lopIdsSelected])
    useEffect(() => {
        dispatch(actions.danhMucWrapper.lop.loadStart(nam_hoc || '', 0));
    }, [nam_hoc]);
    useEffect(() => {
        if (categorySource?.dm_khois?.length === 0) dispatch(actions.categorySource.loadKhoiStart());
    }, [categorySource]);

    return (
        <Box>
            <Button text={displayText}
                onClick={() => {
                    setIsShowSelectionModal(true)
                    setTempLopSelectedId(props.lopIdsSelected)
                }}
                block
                trailingVisual={TriangleDownIcon}
            />
            {isShowSelectionModal &&
                <Modal
                    isOpen={true}
                    title={language === "en" ? "Select" : "Chọn lớp"}
                    width={"large"}
                    onClose={() => {
                        setIsShowSelectionModal(false)
                    }}
                >
                    <Box sx={{
                        maxHeight:window.innerHeight-200,
                        overflowY:"scroll",
                        mr:-3,
                        pr:3
                    }}>
                        <SccopeSelectionByLop
                            onValueChanged={(ids) => {
                                setTempLopSelectedId(ids)
                            }}
                            lopIdsSelected={tempLopSelectedId}
                        />
                    </Box>
                    <ModalActions>
                        <Button text="Base.Label.Close"
                            onClick={() => {
                                setIsShowSelectionModal(false)
                            }}
                        />
                        <Button text="Base.Label.Confirm" variant="primary" sx={{ ml: 2 }}
                            onClick={() => {
                                props.onValueChanged(tempLopSelectedId)
                                setIsShowSelectionModal(false)
                            }}
                        />
                    </ModalActions>
                </Modal>
            }
        </Box>
    );
};

export default SccopeSelectionButtonByLop;