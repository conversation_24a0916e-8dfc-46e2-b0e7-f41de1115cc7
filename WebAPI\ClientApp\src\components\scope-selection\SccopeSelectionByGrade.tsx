import { Box, Checkbox, FormControl, NavList } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useDebounce } from "use-debounce";
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { useLanguage } from '../../hooks/useLanguage';
import { actions } from "../../state/actions/actionsWrapper";
import { RootState } from '../../state/reducers';
import styles from "./SccopeSelectionByLop.module.css";
interface ISccopeSelectionByGradeProps {
    gradeIds: number[],
    onValueChanged: (ids: number[]) => void,
}
const SccopeSelectionByGrade = (props: ISccopeSelectionByGradeProps) => {
    const categorySource = useSelector((state: RootState) => state.categorySource);
    const { nam_hoc } = useCommonSelectedHook();
    const dm_khois = useSelector((state: RootState) => state.categorySource.dm_khois);
    const dm_truongs = useSelector((state: RootState) => state.auth.user_info?.campus);
    const { dm_lops, status } = useSelector((state: RootState) => state.danhMucWrapper.lop);
    const { language } = useLanguage();
    const dispatch = useDispatch();
    const [searchKey, setSearchKey] = useState("");
    const [searchKeyDelayed] = useDebounce(searchKey, 300)
    const filterdLop = useMemo(() => {
        if (!searchKeyDelayed) return dm_lops;
        return dm_lops.filter(x => x.ten_lop.includes(searchKeyDelayed))
    }, [dm_lops, searchKeyDelayed])
    const filterdTruong = useMemo(() => {
        const dm_truong_ids = filterdLop.map(x => x.dm_truong_id);
        return dm_truongs?.filter(x => dm_truong_ids.includes(x.id)) ?? []
    }, [filterdLop])
    const filterdKhoi = useMemo(() => {
        const dm_khoi_ids = filterdLop.map(x => x.dm_khoi_id);
        return dm_khois?.filter(x => dm_khoi_ids.includes(x.id)) ?? []
    }, [filterdLop])
    useEffect(() => {
        dispatch(actions.danhMucWrapper.lop.loadStart(nam_hoc || '', 0));
    }, [nam_hoc]);
    useEffect(() => {
        if (categorySource?.dm_khois?.length === 0) dispatch(actions.categorySource.loadKhoiStart());
    }, [categorySource]);

    return (
        <Box>
            {/* <Box>
                <TextInput leadingVisual={SearchIcon}
                    placeholder={language === "en" ? 'Search' : 'Tìm kiếm'}
                    block
                    value={searchKey}
                    onChange={(e) => {
                        setSearchKey(e.target.value)
                    }}
                />
            </Box> */}
            <NavList>
                {filterdTruong &&
                    <>
                        {filterdTruong.map(truong => {
                            const truong_khois = filterdKhoi.filter(x => x.dm_truong_id === truong.id)
                            var khoiIdsTruong = truong_khois.map(x => x.id)
                            const khoiSelectedTruong = truong_khois.filter(x => props.gradeIds.includes(x.id))
                            const truongChecked = khoiSelectedTruong.length > 0 && khoiSelectedTruong.length === truong_khois.length;
                            return (
                                <NavList.Item key={truong.id} sx={{
                                    pb: 0
                                }}
                                    defaultOpen
                                >
                                    <FormControl sx={{
                                        mt: "-6px",
                                        pt: "6px",
                                        ml: "-8px",
                                        pl: "8px",
                                        pb: 2,

                                    }}>
                                        <Checkbox checked={truongChecked}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    props.onValueChanged([...props.gradeIds, ...khoiIdsTruong])
                                                } else {
                                                    props.onValueChanged(props.gradeIds.filter(x => !khoiIdsTruong.includes(x)))
                                                }
                                            }}
                                        />
                                        <FormControl.Label sx={{ fontWeight: 400, minWidth: "300px" }}>
                                            <Box className={styles.text}>
                                                {language === "en" ? truong.ten_truong_en : truong.ten_truong}
                                            </Box>
                                        </FormControl.Label>
                                    </FormControl>
                                    {truong_khois.length > 0 &&
                                        <NavList.SubNav>
                                            {truong_khois.map(khoi => {
                                                const khoiChecked = props.gradeIds.includes(khoi.id)
                                                return (
                                                    <NavList.Item sx={{
                                                        pb: 0
                                                    }}>
                                                        <FormControl
                                                            sx={{
                                                                mt: "-6px",
                                                                pt: "6px",
                                                                ml: "-8px",
                                                                pl: "16px",
                                                                pb: 2,

                                                            }}
                                                        >
                                                            <Checkbox checked={khoiChecked}
                                                                onChange={(e) => {
                                                                    if (e.target.checked) {
                                                                        props.onValueChanged([...props.gradeIds, khoi.id])
                                                                    } else {
                                                                        props.onValueChanged(props.gradeIds.filter(x => x !== khoi.id))
                                                                    }
                                                                }}
                                                            />
                                                            <FormControl.Label sx={{ fontWeight: 400, minWidth: "300px" }}>
                                                                <Box className={styles.text}>
                                                                    {language === "en" ? khoi.ten_khoi_en : khoi.ten_khoi}
                                                                </Box>
                                                            </FormControl.Label>
                                                        </FormControl>

                                                    </NavList.Item>
                                                );
                                            })}
                                        </NavList.SubNav>
                                    }
                                </NavList.Item>
                            );
                        })}
                    </>
                }
            </NavList>
        </Box>
    );
};

export default SccopeSelectionByGrade;