import { SearchIcon } from "@primer/octicons-react";
import { Box, Checkbox, FormControl, NavList } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useDebounce } from "use-debounce";
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { useLanguage } from '../../hooks/useLanguage';
import { actions } from "../../state/actions/actionsWrapper";
import { RootState } from '../../state/reducers';
import TextInput from '../ui/text-input';
import styles from "./SccopeSelectionByLop.module.css";
interface ISccopeSelectionByLopProps {
    lopIdsSelected: number[],
    onValueChanged: (ids: number[]) => void,
}
const SccopeSelectionByLop = (props: ISccopeSelectionByLopProps) => {
    const categorySource = useSelector((state: RootState) => state.categorySource);
    const { nam_hoc } = useCommonSelectedHook();
    const dm_khois = useSelector((state: RootState) => state.categorySource.dm_khois);
    const dm_truongs = useSelector((state: RootState) => state.auth.user_info?.campus);
    const { dm_lops, status } = useSelector((state: RootState) => state.danhMucWrapper.lop);
    const { language } = useLanguage();
    const dispatch = useDispatch();
    const [searchKey, setSearchKey] = useState("");
    const [searchKeyDelayed] = useDebounce(searchKey, 300)
    const filterdLop = useMemo(() => {
        if (!searchKeyDelayed) return dm_lops;
        return dm_lops.filter(x => x.ten_lop.includes(searchKeyDelayed))
    }, [dm_lops, searchKeyDelayed])
    const filterdTruong = useMemo(() => {
        const dm_truong_ids = filterdLop.map(x => x.dm_truong_id);
        return dm_truongs?.filter(x => dm_truong_ids.includes(x.id)) ?? []
    }, [filterdLop])
    const filterdKhoi = useMemo(() => {
        const dm_khoi_ids = filterdLop.map(x => x.dm_khoi_id);
        return dm_khois?.filter(x => dm_khoi_ids.includes(x.id)) ?? []
    }, [filterdLop])
    useEffect(() => {
        dispatch(actions.danhMucWrapper.lop.loadStart(nam_hoc || '', 0));
    }, [nam_hoc]);
    useEffect(() => {
        if (categorySource?.dm_khois?.length === 0) dispatch(actions.categorySource.loadKhoiStart());
    }, [categorySource]);

    return (
        <Box>
            <Box>
                <TextInput leadingVisual={SearchIcon}
                    placeholder={language === "en" ? 'Search' : 'Tìm kiếm'}
                    block
                    value={searchKey}
                    onChange={(e) => {
                        setSearchKey(e.target.value)
                    }}
                />
            </Box>
            <NavList>
                {filterdTruong &&
                    <>
                        {filterdTruong.map(truong => {
                            const truong_khois = filterdKhoi.filter(x => x.dm_truong_id === truong.id)
                            const lopsTruong = filterdLop.filter(x => x.dm_truong_id === truong.id);
                            const lopsIdTruong = lopsTruong.map(x => x.id);
                            const lopsSelectedTruong = lopsTruong.filter(x => props.lopIdsSelected.includes(x.id))
                            const truongChecked = lopsSelectedTruong.length > 0 && lopsSelectedTruong.length === lopsTruong.length;
                            return (
                                <NavList.Item key={truong.id} sx={{
                                    pb: 0
                                }}
                                    // defaultOpen
                                >
                                    <FormControl sx={{
                                        mt: "-6px",
                                        pt: "6px",
                                        ml: "-8px",
                                        pl: "8px",
                                        pb: 2,

                                    }}>
                                        <Checkbox checked={truongChecked}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    props.onValueChanged([...props.lopIdsSelected, ...lopsIdTruong])
                                                } else {
                                                    props.onValueChanged(props.lopIdsSelected.filter(x => !lopsIdTruong.includes(x)))
                                                }
                                            }}
                                        />
                                        <FormControl.Label sx={{ fontWeight: 400, minWidth: "300px" }}>
                                            <Box className={styles.text}>
                                                {truong.ten_truong}
                                            </Box>
                                        </FormControl.Label>
                                    </FormControl>
                                    {truong_khois.length > 0 &&
                                        <NavList.SubNav>
                                            {truong_khois.map(khoi => {
                                                const lops = filterdLop.filter(x => x.dm_khoi_id === khoi.id && x.dm_truong_id === truong.id);
                                                const lopsId = lops.map(x => x.id);
                                                const lopsSelected = lops.filter(x => props.lopIdsSelected.includes(x.id))
                                                const khoiChecked = lopsSelected.length > 0 && lopsSelected.length === lops.length;
                                                return (
                                                    <NavList.Item sx={{
                                                        pb: 0
                                                    }}
                                                        // defaultOpen={khoiChecked}
                                                    >
                                                        <FormControl
                                                            sx={{
                                                                mt: "-6px",
                                                                pt: "6px",
                                                                ml: "-8px",
                                                                pl: "16px",
                                                                pb: 2,

                                                            }}
                                                        >
                                                            <Checkbox checked={khoiChecked}
                                                                onChange={(e) => {
                                                                    if (e.target.checked) {
                                                                        props.onValueChanged([...props.lopIdsSelected, ...lopsId])
                                                                    } else {
                                                                        props.onValueChanged(props.lopIdsSelected.filter(x => !lopsId.includes(x)))
                                                                    }
                                                                }}
                                                            />
                                                            <FormControl.Label sx={{ fontWeight: 400, minWidth: "300px" }}>
                                                                <Box className={styles.text}>
                                                                    {khoi.ten_khoi}
                                                                </Box>
                                                            </FormControl.Label>
                                                        </FormControl>
                                                        {lops.length > 0 &&

                                                            <NavList.SubNav>
                                                                {lops.map(lop => {
                                                                    const lopChecked = props.lopIdsSelected.includes(lop.id)
                                                                    return (
                                                                        <NavList.Item sx={{
                                                                            pb: 0
                                                                        }}>
                                                                            <FormControl
                                                                                sx={{
                                                                                    mt: "-6px",
                                                                                    pt: "6px",
                                                                                    ml: "-8px",
                                                                                    pl: "24px",
                                                                                    pb: 2,

                                                                                }}
                                                                            >
                                                                                <Checkbox checked={lopChecked}
                                                                                    onChange={(e) => {
                                                                                        if (e.target.checked) {
                                                                                            props.onValueChanged([...props.lopIdsSelected, lop.id])
                                                                                        } else {
                                                                                            props.onValueChanged(props.lopIdsSelected.filter(x => x !== lop.id))
                                                                                        }
                                                                                    }}
                                                                                />
                                                                                <FormControl.Label sx={{ fontWeight: 400, minWidth: "300px" }}>
                                                                                    <Box className={styles.text}>
                                                                                        {lop.ten_lop}
                                                                                    </Box>

                                                                                </FormControl.Label>
                                                                            </FormControl>
                                                                        </NavList.Item>
                                                                    );
                                                                })}
                                                            </NavList.SubNav>

                                                        }
                                                    </NavList.Item>
                                                );
                                            })}
                                        </NavList.SubNav>
                                    }
                                </NavList.Item>
                            );
                        })}
                    </>
                }
            </NavList>
        </Box>
    );
};

export default SccopeSelectionByLop;