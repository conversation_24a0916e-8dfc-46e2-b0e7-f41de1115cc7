.container {
  padding: 16px;
}

.searchBox {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

.resultsContainer {
  max-height: 400px;
  overflow-y: auto;
}

.noResults {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.resultsList {
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  background: #fff;
}

.resultsHeader {
  padding: 12px 16px;
  background: #f6f8fa;
  border-bottom: 1px solid #e1e4e8;
  font-weight: 600;
  color: #24292e;
}

.resultItem {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e1e4e8;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.resultItem:hover {
  background-color: #f6f8fa;
}

.resultItem:last-child {
  border-bottom: none;
}

.avatarContainer {
  margin-right: 12px;
  flex-shrink: 0;
}

.infoContainer {
  flex: 1;
  min-width: 0;
}

.name {
  font-weight: 600;
  color: #24292e;
  margin-bottom: 4px;
}

.details {
  color: #586069;
  font-size: 14px;
  margin-bottom: 2px;
}

.email {
  color: #0366d6;
  font-size: 12px;
}
