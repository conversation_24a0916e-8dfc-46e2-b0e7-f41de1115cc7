import React, { useState, useEffect } from "react";
import { Box, Button, TextInput, Spinner } from "@primer/react";
import { SearchIcon } from "@primer/octicons-react";
import { phuHuynhApi } from "../../api/phuHuynhApi";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import UserAvatar from "../user-avatar";
import styles from "./SearchBoxPhuHuynhModal.module.css";

interface IPhuHuynhItemRespone {
  id: number;
  ho_ten: string;
  dien_thoai: string;
  email: string;
  quan_he: string;
  avatar?: string;
}

interface SearchBoxPhuHuynhModalProps {
  onPhuHuynhSelected: (phuHuynh: IPhuHuynhItemRespone) => void;
  placeholder?: string;
}

const SearchBoxPhuHuynhModal: React.FC<SearchBoxPhuHuynhModalProps> = ({
  onPhuHuynhSelected,
  placeholder = "Nhập tên hoặc số điện thoại phụ huynh...",
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [phuHuynhs, setPhuHuynhs] = useState<IPhuHuynhItemRespone[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const { dm_coso_id } = useCommonSelectedHook();

  const removeVietnameseTones = (str: string) => {
    return str
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/đ/g, "d")
      .replace(/Đ/g, "D");
  };

  const searchPhuHuynh = async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setPhuHuynhs([]);
      setHasSearched(false);
      return;
    }

    setLoading(true);
    try {
      const res = await phuHuynhApi.SelectAll(dm_coso_id, 0, 0);
      if (res.is_success) {
        const normalizedSearch = removeVietnameseTones(
          searchTerm.toLowerCase()
        );
        const filteredResults = res.data.filter(
          (item: IPhuHuynhItemRespone) => {
            const fullText =
              item.ho_ten + " - " + item.dien_thoai + " (" + item.quan_he + ")";
            return removeVietnameseTones(fullText.toLowerCase()).includes(
              normalizedSearch
            );
          }
        );

        setPhuHuynhs(filteredResults);
        setHasSearched(true);
      }
    } catch (error) {
      console.error("Error searching phụ huynh:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    searchPhuHuynh(searchTerm);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handlePhuHuynhClick = (phuHuynh: IPhuHuynhItemRespone) => {
    onPhuHuynhSelected(phuHuynh);
  };

  return (
    <Box className={styles.container}>
      <Box className={styles.searchBox}>
        <TextInput
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          leadingVisual={SearchIcon}
          sx={{ width: "100%" }}
        />
        <Button
          variant="primary"
          onClick={handleSearch}
          disabled={loading || !searchTerm.trim()}
          sx={{ ml: 2 }}
        >
          {loading ? <Spinner size="small" /> : "Tìm kiếm"}
        </Button>
      </Box>

      {loading && (
        <Box className={styles.loadingContainer}>
          <Spinner size="medium" />
          <Box sx={{ ml: 2 }}>Đang tìm kiếm...</Box>
        </Box>
      )}

      {hasSearched && !loading && (
        <Box className={styles.resultsContainer}>
          {phuHuynhs.length === 0 ? (
            <Box className={styles.noResults}>
              Không tìm thấy phụ huynh nào phù hợp
            </Box>
          ) : (
            <Box className={styles.resultsList}>
              <Box className={styles.resultsHeader}>
                Tìm thấy {phuHuynhs.length} kết quả:
              </Box>
              {phuHuynhs.map((phuHuynh) => (
                <Box
                  key={phuHuynh.id}
                  className={styles.resultItem}
                  onClick={() => handlePhuHuynhClick(phuHuynh)}
                >
                  <Box className={styles.avatarContainer}>
                    <UserAvatar
                      fullName={phuHuynh.ho_ten}
                      size="medium"
                      url={phuHuynh.avatar}
                    />
                  </Box>
                  <Box className={styles.infoContainer}>
                    <Box className={styles.name}>{phuHuynh.ho_ten}</Box>
                    <Box className={styles.details}>
                      {phuHuynh.dien_thoai} - {phuHuynh.quan_he}
                    </Box>
                    {phuHuynh.email && (
                      <Box className={styles.email}>{phuHuynh.email}</Box>
                    )}
                  </Box>
                </Box>
              ))}
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

export default SearchBoxPhuHuynhModal;
