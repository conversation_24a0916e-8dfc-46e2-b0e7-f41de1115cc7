import {
  TriangleDownIcon,
  XCircleFillIcon,
  SearchIcon,
} from "@primer/octicons-react";
import { Box, Button, SelectPanel, Spinner, ActionList } from "@primer/react";
import { useEffect, useMemo, useState, useRef } from "react";
import { phuHuynhApi } from "../../api/phuHuynhApi";
import { useCommonContext } from "../../contexts/common";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { IPhuHuynhItemRespone } from "../../models/response/phu-huynh/IPhuHuynhItemRespone";
import { ItemInput } from "@primer/react/lib/deprecated/ActionList/List";
import UserAvatar from "../user-avatar";

type SearchBoxPhuHuynhProps = {
  isReadonly?: boolean;
  value?: number;
  onValueChanged: (id: number, data?: IPhuHuynhItemRespone) => void;
  className?: string;
  preText?: string;
  width?: string | number;
  stylingMode?: "outlined" | "filled" | "underlined";
  isShowClearBtn?: boolean;
  maxWidth?: any;
  onRowClick?: (id: number) => void;
};

const SearchBoxPhuHuynh = (props: SearchBoxPhuHuynhProps) => {
  const { translate } = useCommonContext();
  const [phuHuynhs, setPhuHuynhs] = useState<IPhuHuynhItemRespone[]>([]);
  const { dm_coso_id } = useCommonSelectedHook();
  const [filter, setFilter] = useState("");
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const timeoutRef = useRef<number | null>(null);
  const [hasSearched, setHasSearched] = useState(false);
  const [shouldResetFilterOnNextRender, setShouldResetFilterOnNextRender] =
    useState(false);

  const searchPhuHuynh = async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setPhuHuynhs([]);
      setHasSearched(false);
      return;
    }

    setLoading(true);
    try {
      const res = await phuHuynhApi.SelectAll(dm_coso_id, 0, 0);
      if (res.is_success) {
        const normalizedSearch = removeVietnameseTones(
          searchTerm.toLowerCase()
        );
        const filteredResults = res.data.filter(
          (item: IPhuHuynhItemRespone) => {
            const fullText =
              item.ho_ten + " - " + item.dien_thoai + " (" + item.quan_he + ")";
            return removeVietnameseTones(fullText.toLowerCase()).includes(
              normalizedSearch
            );
          }
        );

        setPhuHuynhs(filteredResults);
        setHasSearched(true);
      }
    } catch (error) {
      console.error("Error searching phụ huynh:", error);
    } finally {
      setLoading(false);
    }
  };

  const debouncedSearch = (searchTerm: string) => {
    if (timeoutRef.current) {
      window.clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = window.setTimeout(() => {
      searchPhuHuynh(searchTerm);
    }, 300);
  };

  useEffect(() => {
    if (shouldResetFilterOnNextRender) {
      setFilter("");
      setShouldResetFilterOnNextRender(false);
      return;
    }

    if (open && filter.length > 0) {
      debouncedSearch(filter);
    } else if (open && filter.length === 0) {
      setPhuHuynhs([]);
      setHasSearched(false);
    }

    return () => {
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
      }
    };
  }, [filter, open, shouldResetFilterOnNextRender]);

  useEffect(() => {
    const loadSelectedPhuHuynh = async () => {
      if (props.value && props.value > 0) {
        try {
          const res = await phuHuynhApi.getById(props.value);
          if (res.is_success && res.data) {
            setPhuHuynhs((prevState) => {
              if (
                !prevState.some(
                  (p: IPhuHuynhItemRespone) => p.id === res.data.id
                )
              ) {
                return [res.data, ...prevState];
              }
              return prevState;
            });
          }
        } catch (error) {
          console.error("Error loading selected phụ huynh:", error);
        }
      }
    };

    loadSelectedPhuHuynh();
  }, [props.value]);

  const removeVietnameseTones = (str: string) => {
    return str
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/đ/g, "d")
      .replace(/Đ/g, "D");
  };

  const dataSource = useMemo(() => {
    return phuHuynhs.map((x: IPhuHuynhItemRespone) => ({
      id: x.id,
      text: x.ho_ten + ` - ` + x.dien_thoai + ` (` + x.quan_he + `)`,
    }));
  }, [phuHuynhs]);

  const _selectedData = useMemo(() => {
    return dataSource.find((item) => item.id === props.value);
  }, [props.value, dataSource]);

  const renderItem = (item: any) => {
    // Tìm đối tượng phụ huynh đầy đủ
    const phuHuynh = phuHuynhs.find(x => x.id === item.id);
    
    return (
        <ActionList.Item
            sx={{
                cursor: 'pointer',
                padding: '8px 12px',
                '&:hover': {
                    backgroundColor: 'accent.subtle',
                    color: 'accent.fg'
                }
            }}
            onClick={(e) => {
                e.stopPropagation();
                if (props.onRowClick) {
                    props.onRowClick(item.id);
                } else {
                    props.onValueChanged(item.id, phuHuynh);
                    setOpen(false);
                }
            }}
        >
            <Box sx={{ 
                display: 'flex', 
                alignItems: 'center',
                width: '100%',
                gap: '12px'
            }}>
                <UserAvatar fullName={phuHuynh?.ho_ten} size="medium" />
                
                <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column',
                    flex: 1
                }}>
                    <Box sx={{ 
                        display: 'flex', 
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        width: '100%'
                    }}>
                        <Box sx={{ 
                            fontWeight: 'bold', 
                            fontSize: '14px',
                            color: 'fg.default'
                        }}>
                            {phuHuynh?.ho_ten} ({phuHuynh?.quan_he})
                        </Box>                      
                    </Box>
                    <Box sx={{ 
                        fontSize: '13px', 
                        color: 'fg.muted',
                        marginTop: '4px'
                    }}>
                        SĐT: {phuHuynh?.dien_thoai}
                    </Box>
                </Box>
            </Box>
        </ActionList.Item>
    );
};

  const handleButtonClick = () => {
    setShouldResetFilterOnNextRender(true);
    setOpen(true);
  };

  return (
    <SelectPanel
      key={open ? "open" : "closed"}
      renderAnchor={({
        children,
        "aria-labelledby": ariaLabelledBy,
        ...anchorProps
      }) => (
        <Button
          sx={{
            width: "100%",
            display: "flex",
            justifyContent: "space-between",
          }}
          leadingVisual={SearchIcon}
          trailingAction={TriangleDownIcon}
          aria-labelledby={` ${ariaLabelledBy}`}
          onClick={handleButtonClick}
          {...anchorProps}
        >
          <p
            style={{
              maxWidth: props.maxWidth,
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {children || translate(`Tìm phụ huynh`)}
          </p>
        </Button>
      )}
      title={
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box sx={{ flex: 1 }}>
            {loading ? (
              <>
                Đang tìm kiếm... <Spinner size="small" sx={{ ml: 2 }} />
              </>
            ) : hasSearched ? (
              phuHuynhs.length === 0 ? (
                "Không tìm thấy phụ huynh nào phù hợp"
              ) : (
                "Tìm kiếm phụ huynh"
              )
            ) : (
              "Nhập từ khóa để tìm kiếm phụ huynh"
            )}
          </Box>
          {props.isShowClearBtn && (props.value ?? 0) > 0 && (
            <Button
              trailingVisual={XCircleFillIcon}
              variant="invisible"
              sx={{
                color: "danger.emphasis",
              }}
              onClick={() => {
                props.onValueChanged(0);
              }}
            >
              Bỏ chọn
            </Button>
          )}
        </Box>
      }
      textInputProps={{
        placeholder: "Nhập họ tên, số điện thoại...",
        leadingVisual: SearchIcon,
        autoFocus: true,
      }}
      open={props.isReadonly === false ? open : false}
      onOpenChange={(isOpen: boolean) => {
        setOpen(isOpen);
        if (isOpen) {
          setShouldResetFilterOnNextRender(true);
          setPhuHuynhs([]);
          setHasSearched(false);
        }
      }}
      items={dataSource}
      selected={_selectedData}
      renderItem={renderItem}
      onSelectedChange={(selected: ItemInput | undefined) => {
        if (selected && !props.onRowClick) {
          const selectedId = (selected as any).id;
          props.onValueChanged(
            selectedId,
            phuHuynhs.find((x) => x.id === selectedId)
          );
          setOpen(false);
        } else if (!selected) {
          props.onValueChanged(0);
          setOpen(false);
        }
      }}
      onFilterChange={(value: string) => {
        if (!shouldResetFilterOnNextRender) {
          setFilter(value);
        }
      }}
      showItemDividers={true}
      overlayProps={{
        width: "large",
        height: "medium",
      }}
    />
  );
};

export default SearchBoxPhuHuynh;
