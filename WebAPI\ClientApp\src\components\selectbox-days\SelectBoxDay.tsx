import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, Checkbox, FormControl, SelectPanel } from '@primer/react';
import { useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { useLanguage } from '../../hooks/useLanguage';
import { useThus } from '../../hooks/useThus';
type ISelectBoxRoleProps = {

    isReadonly?: boolean;
    value: number[];
    onValueChanged: (ids: number[]) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean,
    maxWidth?: any
};

const SelectBoxDay = (props: ISelectBoxRoleProps) => {
    const { translate } = useCommonContext();
    const { thus } = useThus();
    const { language } = useLanguage();
    const filterdData = useMemo(() => {
        return thus.map(x => {
            return {
                id: x.id,
                text: language === "en" ? x.name_en : x.name
            }
        });
    }, [thus, language])
    const isSelectedAll = filterdData.length > 0 && filterdData.map(x => x.id).find(id => !props.value.includes(id)) === undefined;
    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const _selectedDatas = useMemo(() => {
        return filterdData.filter(item => props.value.includes(item.id))
    }, [props.value, filterdData])
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button sx={{
                    maxWidth: 300
                }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate(`SelectBoxDay.PlaceHolder`)}
                    </p>
                </Button>
            )}
            title={<>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        <FormControl>
                            <Checkbox checked={isSelectedAll} onChange={(e) => {
                                if (e.target.checked) {
                                    props.onValueChanged(filterdData.map(x => x.id))
                                } else {
                                    props.onValueChanged([])
                                }
                            }} />
                            <FormControl.Label>Select all</FormControl.Label>
                        </FormControl>
                    </Box>
                    {props.isShowClearBtn && props.value.length > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => {
                                props.onValueChanged([])
                            }}
                        >
                            Clear
                        </Button>
                    }
                </Box>
            </>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedDatas}
            onSelectedChange={(data: any) => {
                props.onValueChanged(data.map((x: any) => x.id))
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'auto', height: 'auto' }}
        />
    );
};

export default SelectBoxDay;