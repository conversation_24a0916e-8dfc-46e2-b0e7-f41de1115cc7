import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, Checkbox, FormControl, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';


interface ISelectBoxTHeMultipleProps {
    selectedValue: number[];
    onSelectionChanged: (dm_he_ids: number[]) => void;
    maxWidth?: number;
    isShowClearBtn?: boolean
}

const SelectBoxHeMultiple = (props: ISelectBoxTHeMultipleProps) => {
    const { translate } = useCommonContext();
    const { language } = useSelector((x: RootState) => x.common);
    const dispatch = useDispatch();
    const { selectedValue, onSelectionChanged } = props;
    const [filter, setFilter] = useState('')
    const [open, setOpen] = useState(false)
    const categorySource = useSelector((state: RootState) => state.categorySource);

    useEffect(() => {
        if (categorySource.dm_hes.length === 0) dispatch(actions.categorySource.loadHeStart());
    }, []);
    const dataSource = useMemo(() => {
        return (categorySource.dm_hes ?? []).map(x => {
            const item: any = {

                id: x.id,
                text: language === "en" ? x.ten_he_en : x.ten_he,
            }
            return item;
        })
    }, [categorySource.dm_hes, filter, language])
    const filterdData = useMemo(() => {
        return dataSource.filter(item =>
            item.text.toLowerCase().includes(filter.toLowerCase())
        )
    }, [dataSource, filter])
    const _selectedDatas = useMemo(() => {
        return dataSource.filter(item => selectedValue.includes(item.id))
    }, [selectedValue, dataSource])
    const isSelectedAll = filterdData.length > 0 && filterdData.map(x => x.id).find(id => !selectedValue.includes(id)) === undefined;

    return (
        <>
            <SelectPanel
                renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                    <Button sx={{
                        maxWidth: props.maxWidth ?? 300
                    }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                        <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                            {children || translate(`Chọn hệ`)}
                        </p>
                    </Button>
                )}
                title={<>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <Box sx={{ flex: 1 }}>
                            <FormControl>
                                <Checkbox checked={isSelectedAll} onChange={(e) => {
                                    if (e.target.checked) {
                                        props.onSelectionChanged(filterdData.map(x => x.id))
                                    } else {
                                        props.onSelectionChanged([])
                                    }
                                }} />
                                <FormControl.Label>Select all</FormControl.Label>
                            </FormControl>
                        </Box>
                        {props.isShowClearBtn && selectedValue.length > 0 &&
                            <Button
                                trailingVisual={XCircleFillIcon}
                                variant='invisible'
                                sx={{
                                    color: "danger.emphasis"
                                }}
                                onClick={() => {
                                    props.onSelectionChanged([])
                                }}
                            >
                                Bỏ chọn
                            </Button>
                        }
                    </Box>
                </>}
                placeholderText="Search"
                open={open}

                onOpenChange={setOpen}
                items={filterdData}
                selected={_selectedDatas}
                onSelectedChange={(data: any) => {
                    props.onSelectionChanged(data.map((x: any) => x.id))
                }}
                onFilterChange={setFilter}
                showItemDividers={true}
                overlayProps={{ width: 'large', height: 'medium' }}
            />
        </>
    );
};

export default SelectBoxHeMultiple;
