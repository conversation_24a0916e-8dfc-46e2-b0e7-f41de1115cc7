import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';

import { Box, Button, SelectPanel } from '@primer/react';
import { hinhThucNopBomApi } from '../../api/hinhThucNopBomApi';
import { IHinhThucNopBom } from '../../models/response/finance/IHinhThucNopBom';
type ISelectBoxRoleProps = {

    isReadonly?: boolean;
    value: number[];
    onValueChanged: (ids: number[]) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean,
    maxWidth?: any
};

const SelectBoxRole = (props: ISelectBoxRoleProps) => {
    const { translate } = useCommonContext();
    const [hinhThucNopBoms, setHinhThucNopBoms] = useState<IHinhThucNopBom[]>([]);
    const handleReloadHinhThucHinhThucNopBom = async () => {
        const res = await hinhThucNopBomApi.selectAll();
        if (res.is_success) {
            setHinhThucNopBoms(res.data);
        }
    };
    useEffect(() => {
        handleReloadHinhThucHinhThucNopBom();
    }, []);



    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const dataSource = useMemo(() => {
        return hinhThucNopBoms.filter(x => x.id > 0).map(x => {
            const item: any = {
                id: x.id,
                text: x.name
            }
            return item;
        })
    }, [hinhThucNopBoms])
    const filterdData = useMemo(() => {
        return dataSource.filter(item =>
            item.text.toLowerCase().includes(filter.toLowerCase())
        )
    }, [dataSource, filter])
    const _selectedDatas = useMemo(() => {
        return dataSource.filter(item => props.value.includes(item.id))
    }, [props.value, dataSource])
    return (

        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button sx={{
                    maxWidth: 500
                }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate(`Chọn hình thức nộp`)}
                    </p>
                </Button>
            )}
            title={<>
                <Box sx={{ display: "flex", flex: "flex-end" }}>
                    {props.isShowClearBtn && props.value.length > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => {
                                props.onValueChanged([])
                            }}
                        >
                            Bỏ chọn
                        </Button>
                    }
                </Box>
            </>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedDatas}
            onSelectedChange={(data: any) => {
                props.onValueChanged(data.map((x: any) => x.id))
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'medium', height: 'medium' }}
        />

    );
};

export default SelectBoxRole;
