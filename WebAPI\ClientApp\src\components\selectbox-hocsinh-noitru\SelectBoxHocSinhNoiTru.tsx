import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { hocSinhApi } from '../../api/hocSinhApi';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { IHocSinhModel } from '../../models/response/hoc-sinh/IHocSinhModel';
import { hocSinhNoiTruApi } from '../../api/hocSinhNoiTruApi';
import { IHocSinhNoiTruItemResponse } from '../../models/response/noi-tru/IHocSinhNoiTruItemResponse';

type ComboboxHocSinhProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: {
        id: number;
        // dm_truong_id: number;
        // dm_khoi_id: number;
        // dm_he_id: number;
        // dm_lop_id: number;
        ma_hs: string;
        ho_ten: string;
        ten_khoi?: string;
        ten_lop?: string;
    }) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean;
    maxWidth?: any;
};

const SelectBoxHocSinhNoiTru = (props: ComboboxHocSinhProps) => {
    const { translate } = useCommonContext();
    const [hocSinhs, setHocSinhs] = useState<IHocSinhModel[]>([]);
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
    const [filter, setFilter] = useState('');
    const [open, setOpen] = useState(false);

    const handleReloadHocSinh = async () => {
        try {
          // Lấy danh sách học sinh đăng ký nội trú 
          const res = await hocSinhApi.SelectViewShortNoiTru({
            nam_hoc: nam_hoc, 
            dm_coso_id: dm_coso_id,
            dm_trangthaihocsinh_ids: [],
            dm_lop_id: 0,
            dm_truong_id: 0,
            dm_he_id: 0, 
            dm_khoi_id: 0
          });
       
          // Lấy danh sách học sinh đã đăng ký nội trú và lọc theo năm học
          const list_dadangky_noitru = await hocSinhNoiTruApi.selectAll();
          const filteredRegistered = list_dadangky_noitru.data.filter(
            (x: IHocSinhNoiTruItemResponse) => x.nam_hoc === nam_hoc
          );
       
          if (res.is_success && list_dadangky_noitru.is_success) {
            const registeredStudentIds = new Set(
              filteredRegistered.map((student: IHocSinhNoiTruItemResponse) => student.ts_hocsinh_id)
            );
       
            const unregisteredStudents = res.data.filter(
              (student: IHocSinhModel) => !registeredStudentIds.has(student.id)
            );
       
            setHocSinhs(unregisteredStudents);
          }
        } catch (error) {
          console.error('Error loading students:', error);
        }
       };
    

    useEffect(() => {
        handleReloadHocSinh();
    }, []);

    const dataSource = useMemo(() => hocSinhs.map(x => ({
        id: x.id,
        text: x.ma_hs + ' - ' + x.ho_ten + (x.ten_khoi ? ' - ' + x.ten_khoi : '') + (x.ten_lop ? ' - ' + x.ten_lop : ''),
        fullData: {
            id: x.id,
            dm_truong_id: x.dm_truong_id,
            dm_khoi_id: x.dm_khoi_id,
            dm_he_id: x.dm_he_id,
            dm_lop_id: x.dm_lop_id,
            ma_hs: x.ma_hs,
            ho_ten: x.ho_ten,
            ten_khoi: x.ten_khoi,
            ten_lop: x.ten_lop
        }
    })), [hocSinhs]);

    const removeVietnameseTones = (str: string) => {
        return str
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/đ/g, "d")
            .replace(/Đ/g, "D");
    };

    const filterdData = useMemo(() => {
        const normalizedFilter = removeVietnameseTones(filter.toLowerCase());
        return dataSource.filter(item =>
            removeVietnameseTones(item.text.toLowerCase()).includes(normalizedFilter)
        );
    }, [dataSource, filter]);

    const _selectedData = useMemo(() => 
        dataSource.find(item => item.id === props.value)
    , [props.value, dataSource]);

    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between'
                }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate('Chọn học sinh')}
                    </p>
                </Button>
            )}
            title={
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        Chọn học sinh
                    </Box>
                    {props.isShowClearBtn && (props.value ?? 0) > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => props.onValueChanged(0)}
                        >
                            Bỏ chọn
                        </Button>
                    }
                </Box>
            }
            placeholderText="Search"
            open={props.isReadonly === false ? open : false}
            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedData}
            onSelectedChange={(data: any) => {
                const selectedHocSinh = dataSource.find(x => x.id === data.id);
                props.onValueChanged(data.id, selectedHocSinh?.fullData);
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />
    );
};

export default SelectBoxHocSinhNoiTru;