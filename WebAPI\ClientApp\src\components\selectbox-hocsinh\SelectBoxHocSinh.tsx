
import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { hocSinhApi } from '../../api/hocSinhApi';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { IHocSinhModel } from '../../models/response/hoc-sinh/IHocSinhModel';
type ComboboxHocSinhProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: IHocSinhModel) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean,
    maxWidth?: any
};
const SelectBoxHocSinh = (props: ComboboxHocSinhProps) => {
    const { translate } = useCommonContext();
    const [hocSinhs, setHocSinhs] = useState<IHocSinhModel[]>([]);
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

    const handleReloadHocSinh = async () => {
        const res = await hocSinhApi.SelectViewShort({
            nam_hoc: nam_hoc,
            dm_coso_id: dm_coso_id,
            dm_trangthaihocsinh_ids: [],
            dm_lop_id: 0,
            dm_truong_id: 0,
            dm_he_id: 0,
            dm_khoi_id: 0
        });
        if (res.is_success) {
            setHocSinhs(res.data);
        }
    };

    useEffect(() => {
        handleReloadHocSinh();
    }, []);

    const [filter, setFilter] = useState('')
    const [open, setOpen] = useState(false)
    const dataSource = useMemo(() => {
        return hocSinhs.map(x => {
            const item: any = {
                id: x.id,
                text: x.ma_hs + ` - ` + x.ho_ten + (x.ten_khoi != null ? (` - ` + x.ten_khoi) : '') + (x.ten_lop != null ? (` - ` + x.ten_lop) : ''),
            }
            return item;
        })
    }, [hocSinhs, filter])

    const removeVietnameseTones = (str: string) => {
        return str
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/đ/g, "d")
            .replace(/Đ/g, "D");
    };
    const filterdData = useMemo(() => {
        const normalizedFilter = removeVietnameseTones(filter.toLowerCase());
        return dataSource.filter(item =>
            removeVietnameseTones(item.text.toLowerCase()).includes(normalizedFilter)
        );
    }, [dataSource, filter]);

    const _selectedData = useMemo(() => {
        return dataSource.find(item => item.id === props.value)
    }, [props.value, dataSource])
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between'
                }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate(`Chọn học sinh`)}
                    </p>
                </Button>
            )}
            title={<>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        Chọn học sinh
                    </Box>
                    {props.isShowClearBtn && (props.value ?? 0) > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => {
                                props.onValueChanged(0)
                            }}
                        >
                            Bỏ chọn
                        </Button>
                    }
                </Box>
            </>}
            placeholderText="Search"
            open={props.isReadonly == false ? open : false}

            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedData}
            onSelectedChange={(data: any) => {
                props.onValueChanged(data.id, hocSinhs.find(x => x.id === data.id))
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />

    );
};

export default SelectBoxHocSinh;
