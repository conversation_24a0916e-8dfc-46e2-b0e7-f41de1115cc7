import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, Checkbox, FormControl, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { lopApi } from '../../api/lopApi';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { dm_lop_viewmodel } from '../../models/response/dm-lop/dm_lop';
import { RootState } from '../../state/reducers';


interface ISelectBoxLopMultipleProps {
    selectedValue: number[];
    onSelectionChanged: (dm_lop_ids: number[]) => void;
    maxWidth?: number;
    isShowClearBtn?: boolean,
    dm_truong_ids: number[];
    dm_khoi_ids: number[];

}

const SelectBoxLopMultiple = (props: ISelectBoxLopMultipleProps) => {
    const { translate } = useCommonContext();
    const { language } = useSelector((x: RootState) => x.common);
    const { dm_coso_id, nam_hoc } = useCommonSelectedHook();
    const [lops, setLops] = useState<dm_lop_viewmodel[]>([]);

    const dispatch = useDispatch();
    const { selectedValue, onSelectionChanged } = props;
    const [filter, setFilter] = useState('')
    const [open, setOpen] = useState(false)
    const handleReloadLops = async () => {
        const res = await lopApi.Select({
            nam_hoc: nam_hoc || '',
            dm_he_id: 0,
            dm_khoi_id: 0,
            dm_truong_id: 0,
        });
        if (res.is_success) {
            setLops(res.data);
        }
    };

    useEffect(() => {
        handleReloadLops();
    }, []);

    const dataSource = useMemo(() => {
        let filterData = [...lops]

        if (props.dm_truong_ids.length > 0) {
            filterData = filterData.filter((x: any) => props.dm_truong_ids.includes(x.dm_truong_id))
        }
        if (props.dm_khoi_ids.length > 0) {
            filterData = filterData.filter((x: any) => props.dm_khoi_ids.includes(x.dm_khoi_id))
        }
        let temp = filterData = filterData.map(x => {
            const item: any = {
                id: x.id,
                text: language === "en" ? x.ten_lop : x.ten_lop,
            }
            return item;
        })
        return temp;
    }, [lops, filter, language, props.dm_truong_ids, props.dm_khoi_ids])
    const filterdData = useMemo(() => {
        return dataSource.filter(item =>
            item.text.toLowerCase().includes(filter.toLowerCase())
        )
    }, [dataSource, filter])
    const _selectedDatas = useMemo(() => {
        return dataSource.filter(item => selectedValue.includes(item.id))
    }, [selectedValue, dataSource])
    const isSelectedAll = filterdData.length > 0 && filterdData.map(x => x.id).find(id => !selectedValue.includes(id)) === undefined;

    return (
        <>
            <SelectPanel
                renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                    <Button sx={{
                        maxWidth: props.maxWidth ?? 300
                    }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                        <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                            {children || translate(`Chọn lớp`)}
                        </p>
                    </Button>
                )}
                title={<>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <Box sx={{ flex: 1 }}>
                            <FormControl>
                                <Checkbox checked={isSelectedAll} onChange={(e) => {
                                    if (e.target.checked) {
                                        props.onSelectionChanged(filterdData.map(x => x.id))
                                    } else {
                                        props.onSelectionChanged([])
                                    }
                                }} />
                                <FormControl.Label>Select all</FormControl.Label>
                            </FormControl>
                        </Box>
                        {props.isShowClearBtn && selectedValue.length > 0 &&
                            <Button
                                trailingVisual={XCircleFillIcon}
                                variant='invisible'
                                sx={{
                                    color: "danger.emphasis"
                                }}
                                onClick={() => {
                                    props.onSelectionChanged([])
                                }}
                            >
                                Bỏ chọn
                            </Button>
                        }
                    </Box>
                </>}
                placeholderText="Search"
                open={open}

                onOpenChange={setOpen}
                items={filterdData}
                selected={_selectedDatas}
                onSelectedChange={(data: any) => {
                    props.onSelectionChanged(data.map((x: any) => x.id))
                }}
                onFilterChange={setFilter}
                showItemDividers={true}
                overlayProps={{ width: 'large', height: 'medium' }}
            />
        </>
    );
};

export default SelectBoxLopMultiple;
