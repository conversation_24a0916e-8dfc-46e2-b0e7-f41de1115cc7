import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { hinhThucMienGiamApi } from '../../api/hinhThucMIenGiamApi';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { sf_hinhthucmiengiam } from '../../models/response/hinh-thuc-mien-giam/sf_hinhthucmiengiam';
type ComboboxMienGiamProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: sf_hinhthucmiengiam) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean,
    maxWidth?: any
};
const SelectBoxMienGiam = (props: ComboboxMienGiamProps) => {
    const { translate } = useCommonContext();
    const [hinhThucMienGiams, setHinhThucMienGiams] = useState<sf_hinhthucmiengiam[]>([]);
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

    const handleReloadHinhThucMienGiam = async () => {
        const res = await hinhThucMienGiamApi.selectAllByNamHocCoso(
            nam_hoc,
            dm_coso_id
        );
        if (res.is_success) {
            setHinhThucMienGiams(res.data);
        }
    };

    useEffect(() => {
        handleReloadHinhThucMienGiam();
    }, []);

    const [filter, setFilter] = useState('')
    const [open, setOpen] = useState(false)
    const dataSource = useMemo(() => {
        return hinhThucMienGiams.map(x => {
            const item: any = {
                id: x.id,
                text: x.ma_hinh_thuc + '-' + x.hinh_thuc_mien_giam,
            }
            return item;
        })
    }, [hinhThucMienGiams, filter])

    const removeVietnameseTones = (str: string) => {
        return str
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/đ/g, "d")
            .replace(/Đ/g, "D");
    };
    const filterdData = useMemo(() => {
        const normalizedFilter = removeVietnameseTones(filter.toLowerCase());
        return dataSource.filter(item =>
            removeVietnameseTones(item.text.toLowerCase()).includes(normalizedFilter)
        );
    }, [dataSource, filter]);

    const _selectedData = useMemo(() => {
        return dataSource.find(item => item.id === props.value)
    }, [props.value, dataSource])
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button
                    sx={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between',
                    }}
                    trailingAction={TriangleDownIcon}
                    aria-labelledby={ariaLabelledBy}
                    {...anchorProps}
                >
                    <p style={{ maxWidth: props.maxWidth, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {children || translate('Chọn ưu đãi, miễn giảm')}
                    </p>
                </Button>
            )}
            title={<>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        Chọn ưu đãi, miễn giảm
                    </Box>
                    {props.isShowClearBtn && (props.value ?? 0) > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => {
                                props.onValueChanged(0)
                            }}
                        >
                            Bỏ chọn
                        </Button>
                    }
                </Box>
            </>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedData}
            onSelectedChange={(data: any) => {
                props.onValueChanged(data.id, hinhThucMienGiams.find(x => x.id === data.id))
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />

    );
};

export default SelectBoxMienGiam;