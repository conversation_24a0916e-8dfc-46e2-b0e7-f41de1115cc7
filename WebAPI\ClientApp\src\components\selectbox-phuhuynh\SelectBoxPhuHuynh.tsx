
import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { phuHuynhApi } from '../../api/phuHuynhApi';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { IPhuHuynhItemRespone } from '../../models/response/phu-huynh/IPhuHuynhItemRespone';
type ComboboxPhuHuynhProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: IPhuHuynhItemRespone) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean,
    maxWidth?: any
};
const SelectBoxPhuHuynh = (props: ComboboxPhuHuynhProps) => {
    const { translate } = useCommonContext();
    const [phuHuynhs, setPhuHuynhs] = useState<IPhuHuynhItemRespone[]>([]);
    const { dm_coso_id } = useCommonSelectedHook();
    const handleReloadPhuHuynh = async () => {
        const res = await phuHuynhApi.SelectAll(dm_coso_id, 0, 0);
        if (res.is_success) {
            setPhuHuynhs(res.data);
        }
    };

    useEffect(() => {
        handleReloadPhuHuynh();
    }, []);

    const [filter, setFilter] = useState('')
    const [open, setOpen] = useState(false)
    const dataSource = useMemo(() => {
        return phuHuynhs.map(x => {
            const item: any = {
                id: x.id,
                text: x.ho_ten + ` - ` + x.dien_thoai + ` (` + x.quan_he + `)`
            }
            return item;
        })
    }, [phuHuynhs, filter])

    const removeVietnameseTones = (str: string) => {
        return str
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/đ/g, "d")
            .replace(/Đ/g, "D");
    };
    const filterdData = useMemo(() => {
        const normalizedFilter = removeVietnameseTones(filter.toLowerCase());
        return dataSource.filter(item =>
            removeVietnameseTones(item.text.toLowerCase()).includes(normalizedFilter)
        );
    }, [dataSource, filter]);

    const _selectedData = useMemo(() => {
        return dataSource.find(item => item.id === props.value)
    }, [props.value, dataSource])
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between'
                }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate(`Chọn phụ huynh`)}
                    </p>
                </Button>
            )}
            title={<>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        Chọn phụ huynh
                    </Box>
                    {props.isShowClearBtn && (props.value ?? 0) > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => {
                                props.onValueChanged(0)
                            }}
                        >
                            Bỏ chọn
                        </Button>
                    }
                </Box>
            </>}
            placeholderText="Search"
            open={props.isReadonly == false ? open : false}

            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedData}
            onSelectedChange={(data: any) => {
                props.onValueChanged(data.id, phuHuynhs.find(x => x.id === data.id))
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />

    );
};

export default SelectBoxPhuHuynh;
