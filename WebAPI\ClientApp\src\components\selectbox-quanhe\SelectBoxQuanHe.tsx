import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { quanHeApi } from '../../api/quanHeApi';

type SelectBoxMultipleTrangThaiEmailProps = {
    isReadonly?: boolean;
    value?: number[];
    onValueChanged: (ids: number[]) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean;
    maxWidth?: any;
};
const SelectBoxQuanHe = (props: SelectBoxMultipleTrangThaiEmailProps) => {
    const [QuanHe, setQuanHe] = useState<any[]>([]);
    const [filter, setFilter] = useState('');
    const [open, setOpen] = useState(false);
    const removeVietnameseTones = (str: string) => {
        return str
            .normalize('NFD')
            .replace(/[̀-ͯ]/g, '')
            .replace(/đ/g, 'd')
            .replace(/Đ/g, 'D');
    };
    useEffect(() => {
        handleGetQuanHeAsync();
    }, [])
    const dataSource = useMemo(() => {
        return QuanHe.map(x => {
            const item: any = {
                id: x.id,
                text: x.quan_he,
            }
            return item;
        })
    }, [QuanHe, filter])
    const _selectedData = useMemo(() => {
        return dataSource.filter(item => props.value?.includes(item.id));
    }, [props.value, dataSource]);
    const handleGetQuanHeAsync = async () => {
        const res = await quanHeApi.selectAll();
        if (res.is_success) {
            setQuanHe(res.data)
        }
    }
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button
                    sx={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}
                    trailingAction={TriangleDownIcon}
                    aria-labelledby={ariaLabelledBy}
                    {...anchorProps}
                >
                    <p style={{ maxWidth: props.maxWidth, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {children || 'Chọn quan hệ'}
                    </p>
                </Button>
            )}

            title={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ flex: 1 }}>Chọn quan hệ</Box>
                    {props.isShowClearBtn && (props.value?.length ?? 0) > 0 && (
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{ color: 'danger.emphasis' }}
                            onClick={() => props.onValueChanged([])}
                        >
                            Bỏ chọn
                        </Button>
                    )}
                </Box>
            }
            placeholderText='Search'
            open={open}
            onOpenChange={setOpen}
            items={dataSource}
            selected={_selectedData}
            onSelectedChange={(selectedItems: any[]) => {
                const newValues = selectedItems.map(item => item.id);
                props.onValueChanged(newValues);
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />
    );
};

export default SelectBoxQuanHe;
