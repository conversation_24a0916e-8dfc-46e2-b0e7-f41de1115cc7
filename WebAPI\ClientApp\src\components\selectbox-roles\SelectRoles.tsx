import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useCommonContext } from '../../contexts/common';

import { Box, Button, Checkbox, FormControl, SelectPanel } from '@primer/react';
import { useRoles } from '../../hooks/useRoles';
type ISelectBoxRoleProps = {

    isReadonly?: boolean;
    value: number[];
    onValueChanged: (ids: number[]) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean,
    maxWidth?: any
};

const SelectBoxRole = (props: ISelectBoxRoleProps) => {
    const { translate } = useCommonContext();
    const dispatch = useDispatch();
    const { roles } = useRoles();



    const [open, setOpen] = useState(false)
    const [filter, setFilter] = useState('')
    const dataSource = useMemo(() => {
        return roles.map(x => {
            const item: any = {
                id: x.id,
                text: x.name,
                // search: `${x.ho_ten} ${x.email} ${x.phone_number} ${x.ma_nv}`,
                // trailingVisual: getTrailingVisual(x)
            }
            return item;
        })
    }, [roles])
    const filterdData = useMemo(() => {
        return dataSource.filter(item =>
            item.text.toLowerCase().includes(filter.toLowerCase())
        )
    }, [dataSource, filter])
    const _selectedDatas = useMemo(() => {
        return dataSource.filter(item => props.value.includes(item.id))
    }, [props.value, dataSource])

    const isSelectedAll = filterdData.length > 0 && filterdData.map(x => x.id).find(id => !props.value.includes(id)) === undefined;

    return (

        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button sx={{
                    maxWidth: 300
                }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate(`SelectBoxRole.PlaceHolder`)}
                    </p>
                </Button>
            )}
            title={<>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        <FormControl>
                            <Checkbox checked={isSelectedAll} onChange={(e) => {
                                if (e.target.checked) {
                                    props.onValueChanged(filterdData.map(x => x.id))
                                } else {
                                    props.onValueChanged([])
                                }
                            }} />
                            <FormControl.Label>Select all</FormControl.Label>
                        </FormControl>
                    </Box>
                    {props.isShowClearBtn && props.value.length > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => {
                                props.onValueChanged([])
                            }}
                        >
                            Bỏ chọn
                        </Button>
                    }
                </Box>
            </>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedDatas}
            onSelectedChange={(data: any) => {
                props.onValueChanged(data.map((x: any) => x.id))
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'medium', height: 'medium' }}
        />

    );
};

export default SelectBoxRole;
