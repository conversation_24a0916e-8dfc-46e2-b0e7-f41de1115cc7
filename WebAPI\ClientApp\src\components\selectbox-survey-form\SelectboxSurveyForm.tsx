import { TriangleDownIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from "@primer/react";
import { BetterSystemStyleObject } from "@primer/react/lib/sx";
import { useEffect, useMemo, useState } from "react";
import { useDebounce } from "use-debounce";
import { crmSurveyFormApi } from "../../api/crmSurveyFormApi";
import { useCommonContext } from "../../contexts/common";
import { ICrmSurveyForm } from "../../models/response/crm-survey/ICrmSurveyForm";
interface ISelectboxSurveyForm {
    onValueChanged: (id: number, form?: ICrmSurveyForm) => void,
    value: number,
    maxWidth?: any,
    sx?: BetterSystemStyleObject
}
const getSurveyIcon = (form: ICrmSurveyForm) => {
    return (
        <Box sx={{
            backgroundImage: `url("${form.thumbail_img}")`,
            height: "30px",
            width: "30px",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "top",
            backgroundSize: "cover"
        }}>
            &nbsp;
        </Box>
    );
}
const SelectboxSurveyForm = (props: ISelectboxSurveyForm) => {
    const [open, setOpen] = useState(false)
    const [isLoading, setIsLoading] = useState(false);
    const [filter, setFilter] = useState('')
    const [items, setItems] = useState<any[]>([]);
    const [searchKeyDelayed] = useDebounce(filter, 1000);
    const { translate } = useCommonContext();
    useEffect(() => {
        handleGetForms();
    }, [])
    const filteredItems = useMemo(() => {
        return items.filter((item: any) => item.text.toLowerCase().includes(filter.toLowerCase()))
    }, [items, searchKeyDelayed])
    const handleGetForms = async () => {
        setIsLoading(true);
        const res = await crmSurveyFormApi.selectAll();
        setIsLoading(false)
        if (res.is_success) {
            setItems(res.data.map((x: ICrmSurveyForm) => {
                return {
                    id: x.id,
                    text: x.name,
                    // trailingVisual: getSurveyIcon(x)
                }
            }))
        }
    }
    const selected = items.find((x: any) => x.id === props.value)
    return (
        <>
            <SelectPanel
                renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                    <Box sx={{
                        display: "flex"
                    }}>
                        <Button sx={{ flex: 1 }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                            {children || translate("SelectboxSurveyForm.PlaceHolder")}
                        </Button>

                    </Box>
                )}
                title={""}
                placeholderText="Search"
                open={open}
                onOpenChange={setOpen}
                items={filteredItems}
                selected={selected}
                onSelectedChange={(selected: any) => {
                    props.onValueChanged(selected.id, items.find(x => x.id === selected.id))
                }}
                onFilterChange={setFilter}
                showItemDividers={true}
                loading={isLoading}
                overlayProps={{ width: 'large', height: 'medium' }}
            />
        </>
    );
};

export default SelectboxSurveyForm;