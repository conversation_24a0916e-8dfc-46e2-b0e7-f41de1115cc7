
import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { thongTinCbnvApi } from '../../api/thongTinCbnvApi';
import { useCommonContext } from '../../contexts/common';
import { IThongTinCbnvVm } from '../../models/response/hoc-sinh/IThongTinCbnv';
type ComboboxThongTinCbnvProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: IThongTinCbnvVm) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean,
    maxWidth?: any
};
const SelectBoxThongTinCbnv = (props: ComboboxThongTinCbnvProps) => {
    const { translate } = useCommonContext();
    const [thongTinCbnvs, setThongTinCbnvs] = useState<IThongTinCbnvVm[]>([]);
    const handleReloadThongTinCbnv = async () => {
        const res = await thongTinCbnvApi.select_all();
        if (res.is_success) {
            setThongTinCbnvs(res.data);
        }
    };

    useEffect(() => {
        handleReloadThongTinCbnv();
    }, []);

    const [filter, setFilter] = useState('')
    const [open, setOpen] = useState(false)
    const dataSource = useMemo(() => {
        return thongTinCbnvs.map(x => {
            const item: any = {
                id: x.id,
                text: x.ten_nhan_vien + ' (' + x.ma_nhan_vien + ')',
            }
            return item;
        })
    }, [thongTinCbnvs, filter])

    const removeVietnameseTones = (str: string) => {
        return str
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/đ/g, "d")
            .replace(/Đ/g, "D");
    };
    const filterdData = useMemo(() => {
        const normalizedFilter = removeVietnameseTones(filter.toLowerCase());
        return dataSource.filter(item =>
            removeVietnameseTones(item.text.toLowerCase()).includes(normalizedFilter)
        );
    }, [dataSource, filter]);

    const _selectedData = useMemo(() => {
        return dataSource.find(item => item.id === props.value)
    }, [props.value, dataSource])
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between'
                }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate(`Chọn cbnv`)}
                    </p>
                </Button>
            )}
            title={<>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        Chọn CBNV
                    </Box>
                    {props.isShowClearBtn && (props.value ?? 0) > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => {
                                props.onValueChanged(0)
                            }}
                        >
                            Bỏ chọn
                        </Button>
                    }
                </Box>
            </>}
            placeholderText="Search"
            open={props.isReadonly == false ? open : false}

            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedData}
            onSelectedChange={(data: any) => {
                props.onValueChanged(data.id, thongTinCbnvs.find(x => x.id === data.id))
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />

    );
};

export default SelectBoxThongTinCbnv;
