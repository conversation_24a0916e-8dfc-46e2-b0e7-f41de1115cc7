import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useMemo, useState } from 'react';

type SelectBoxMultipleTrangThaiEmailProps = {
    isReadonly?: boolean;
    value?: number[];
    onValueChanged: (ids: number[]) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean;
    maxWidth?: any;
};
const trangThaiEmails = [
    { id: 1, text: "Tạo mới" },
    { id: 2, text: "Từ chối" },
    { id: 3, text: "Đã phê duyệt" },
    { id: 4, text: "Đang gửi" },
    { id: 5, text: "Hoàn thành" },
];
const SelectBoxMultipleTrangThaiEmail = (props: SelectBoxMultipleTrangThaiEmailProps) => {
    const [filter, setFilter] = useState('');
    const [open, setOpen] = useState(false);
    const removeVietnameseTones = (str: string) => {
        return str
            .normalize('NFD')
            .replace(/[̀-ͯ]/g, '')
            .replace(/đ/g, 'd')
            .replace(/Đ/g, 'D');
    };

    const filteredData = useMemo(() => {
        const normalizedFilter = removeVietnameseTones(filter.toLowerCase());
        return trangThaiEmails.filter(item => removeVietnameseTones(item.text.toLowerCase()).includes(normalizedFilter));
    }, [trangThaiEmails, filter]);

    const _selectedData = useMemo(() => {
        return trangThaiEmails.filter(item => props.value?.includes(item.id));
    }, [props.value, trangThaiEmails]);

    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button
                    sx={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}
                    trailingAction={TriangleDownIcon}
                    aria-labelledby={ariaLabelledBy}
                    {...anchorProps}
                >
                    <p style={{ maxWidth: props.maxWidth, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {children || 'Chọn trạng thái'}
                    </p>
                </Button>
            )}

            title={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ flex: 1 }}>Chọn trạng thái</Box>
                    {props.isShowClearBtn && (props.value?.length ?? 0) > 0 && (
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{ color: 'danger.emphasis' }}
                            onClick={() => props.onValueChanged([])}
                        >
                            Bỏ chọn
                        </Button>
                    )}
                </Box>
            }
            placeholderText='Search'
            open={open}
            onOpenChange={setOpen}
            items={filteredData}
            selected={_selectedData}
            onSelectedChange={(selectedItems: any[]) => {
                const newValues = selectedItems.map(item => item.id);
                props.onValueChanged(newValues);
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />
    );
};

export default SelectBoxMultipleTrangThaiEmail;
