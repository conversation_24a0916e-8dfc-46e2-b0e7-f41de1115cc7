import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { trangThaiPhuHuynhApi } from '../../api/trangThaiPhuHuynhApi';

type SelectBoxMultipleTrangThaiEmailProps = {
    isReadonly?: boolean;
    value?: number[];
    onValueChanged: (ids: number[]) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean;
    maxWidth?: any;
    phuHuynhs?: any[];
    isCounting?: boolean | false;
};
const SelectBoxMultipleTrangThaiEmail = (props: SelectBoxMultipleTrangThaiEmailProps) => {
    const [filter, setFilter] = useState('');
    const [open, setOpen] = useState(false);
    const [TrangThaiPhuHuynh, setTrangThaiPhuHuynh] = useState<any[]>([]);
    useEffect(() => {
        handleGetTrangThaiPhuHuynhAsync();
    }, []);

    // Tính toán số lượng cho mỗi trạng thái
    const calculateCounts = useMemo(() => {
        const counts: { [key: number]: number } = {};
        if (props.phuHuynhs && props.phuHuynhs.length > 0) {
            props.phuHuynhs.forEach((ph) => {
                const statusId = ph.ts_phuhuynh_adm_status_id;
                counts[statusId] = (counts[statusId] || 0) + 1;
            });
        }
        return counts;
    }, [props.phuHuynhs]);

    const dataSource = useMemo(() => {
        if (props.isCounting) {
            const items = TrangThaiPhuHuynh.map((x) => {
                const count = calculateCounts[x.id] || 0;
                return {
                    id: x.id,
                    text: `${x.name} - [${count}]`,
                    count: count,
                };
            });

            const totalCount = props.phuHuynhs?.length || 0;
            const allOption = {
                id: 0,
                text: `Tất cả (${totalCount})`,
                count: totalCount,
            };

            return [allOption, ...items];
        } else {
            const items = TrangThaiPhuHuynh.map((x) => {
                const count = calculateCounts[x.id] || 0;
                return {
                    id: x.id,
                    text: `${x.name}`,
                    count: count,
                };
            });

            const totalCount = props.phuHuynhs?.length || 0;
            const allOption = {
                id: 0,
                text: `Tất cả`,
                count: totalCount,
            };

            return [allOption, ...items];
        }
    }, [TrangThaiPhuHuynh, calculateCounts, props.phuHuynhs]);

    const removeVietnameseTones = (str: string) => {
        return str
            .normalize('NFD')
            .replace(/[̀-ͯ]/g, '')
            .replace(/đ/g, 'd')
            .replace(/Đ/g, 'D');
    };

    const filteredData = useMemo(() => {
        const normalizedFilter = removeVietnameseTones(filter.toLowerCase());
        return dataSource.filter(item => removeVietnameseTones(item.text.toLowerCase()).includes(normalizedFilter));
    }, [dataSource, filter]);

    const _selectedData = useMemo(() => {
        return dataSource.filter(item => props.value?.includes(item.id));
    }, [props.value, dataSource]);

    const handleGetTrangThaiPhuHuynhAsync = async () => {
        const res = await trangThaiPhuHuynhApi.selectAll();
        if (res.is_success) {
            setTrangThaiPhuHuynh(res.data);
        }
    };
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button
                    sx={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}
                    trailingAction={TriangleDownIcon}
                    aria-labelledby={ariaLabelledBy}
                    {...anchorProps}
                >
                    <p style={{ maxWidth: props.maxWidth, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {children || 'Chọn trạng thái'}
                    </p>
                </Button>
            )}

            title={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ flex: 1 }}>Chọn trạng thái PH</Box>
                    {props.isShowClearBtn && (props.value?.length ?? 0) > 0 && (
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{ color: 'danger.emphasis' }}
                            onClick={() => props.onValueChanged([])}
                        >
                            Bỏ chọn
                        </Button>
                    )}
                </Box>
            }
            placeholderText='Search'
            open={open}
            onOpenChange={setOpen}
            items={filteredData}
            selected={_selectedData}
            onSelectedChange={(selectedItems: any[]) => {
                const newValues = selectedItems.map(item => item.id);
                props.onValueChanged(newValues);
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />
    );
};

export default SelectBoxMultipleTrangThaiEmail;
