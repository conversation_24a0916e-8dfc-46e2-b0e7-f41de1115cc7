import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, Checkbox, FormControl, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';


interface ISelectBoxTruongMultipleProps {
	selectedValue: number[];
	onSelectionChanged: (dm_truong_ids: number[]) => void;
	maxWidth?: number;
	isShowClearBtn?: boolean
}

const SelectBoxTruongMultiple = (props: ISelectBoxTruongMultipleProps) => {
	const { translate } = useCommonContext();
	const { language } = useSelector((x: RootState) => x.common);
	const dm_truongs = useSelector((state: RootState) => state.auth.user_info?.campus);
	const dispatch = useDispatch();
	const { selectedValue, onSelectionChanged } = props;
	const [filter, setFilter] = useState('')
	const [open, setOpen] = useState(false)


	useEffect(() => {
		dispatch(actions.categorySource.loadDmTruongStart());

	}, []);
	const dataSource = useMemo(() => {
		return (dm_truongs ?? []).map(x => {
			const item: any = {

				id: x.id,
				text: language === "en" ? x.ten_truong_en : x.ten_truong,
			}
			return item;
		})
	}, [dm_truongs, filter, language])
	const filterdData = useMemo(() => {
		return dataSource.filter(item =>
			item.text.toLowerCase().includes(filter.toLowerCase())
		)
	}, [dataSource, filter])
	const _selectedDatas = useMemo(() => {
		return dataSource.filter(item => selectedValue.includes(item.id))
	}, [selectedValue, dataSource])
	const isSelectedAll = filterdData.length > 0 && filterdData.map(x => x.id).find(id => !selectedValue.includes(id)) === undefined;

	return (
		<>
			<SelectPanel
				renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
					<Button sx={{
						maxWidth: props.maxWidth ?? 300
					}} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
						<p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
							{children || translate(`Chọn trường`)}
						</p>
					</Button>
				)}
				title={<>
					<Box sx={{ display: "flex", alignItems: "center" }}>
						<Box sx={{ flex: 1 }}>
							<FormControl>
								<Checkbox checked={isSelectedAll} onChange={(e) => {
									if (e.target.checked) {
										props.onSelectionChanged(filterdData.map(x => x.id))
									} else {
										props.onSelectionChanged([])
									}
								}} />
								<FormControl.Label>Select all</FormControl.Label>
							</FormControl>
						</Box>
						{props.isShowClearBtn && selectedValue.length > 0 &&
							<Button
								trailingVisual={XCircleFillIcon}
								variant='invisible'
								sx={{
									color: "danger.emphasis"
								}}
								onClick={() => {
									props.onSelectionChanged([])
								}}
							>
								Bỏ chọn
							</Button>
						}
					</Box>
				</>}
				placeholderText="Search"
				open={open}

				onOpenChange={setOpen}
				items={filterdData}
				selected={_selectedDatas}
				onSelectedChange={(data: any) => {
					props.onSelectionChanged(data.map((x: any) => x.id))
				}}
				onFilterChange={setFilter}
				showItemDividers={true}
				overlayProps={{ width: 'large', height: 'medium' }}
			/>
		</>
	);
};

export default SelectBoxTruongMultiple;
