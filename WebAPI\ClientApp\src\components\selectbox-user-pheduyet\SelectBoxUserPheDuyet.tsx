
import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { userApi } from '../../api/userApi';
import { useCommonContext } from '../../contexts/common';
import { useCommonSelectedHook } from '../../hooks/useCommonSelectedHook';
import { IUserModel } from '../../models/response/account/user-info';
type SelectBoxUserPheDuyetProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean,
    maxWidth?: any
};
const SelectBoxUserPheDuyet = (props: SelectBoxUserPheDuyetProps) => {
    const { dm_coso_id } = useCommonSelectedHook();
    const { translate } = useCommonContext();
    const [UserPheDuyets, setUserPheDuyets] = useState<IUserModel[]>([]);
    const handleReloadUserPheDuyet = async () => {
        const res = await userApi.SelectUserPheDuyet(dm_coso_id);
        if (res.is_success) {
            setUserPheDuyets(res.data);
        }
    };
    useEffect(() => {
        handleReloadUserPheDuyet();
    }, []);

    const [filter, setFilter] = useState('')
    const [open, setOpen] = useState(false)
    const dataSource = useMemo(() => {
        return UserPheDuyets.map(x => {
            const item: any = {
                id: x.id,
                text: x.full_name + ' (' + x.phone_number + ' - ' + x.email + ')',
            }
            return item;
        })
    }, [UserPheDuyets, filter])

    const removeVietnameseTones = (str: string) => {
        return str
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/đ/g, "d")
            .replace(/Đ/g, "D");
    };
    const filterdData = useMemo(() => {
        const normalizedFilter = removeVietnameseTones(filter.toLowerCase());
        return dataSource.filter(item =>
            removeVietnameseTones(item.text.toLowerCase()).includes(normalizedFilter)
        );
    }, [dataSource, filter]);

    const _selectedData = useMemo(() => {
        return dataSource.find(item => item.id === props.value)
    }, [props.value, dataSource])
    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between'
                }} trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                    <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis" }}>
                        {children || translate(`Chọn người phê duyệt`)}
                    </p>
                </Button>
            )}
            title={<>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        Chọn người phê duyệt
                    </Box>
                    {props.isShowClearBtn && (props.value ?? 0) > 0 &&
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{
                                color: "danger.emphasis"
                            }}
                            onClick={() => {
                                props.onValueChanged(0)
                            }}
                        >
                            Bỏ chọn
                        </Button>
                    }
                </Box>
            </>}
            placeholderText="Search"
            open={open}
            onOpenChange={setOpen}
            items={filterdData}
            selected={_selectedData}
            onSelectedChange={(data: any) => {
                props.onValueChanged(data.id)
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />

    );
};

export default SelectBoxUserPheDuyet;
