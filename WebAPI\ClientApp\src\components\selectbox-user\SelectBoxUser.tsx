import { TrashIcon, TriangleDownIcon } from '@primer/octicons-react';
import { ActionList, Box, Button, IconButton, SelectPanel } from '@primer/react';
import { useEffect, useState } from 'react';
import { useDebounce } from 'use-debounce';
// import { userApi } from '../../api/user/userApi';
import { useCommonContext } from '../../contexts/common';
// import { useAppDispatch } from '../../hooks/useAppDispatch';
// import { eSortMode } from '../../models/commons/eSortMode';
// import { IApiModel } from '../../models/requests/user/IUserLoadRequest';
// import { IUser } from '../../models/responses/user/IUser';
import { BetterSystemStyleObject } from '@primer/react/lib/sx';
import { useDispatch } from 'react-redux';
import { userApi } from '../../api/userApi';
import { NotifyHelper } from '../../helpers/toast';
import { IApiModel, IUserInfoModel } from '../../models/response/account/user-info';
import UserAvatar from '../user-avatar';
import styles from "./SelectBoxUser.module.css";
interface ISelectUserProps {
    onValueChanged: (ids: number[]) => void,
    values: number[],
    maxWidth?: any,
    has_api?: IApiModel,
    disabled_id?: number,
    sx?: BetterSystemStyleObject
}
const getUserIcon = (user: IUserInfoModel) => {
    return (
        <Box sx={{ display: "flex", alignItems: "center" }}>

            <Box>
                {user.username}
            </Box>
            <Box>
                <UserAvatar fullName={user.full_name}
                    url={user.img}
                />
            </Box>
        </Box>
    );
}
const SelectBoxUser = (props: ISelectUserProps) => {
    const [open, setOpen] = useState(false)
    const [isLoading, setIsLoading] = useState(false);
    const [filter, setFilter] = useState('')
    const [items, setItems] = useState([]);
    const [selectedUsers, setSelectedUsers] = useState<IUserInfoModel[]>([]);

    const [searchKeyDelayed] = useDebounce(filter, 1000);

    const dispatch = useDispatch();
    const { translate } = useCommonContext();
    useEffect(() => {
        handleSearchUserAsync();
    }, [searchKeyDelayed])
    useEffect(() => {
        handleSearchUserSelectedAsync();
    }, [props.values])
    const handleSearchUserSelectedAsync = async () => {
        if (props.values.length > 0) {
            setIsLoading(true);
            const res = await userApi.getUserByIds(props.values)
            setIsLoading(false);
            if (res.is_success) {
                setSelectedUsers(res.data)
            }
        } else {
            setSelectedUsers([])

        }
    }
    const handleSearchUserAsync = async () => {
        setIsLoading(true);
        const res = await userApi.getUsers({
            page_index: 0,
            page_size: 10,
            search_key: searchKeyDelayed,
            // sort_mode: eSortMode.ASC,
            has_api: props.has_api
        })
        setIsLoading(false)
        if (res.is_success) {
            setItems(res.data.data.map((x: IUserInfoModel) => {
                return {
                    id: x.id,
                    text: x.full_name,
                    trailingVisual: getUserIcon(x)
                }
            }))
        }
    }
    const _selectedDatas = items.filter((x: any) => props.values.includes(x.id))
    return (
        <>
            <SelectPanel
                renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                    <Box sx={{
                        // maxWidth: 300,
                        // height: "auto",
                        // p: 0,
                        // pr: 2,
                        // backgroundColor: "#fff",
                        width: props.maxWidth
                    }}
                        trailingAction={TriangleDownIcon} aria-labelledby={` ${ariaLabelledBy}`} {...anchorProps}>
                        {selectedUsers.length === 0 &&
                            <Button sx={props.sx}>
                                {translate('SelectBoxUser.PlaceHolder')}
                            </Button>
                        }
                        {selectedUsers.length >= 0 &&
                            <Box sx={{
                                display: "unset",
                                backgroundColor: "#fff"
                            }}>
                                <ActionList showDividers>
                                    {selectedUsers.map((user: IUserInfoModel) => {
                                        return (
                                            <ActionList.Item sx={{
                                                alignItems: "flex-start"
                                            }}
                                                className={styles.item}
                                            >
                                                <ActionList.LeadingVisual sx={{ width: "24px", maxWidth: "24px", height: "24px" }}>
                                                    <UserAvatar fullName={user.full_name} url={user.img} />
                                                </ActionList.LeadingVisual>
                                                <Box sx={{ flex: 1, width: "100%" }}>
                                                    {user.full_name}
                                                </Box>
                                                <ActionList.Description variant="block">{user.email}</ActionList.Description>
                                                <ActionList.TrailingVisual>
                                                    <IconButton
                                                        aria-label={`Edit: ${user.id}`}
                                                        title={`Edit: ${user.id}`}
                                                        icon={TrashIcon}
                                                        variant="invisible"
                                                        onClick={() => {

                                                            props.onValueChanged([...props.values.filter(x => x !== user.id)])

                                                        }}
                                                    />
                                                </ActionList.TrailingVisual>
                                            </ActionList.Item>
                                        );
                                    })}
                                </ActionList >
                            </Box>
                        }
                    </Box >
                )}
                title={`Đã chọn: ${props.values.length}`}
                placeholderText="Search"
                open={open}
                onOpenChange={setOpen}
                loading={isLoading}
                items={items}
                selected={_selectedDatas}
                onSelectedChange={(data: any) => {

                    const allIds = items.map((x: any) => x.id);
                    const ids: number[] = data.map((x: any) => x.id);
                    if (props.disabled_id) {
                        const disabledData = data.find((y: any) => y.id === props.disabled_id);
                        if (disabledData) {
                            NotifyHelper.Error(`Bạn không thể chọn ${disabledData.text}`)
                            return;
                        }
                    }
                    props.onValueChanged([...props.values.filter(x => !allIds.includes(x)), ...ids])

                    // props.onValueChanged([...data.map((x: any) => x.id)])
                }}
                onFilterChange={setFilter}
                showItemDividers={true}
                overlayProps={{ width: 'large', height: 'medium' }}
            />
        </>
    );
};

export default SelectBoxUser;