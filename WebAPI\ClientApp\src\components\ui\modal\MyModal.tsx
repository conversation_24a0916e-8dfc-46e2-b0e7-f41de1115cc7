import { Dialog, DialogProps } from '@primer/react/drafts';
import React from 'react';
import { useCommonContext } from '../../../contexts/common';
interface IMyModalProps extends DialogProps {
    children?: React.ReactNode,
    titleComponent?: React.ReactNode
    onClose: () => void,
    width?: "small" | "medium" | "large" | "xlarge";
    // height?: any,
    isOpen?: boolean,
    // isNobody?: boolean
    shouldCloseOnOverlayClick?: boolean;
}
const MyModal = (props: IMyModalProps) => {
    const { translate } = useCommonContext();
    return (
        <>
            {props.isOpen &&
                <Dialog
                    title={props.title ? translate(props.title?.toString()) : props.titleComponent}
                    subtitle={props.subtitle ? translate(props.subtitle?.toString()) : undefined}
                    // isOpen={props.isOpen}
                    width={props.width}
                    height={props.height}
                    sx={props.sx}
                    onClose={props.shouldCloseOnOverlayClick === false ?
                        (gesture) => {
                            // Chỉ đóng khi click vào nút đóng, không đóng khi click ra ngoài
                            if (gesture === 'close-button') {
                                props.onClose();
                            }
                        } :
                        props.onClose
                    }
                    renderHeader={props.renderHeader}

                // aria-labelledby="hello"
                >
                    {/* {props.titleComponent &&
                        <Dialog.Header>
                            {props.titleComponent}
                        </Dialog.Header>
                    } */}

                    {props.children}



                </Dialog>
            }
        </>
    );
};

export default MyModal;