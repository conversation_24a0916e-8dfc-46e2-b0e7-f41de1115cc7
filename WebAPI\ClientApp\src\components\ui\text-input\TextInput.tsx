import { FormControl, TextInput, TextInputProps } from '@primer/react';
import { useCommonContext } from '../../../contexts/common';
import { useEffect, useState } from 'react';
import { useDebounce } from 'use-debounce';

export interface IMyTextInputProps extends TextInputProps {
    register?: any,
    errors?: any,
    validateMessage?: string,
    name?: string,
    required?: boolean,
    minLength?: number,
    maxLength?: number,
    onValueDelayChanged?: (value: any) => void
}
const MyTextInput = (props: IMyTextInputProps) => {
    const { register, errors } = props;
    const { translate } = useCommonContext();
    const [tempValue, setTempValue] = useState<any>(props.value ?? props.defaultValue);
    const tempValueDelayed = useDebounce(tempValue, 300)[0];
    useEffect(() => {

        if (props.onValueDelayChanged && tempValueDelayed !== (props.value ?? props.defaultValue)) {
            props.onValueDelayChanged(tempValueDelayed)
        }
    }, [tempValueDelayed])

    return (
        <>
            {register && props.name &&
                <>
                    <TextInput
                        {...register(props.name, {
                            required: {
                                value: props.required,
                                message: translate(props.validateMessage ?? "")
                            },
                            minLength: {
                                value: props.minLength,
                                message: translate(props.validateMessage ?? "")
                            },
                            maxLength: {
                                value: props.maxLength,
                                message: translate(props.validateMessage ?? "")
                            },

                        })}
                        name={props.name}
                        {...props}
                        onChange={(e) => {
                            setTempValue(e.target.value)
                        }}
                    />
                    {
                        errors && errors[props.name] &&
                        <FormControl.Validation id={props.name} variant="error">
                            <>{errors[props.name].message ?? ""}</>
                        </FormControl.Validation>
                    }
                </>
            }
            {(!register || !props.name) &&
                <TextInput

                    {...props}
                    onChange={(e) => {
                        setTempValue(e.target.value);
                        if (props.onChange) {
                            props.onChange(e);
                        }
                    }}
                />
            }
        </>
    );
};

export default MyTextInput;