import { useEffect, useState } from 'react';
import { Navigate, Route, Routes, useLocation, useNavigate } from 'react-router-dom';
import router from "../app-routes"
import { HomePage } from '../pages';
import LogoutPage from '../pages/logout-page/LogoutPage';


interface Props {
    component: React.ComponentType;
    path: string;
}

const RedirectWrapper = ({ component: Component, path }: Props) => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        // Nếu URL không có tham số và không có dấu / ở cuối
        if (location.pathname === path) {
            navigate(`${path}/all`, { replace: true });
        }
    }, [location.pathname, path, navigate]);

    return <Component />;
};

const PrivateContent = () => {
    const [visible, setVisible] = useState(true);
    const location = useLocation();

    useEffect(() => {
        setVisible(!visible);
    }, [location.pathname]);

    return (
        <Routes>
            {router.map((x, idx) => {
                // Danh sách các routes cần redirect khi không có tham số
                const needsRedirect = [
                    '/home',
                    '/ds_hoc_sinh_lop',
                    '/app-menu-config',
                    '/app-account',
                    '/dan-thuoc',
                    '/thoi-khoa-bieu',
                    '/nguon_biet_toi',
                    '/dm_trangthaihocsinh',
                    '/loai_giay_to',
                    '/hoc-sinh',
                    '/food',
                    '/phu-huynh',
                    '/acceptance-form',
                    '/truong',
                    '/he',
                    '/khoi',
                    '/khoi_he',
                    '/lop',
                    '/suc-khoe',
                    '/thang-diem',
                    '/mon-thi',
                    '/ky-thi',
                    '/thongtin-cbnv',
                    'kythi-monthi',
                    'thi-sinh',
                    'danh-sach-thi',
                    'phong-thi',
                    '/nhap-diem-thi',
                    '/ket-qua-thi',
                    '/tong-hop',
                    '/face-id',
                    '/noi-tru',
                    '/cskh',
                    '/tong-hop-giay-to',
                    '/monthidauvao',
                    '/thidauvao'

                ].includes(x.path.split('/:')[0]); // Lấy phần path trước /:tab?

                return (
                    <Route
                        key={idx}
                        path={`${x.path}`}
                        element={
                            needsRedirect ?
                                <RedirectWrapper component={x.component} path={x.path.split('/:')[0]} /> :
                                <x.component />
                        }
                    />
                );
            })}
            <Route path="/logout" element={<LogoutPage />} />
            <Route path="/home" element={<HomePage />} />
            <Route path="*" element={<Navigate to={"/home"} replace />} />
        </Routes>
    );
};

export default PrivateContent;

// const PrivateContent = () => {
//     const [visible, setVisible] = useState(true);
//     const location = useLocation()
//     const navigate = useNavigate();

//     useEffect(() => {
//         setVisible(!visible)
//            // Thêm logic redirect cho các routes cần trailing slash
//            const pathsNeedTrailingSlash = ['/khoi', '/he', '/lop', '/mon-thi']; // thêm các path khác
//            if (pathsNeedTrailingSlash.includes(location.pathname)) {
//                navigate(`${location.pathname}/all`, { replace: true });
//            }
//     }, [location.pathname])

//     return (
//         <>
//             <Routes>
//                 {router.map((x, idx) => {
//                     return (
//                         <Route key={idx} path={`${x.path}`}
//                             // element={x.component}
//                             Component={x.component}
//                         />

//                     );
//                 })}
//                 <Route path="/logout" element={<LogoutPage />} />
//                 <Route path="/home" element={<HomePage />} />
//                 <Route path="*" element={<Navigate to={"/home"} replace />} />
//             </Routes>
//         </>
//     );
// };

// export default PrivateContent;// import { useEffect, useState } from 'react';
// import { Navigate, Route, Routes, useLocation } from 'react-router-dom';
// import router from "../app-routes";
// import { HomePage } from '../pages';
// import LogoutPage from '../pages/logout-page/LogoutPage';

// const PrivateContent = () => {
//     const [visible, setVisible] = useState(true);
//     const location = useLocation()
//     useEffect(() => {
//         setVisible(!visible)
//     }, [location.pathname])

//     return (
//         <>
//             <Routes>
//                 {router.map((x, idx) => {
//                     return (
//                         <Route key={idx} path={`${x.path}`}
//                             // element={x.component}
//                             Component={x.component}
//                         />

//                     );
//                 })}
//                 <Route path="/logout" element={<LogoutPage />} />
//                 <Route path="/home" element={<HomePage />} />
//                 <Route path="*" element={<Navigate to={"/home"} replace />} />
//             </Routes>
//         </>
//     );
// };

// export default PrivateContent;