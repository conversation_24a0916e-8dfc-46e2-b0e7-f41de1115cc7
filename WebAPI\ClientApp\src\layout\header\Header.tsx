import { GraphIcon } from '@primer/octicons-react';
import { ActionList, ActionMenu, Box, PageLayout, useTheme } from '@primer/react';
import { useState } from 'react';
import { useDispatch, useSelector } from "react-redux";
import { Link } from 'react-router-dom';
import { appInfo } from '../../AppInfo';
import { ComboboxCoSo } from '../../components/combobox-coso';
import { ComboboxNamHoc } from "../../components/combobox-namhoc";
import SearchBoxPhuHuynh from '../../components/searchbox-phuhuynh';
import Button from '../../components/ui/button';
import UserPanel from '../../components/user-panel';
import { useAuth } from '../../hooks/useAuth';
import { IPhuHuynhItemRespone } from '../../models/response/phu-huynh/IPhuHuynhItemRespone';
import PhuHuynhProfile from '../../pages/phu-huynh-profile';
import { actions } from "../../state/actions/actionsWrapper";
import { RootState } from "../../state/reducers";
import MenuGroup from '../menu-group';
import styles from "./Header.module.css";

const AppsIcon = () => {
    return <img alt='apps' style={{ height: "16px" }} src="../../images/apps.png" />
}
const VietNamIcon = () => {
    return <img alt='vn' style={{ height: "16px" }} src="../../images/vietnam.png" />
}
const EnglandIcon = () => {
    return <img alt='en' style={{ height: "16px" }} src="../../images/united-kingdom.png" />
}
const KoreanIcon = () => {
    return <img alt='kr' style={{ height: "16px" }} src="../../images/south-korea.png" />
}
interface IHeaderProps {
    // onColorModeChanged: (colorMode: "day" | "night") => void
}
const Header = (props: IHeaderProps) => {
    const { colorMode } = useTheme()
    const dispatch = useDispatch();
    const { language } = useSelector((x: RootState) => x.common)
    const { dm_truong_selected_id, nam_hoc, hoc_ky, dm_he_selected_id, dm_coso_selected_id } = useSelector((x: RootState) => x.common)
    const { user_info } = useAuth();
    const { appConfig } = useSelector((x: RootState) => x.appConfig)

    const logoUrl = user_info?.vender_logo ?? 'https://portal.1school.edu.vn/images/logo-white.sv'
    const LogoIcon = () => {
        return <img alt='logo' style={{ height: "30px" }} src={`${logoUrl}`} />
    }
    // Thêm state để quản lý modal phụ huynh
    const [isShowPHModal, setIsShowPHModal] = useState(false);
    const [selectedId, setSelectedId] = useState<number>(0);

    const handlePhuHuynhChanged = (id: number, data?: IPhuHuynhItemRespone) => {
        console.log("Selected phụ huynh:", id, data);
    };

    const handlePhuHuynhClick = (id: number) => {
        setSelectedId(id);
        setIsShowPHModal(true);
    };

    return (

        <Box sx={{
            borderBottomWidth: "1px",
            borderBottomStyle: "solid",
            borderBottomColor: "border.default",
            borderTopWidth: "1px",
            borderTopStyle: "solid",
            borderTopColor: "border.default",
            backgroundColor: "rgba(24, 45, 98, 0.2)",
            // backgroundColor: "accent.fg",
            // paddingLeft: "2px",
            // paddingRight: "8px"
        }}>
            <PageLayout sx={{ padding: 0 }} containerWidth="full">

                <Box style={{
                    height: appInfo.headerHeight,
                    display: "flex",
                    width: "100%",
                    alignItems: "center",
                    backgroundColor: "#182D62"

                }}
                    className={styles.header}
                >
                    <Box className={styles.left} sx={{
                        width: 200,
                        pl: 4,
                        display: "flex",
                        alignItems: "center"
                    }}>
                        <Link to={`${(appConfig?.OIDC_AUTHORITY ?? "").replace("auth", "portal")}`}>
                            <LogoIcon />
                        </Link>
                    </Box>
                    <div className={styles.center} style={{
                        flex: 1,
                        height: 56,
                        display: "flex",
                        alignItems: "center"
                    }}>
                        <div className={styles.item}>
                            <Link to={"../dashboard"}>
                                <Button text='Dashboard' leadingVisual={GraphIcon} />
                            </Link>
                        </div>
                        <div className={styles.item} style={{ width: '250px', marginLeft: '10px' }}>
                            <SearchBoxPhuHuynh
                                isReadonly={false}
                                onValueChanged={handlePhuHuynhChanged}
                                isShowClearBtn={true}
                                maxWidth="200px"
                                onRowClick={handlePhuHuynhClick}
                            />
                        </div>
                    </div>
                    <div className={styles.right} style={{
                        // width: 200
                    }}>
                        {/* <div className={styles.item}>
                            <ComboboxTruong
                                value={dm_truong_selected_id}
                                onValueChanged={(value) => {
                                    dispatch(actions.common.changedValueHeaderComboboxTruong(value))
                                }}
                            />
                        </div> */}
                        <div className={styles.item}>
                            <ComboboxCoSo
                                value={dm_coso_selected_id}
                                onValueChanged={(value) => {
                                    dispatch(actions.common.changedValueHeaderComboboxCoSo(value))
                                }}
                            />
                        </div>
                        {/* <div className={styles.item}>
                            <ComboboxHe
                                isShowClearButton
                                value={dm_he_selected_id}
                                onValueChanged={(value) => {
                                    dispatch(actions.common.changedValueHeaderComboboxHe(value))
                                }}
                            />
                        </div> */}
                        <div className={styles.item}>
                            <ComboboxNamHoc
                                value={nam_hoc}
                                width="170px"
                                onValueChanged={(value) => {
                                    dispatch(actions.common.changedValueHeaderComboboxNamHoc(value))
                                }}
                            />
                        </div>
                        {/* <div className={styles.item}>
                            <ComboboxHocKyFixed
                                width="100px"
                                value={hoc_ky}
                                onValueChanged={(value) => {
                                    dispatch(actions.common.changedValueHeaderComboboxHocKy(value))
                                }}
                            />
                        </div> */}

                        {/* <div className={styles.item}>
                            <IconButton icon={QuestionIcon} aria-label="Bell" variant='invisible'
                                size="large"
                            />
                        </div> */}
                        {/* <div className={styles.item}>
                            <Button leadingVisual={AppsIcon}>

                            </Button>

                        </div> */}
                        <div className={styles.item}>
                            <ActionMenu>
                                <ActionMenu.Button aria-label="Select field type" leadingVisual={language === "en" ? EnglandIcon : VietNamIcon}>
                                    {language === "en" ? "English" : "Tiếng Việt"}
                                </ActionMenu.Button>
                                <ActionMenu.Overlay width="auto">
                                    <ActionList selectionVariant="single">
                                        <ActionList.Item selected={language === "vi"}
                                            onSelect={() => {
                                                dispatch(actions.common.changeLanguge("vi"))
                                            }}>
                                            <ActionList.LeadingVisual>
                                                <VietNamIcon />
                                            </ActionList.LeadingVisual>
                                            Tiếng Việt</ActionList.Item>
                                        <ActionList.Item selected={language === "en"}
                                            onSelect={() => {
                                                dispatch(actions.common.changeLanguge("en"))
                                            }}>
                                            <ActionList.LeadingVisual>
                                                <EnglandIcon />
                                            </ActionList.LeadingVisual>English</ActionList.Item>
                                        {/* <ActionList.Item disabled>
                                            <ActionList.LeadingVisual>
                                                <KoreanIcon />
                                            </ActionList.LeadingVisual>한국어</ActionList.Item> */}
                                    </ActionList>
                                </ActionMenu.Overlay>
                            </ActionMenu>
                        </div>
                        <div className={styles.item}>
                            <UserPanel />
                        </div>
                    </div>
                </Box>
                <MenuGroup />

            </PageLayout >
            {isShowPHModal && (
                <PhuHuynhProfile
                    id={selectedId}
                    onClose={() => {
                        setIsShowPHModal(false);
                    }}
                    onSuccess={() => {
                        setIsShowPHModal(false);
                    }}
                />
            )}
        </Box >

    );
};

export default Header;