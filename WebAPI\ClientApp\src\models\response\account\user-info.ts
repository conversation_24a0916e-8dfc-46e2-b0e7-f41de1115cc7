export interface IUserInfoModel {
    id: number,
    user_id?: number,
    username: string,
    email?: string,
    img?: string,
    full_name: string,
    menus: IMenuModel[],
    apis: IApiModel[],
    campus: ICampusModel[],
    sub_systems: IUserSubSystemModel[],
    roles: IRoleModel[],
    sis_giaovien_id?: number
    vender_id?: number
    vender_logo?: string

}
export interface IMenuModel {
    id: number;
    name: string;
    name_en: string;
    path: string;
    icon: string;
    menu_parent_id: number;
    is_active: boolean;
    items: IMenuModel[];
    alert_number: number;
    expanded: boolean;
    menu_id: number;
    tags: string
}
export interface IApiModel {
    id: number;
    url: string;
    method: string
}
export interface ICampusModel {
    id: number;
    ma_truong: string;
    ten_truong: string;
    ten_truong_en: string;
    viet_tat: string;
    dm_coso_id: number;
    dm_coso_viettat: string;
}
export interface IRoleModel {
    id: number;
    name: string;
}
export interface IUserSubSystemModel {
    id: number;
    name: string;
    name_en: string;
    icon: string;
    url: string;
    is_accessed: boolean;
}
export const initialUserInfo: IUserInfoModel = {
    apis: [],
    campus: [],
    full_name: "",
    id: 0,
    menus: [],
    roles: [],
    sub_systems: [],
    username: ""
}

export interface IUserModel {
    id: number,
    username: string,
    email?: string,
    img?: string,
    full_name: string,
    phone_number: string,

}