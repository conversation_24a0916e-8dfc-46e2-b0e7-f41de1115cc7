export interface ex_monthidauvao {
  id: number;
  mon_thi: string;
  mon_thi_en: string;
  created_time?: Date;
  created_user_id?: number;
  last_modified_times?: Date;
  last_modified_user_id?: number;
}

export interface MonThiDauVaoItemResponse {
  id: number;
  mon_thi: string;
  mon_thi_en: string;
  created_time: Date;
  created_user_id: number;
  last_modified_times: Date;
  last_modified_user_id: number;
  created_user: string;
  last_modified_user: string;
}
