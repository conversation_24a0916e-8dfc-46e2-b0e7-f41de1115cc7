export interface ex_monthidauvao_truong {
  id: number;
  ex_monthidauvao_id: number;
  dm_truong_id: number;
  created_time?: Date;
  created_user_id?: number;
  last_modified_times?: Date;
  last_modified_user_id?: number;
}

export interface MonThiDauVaoTruongItemResponse {
  id: number;
  ex_monthidauvao_id: number;
  dm_truong_id: number;
  mon_thi: string;
  mon_thi_en: string;
  ten_truong: string;
  created_time: Date;
  created_user_id: number;
  last_modified_times: Date;
  last_modified_user_id: number;
  created_user: string;
  last_modified_user: string;
}
