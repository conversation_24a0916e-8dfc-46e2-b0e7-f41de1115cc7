import {
    AlertIcon,
    UndoIcon
} from "@primer/octicons-react";
import { Box, Checkbox, FormControl, Radio, TextInput } from "@primer/react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useLocation } from "react-router-dom";
import { crmSurveyFormApi } from "../../api/crmSurveyFormApi";
import { crmSurveyFormQuestionApi } from "../../api/crmSurveyFormQuestionApi";
import { crmSurveyResponseApi } from "../../api/crmSurveyResponseApi";
import CrmSurveyComboBox from "../../components/crm-survey";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import Text from "../../components/ui/text";
import { useCommonContext } from "../../contexts/common";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { eSurveyFormQuestionTypeId } from "../../models/eSurveyFormQuestionTypeId";
import { eSurveyQuestionTypeId } from "../../models/eSurveyQuestionTypeId";
import { ICrmSurveyAnswer } from "../../models/response/crm-survey/ICrmSurveyAnswer";
import { ICrmSurveyFormQuestionDataSource } from "../../models/response/crm-survey/ICrmSurveyFormQuestionDataSource";
import { ICrmSurveyFormActiveVm } from "../../models/response/crm-survey/ICrmSurveyFormVm";
import { ICrmSurveyResponse, ICrmSurveyResponseVm } from "../../models/response/crm-survey/ICrmSurveyResponse";
import SurveyFormBlock from "./SurveyFormBlock";

interface ICrmSurveyFormGoLivePageProps {
    isAttendance: boolean;
    dm_chiendich_id: number;
    crm_survey_form_id: number;
}

const SurveyFormGoLivePage = (props: ICrmSurveyFormGoLivePageProps) => {
    const [isSaving, setIsSaving] = useState(false);

    const { register, handleSubmit, trigger, formState: { errors } } = useForm();
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
    const temp = {} as ICrmSurveyFormActiveVm;
    const [selectedFormVm, setSelectedFormVm] = useState<ICrmSurveyFormActiveVm>(temp);
    const [answerText, setAnswerText] = useState<ICrmSurveyAnswer[]>([]);
    const [error, setError] = useState<{ question_id: number; message: string }[]>([]);
    const [dm_truong_id, setDmTruongId] = useState<number>(0);
    const [dm_khoi_id, setDmKhoiId] = useState<number>(0);
    const [dm_he_id, setDmHeId] = useState<number>(0);

    const [isRegisterSuccess, setIsRegisterSuccess] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const { translate } = useCommonContext();
    const location = useLocation();
    const params = location.pathname.replace('/crm_survey_register/', '').split('/');
    console.log({
        params
    });

    const dm_nguonbiettoi_id = params.length > 0 ? parseInt(params[0].split('_')[1]) : 0;
    const dm_chiendich_id = props.dm_chiendich_id || (params.length > 0 ? parseInt(params[1]) : 0);
    const crm_survey_form_id = props.crm_survey_form_id || (params.length > 1 ? parseInt(params[2]) : 0);
    useEffect(() => {
        if (crm_survey_form_id > 0)
            handleGetDetailAsync();
    }, [crm_survey_form_id])
    const handleGetDetailAsync = async () => {
        setIsLoading(true)
        const res = await crmSurveyFormApi.selectDetail(crm_survey_form_id);
        if (res.is_success) {
            setSelectedFormVm(res.data)
        }
        setIsLoading(false)
    }
    useEffect(() => {
        const loadDataForQuestions = async () => {
            if (!selectedFormVm.questions || selectedFormVm.questions.length === 0) {
                return;
            }
            const updatedQuestions = await Promise.all(
                selectedFormVm.questions.map(async (question) => {
                    if (question.datasource_querry) {
                        try {
                            const res = await crmSurveyFormQuestionApi.select_datasource(question.id, dm_coso_id, 0);

                            // Transform the data to match ICrmSurveyFormQuestionOption interface
                            const options = Array.isArray(res.data)
                                ? res.data
                                    .filter((x: any) => x.text !== '' && x.text !== 'null')
                                    .map((option: any) => ({
                                        id: 0, // Set id to 0 for dynamically loaded options
                                        crm_survey_form_question_id: question.id,
                                        text: option.text,
                                        image: ''
                                    }))
                                : [];

                            return {
                                ...question,
                                options: options,
                            };
                        } catch (error) {
                            console.error(`Error loading options for question ${question.id}:`, error);
                            return question;
                        }
                    }
                    return question;
                })
            );

            setSelectedFormVm((prev) => ({
                ...prev,
                questions: updatedQuestions,
            }));
        };

        loadDataForQuestions();
    }, [selectedFormVm.form]);

    const handleValueChanged = async (crm_survey_form_id: number, crm_survey_form_question_id: number, id: number, text: string) => {
        setAnswerText((prev: any) => {
            const updatedAnswers = prev.map((answer: any) => {
                if (answer.crm_survey_form_question_id === crm_survey_form_question_id) {
                    return {
                        ...answer,
                        crm_survey_form_question_id: crm_survey_form_question_id,
                        crm_survey_form_question_option_id: id,
                        answer_text: text,
                    };
                }
                return answer;
            });
            if (!updatedAnswers.find((answer: any) => answer.crm_survey_form_question_id === crm_survey_form_question_id)) {
                return [
                    ...updatedAnswers,
                    {
                        crm_survey_form_question_id: crm_survey_form_question_id,
                        crm_survey_form_question_option_id: id,
                        answer_text: text,
                    },
                ];
            }

            return updatedAnswers;
        });
    };
    const handleRadioChange = (questionId: number, answerTextValue: string) => {
        setAnswerText((prevAnswers: any) => {
            const existingAnswerIndex = prevAnswers.findIndex((answer: any) => answer.crm_survey_form_question_id === questionId);

            if (existingAnswerIndex > -1) {
                const updatedAnswers = [...prevAnswers];
                updatedAnswers[existingAnswerIndex] = {
                    ...updatedAnswers[existingAnswerIndex],
                    answer_text: answerTextValue,
                    crm_survey_form_question_option_id: 0
                };
                return updatedAnswers;
            } else {
                return [
                    ...prevAnswers,
                    {
                        crm_survey_form_question_id: questionId, crm_survey_form_question_option_id: 0,
                        answer_text: answerTextValue
                    }
                ];
            }
        });
    };
    const handleCheckboxChange = (questionId: number, answerTextValue: string, isChecked: boolean) => {
        setAnswerText((prevAnswers: any) => {
            if (isChecked) {
                return [
                    ...prevAnswers,
                    { crm_survey_form_question_id: questionId, answer_text: answerTextValue, crm_survey_form_question_option_id: 0 }
                ];
            } else {
                return prevAnswers.filter(
                    (answer: any) => !(answer.crm_survey_form_question_id === questionId && answer.answer_text === answerTextValue)
                );
            }
        });
    };
    const handleTextInputChange = (questionId: number, newText: string) => {
        setAnswerText((prevAnswers: any) => {
            const existingAnswer = prevAnswers.find(
                (answer: any) => answer.crm_survey_form_question_id === questionId
            );
            if (existingAnswer) {
                return prevAnswers.map((answer: any) =>
                    answer.crm_survey_form_question_id === questionId
                        ? { ...answer, answer_text: newText }
                        : answer
                );
            } else {
                return [
                    ...prevAnswers,
                    { crm_survey_form_question_id: questionId, answer_text: newText, crm_survey_form_question_option_id: 0 }
                ];
            }
        });
    };
    const handleSubmitForm = async () => {
        const newErrors: { question_id: number; message: string }[] = [];
        selectedFormVm.questions?.forEach(question => {
            if (question.is_required && !answerText.some(answer => answer.crm_survey_form_question_id === question.id)) {
                newErrors.push({ question_id: question.id, message: `Vui lòng chọn câu trả lời cho câu hỏi "${question.title}"` });
            }
        });

        if (newErrors.length >= 0) {
            setError(newErrors);
        }
        if (newErrors.length === 0) {

            const crmSurveyResponse: ICrmSurveyResponse = {
                id: 0,
                dm_chiendich_id: dm_chiendich_id,
                dm_nguonbiettoi_id: dm_nguonbiettoi_id ?? 0,
                crm_survey_form_id: crm_survey_form_id,
                register_date: new Date(),
                phone_number: '',
                ho_ten: '',
                email: '',
                is_open_email_result: false,
                is_send_email_result: false,
                total_open_email_result: 0
            };
            const submitQuestions: ICrmSurveyResponseVm = {
                crmSurveyResponse: crmSurveyResponse,
                questions: selectedFormVm.questions,
                answers: answerText,
            };
            setIsSaving(true);
            const res = await crmSurveyResponseApi.saveChanges(submitQuestions)
            if (res.is_success) {
                setIsRegisterSuccess(true);
                setAnswerText([]);
                setError([]);
            }
            setIsSaving(false);
        }
    };
    useEffect(() => {
        let newErrors = [...error];
        selectedFormVm.questions?.forEach(question => {
            const hasAnswer = answerText.some(answer => answer.crm_survey_form_question_id === question.id);
            const errorIndex = newErrors.findIndex(err => err.question_id === question.id);
            if (hasAnswer) {
                if (errorIndex !== -1) {
                    newErrors.splice(errorIndex, 1);
                }
            }
        });

        setError(newErrors);
    }, [answerText]);

    return (
        <Box
            sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                minHeight: "100vh",
                padding: 2,
            }}
        >
            <Box
                sx={{
                    borderRadius: 2,
                    maxWidth: 850,
                    width: "80%",
                    display: "flex",
                    flexDirection: "column",
                    padding: 2,
                    margin: "0 auto",

                    "@media (max-width: 1200px)": {
                        maxWidth: 800,
                    },
                    "@media (max-width: 1024px)": {
                        maxWidth: 500,
                    },
                    "@media (max-width: 768px)": {
                        maxWidth: 450,
                    },
                    "@media (max-width: 600px)": {
                        width: "100%",
                        height: "100vh",
                    },
                }}
            >
                {!isRegisterSuccess && selectedFormVm.form?.cover_img && (
                    <img
                        src={selectedFormVm.form?.cover_img}
                        alt="cover"
                        style={{
                            width: "100%",
                            height: "auto",
                            borderRadius: "10px"
                        }}
                    />
                )}

                {isRegisterSuccess && (
                    <>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                minHeight: '30vh',
                                textAlign: 'center',
                                maxWidth: 850,
                                width: "80%",
                                padding: 2,
                                margin: "0 auto",
                                "@media (max-width: 1200px)": {
                                    maxWidth: 800,
                                },
                                "@media (max-width: 1024px)": {
                                    maxWidth: 600,
                                },
                                "@media (max-width: 768px)": {
                                    maxWidth: 500,
                                },
                                "@media (max-width: 600px)": {
                                    width: "90%",
                                    height: "100vh",
                                    padding: 1,
                                },
                            }}
                        >
                            <img
                                src={selectedFormVm.form?.cover_img}
                                alt="cover"
                                style={{
                                    maxWidth: '100%',
                                    height: 'auto',
                                    borderRadius: "10px",
                                    marginBottom: 16,
                                }}
                            />
                            <img src='https://static.vecteezy.com/system/resources/previews/006/793/534/non_2x/thank-you-speech-banner-bubble-icon-for-graphic-design-logo-website-social-media-mobile-app-sticker-banner-label-ui-free-vector.jpg'
                                style={{
                                    height: "200px"
                                }}
                                alt='tks'
                            />
                            {/* <img
                                src="../../../images/thank-you.png"
                                alt="Thank You"
                                style={{ maxWidth: '80%', height: 300, marginBottom: 16 }}
                            /> */}
                            <Text
                                sx={{
                                    fontSize: "15px",
                                    fontWeight: "650",
                                    m: 2,
                                }}
                                text="Quý Phụ huynh đã đăng ký thành công. Hệ thống đã gửi email tới hòm thư Quý Phụ huynh đăng ký."
                            />
                            <Text text="Phòng tuyển sinh sẽ liên hệ lại với phụ huynh trong thời gian sớm nhất." />
                            <Box sx={{
                                display: "flex",
                                justifyContent: "center",
                                m: 2,
                            }}>
                                <Button
                                    text="Trở lại"
                                    variant="invisible"
                                    size="medium"
                                    isLoading={isLoading}
                                    onClick={() => { setIsRegisterSuccess(false); }}
                                    icon={UndoIcon}
                                />
                            </Box>
                        </Box>


                    </>
                )}
                {!isRegisterSuccess && (
                    <>
                        <Box sx={{ mb: 2, border: '1px solid #d0d7de', borderRadius: '10px', marginTop: '10px' }}>
                            <Box sx={{ pt: 3, pb: 1 }} id="form-name">
                                <Text
                                    text={selectedFormVm.form?.name}
                                    sx={{
                                        fontSize: "24px",
                                        fontWeight: "600",
                                        ml: 3
                                    }}
                                />
                            </Box>
                            <Box id="form-description">
                                <div
                                    dangerouslySetInnerHTML={{ __html: selectedFormVm.form?.description }}
                                    style={{ whiteSpace: "pre-line", marginLeft: 15, marginBottom: 20 }}
                                />
                            </Box>
                        </Box>

                        <Box>
                            {selectedFormVm.questions?.map((question, idx) => (
                                <SurveyFormBlock key={idx} formQuestionSelectedIds={[]} >
                                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                                        <Box sx={{ mb: 2 }}>
                                            <Text text={question.title} sx={{ fontWeight: 600, mb: 2 }} />
                                            <Box id="question-description">
                                                <Text
                                                    text={question.description}
                                                    sx={{
                                                        whiteSpace: "pre-line",
                                                    }}
                                                />
                                            </Box>
                                        </Box>
                                        {question.image && (
                                            <Box>
                                                <img
                                                    src={question.image}
                                                    style={{
                                                        width: "100%",
                                                        height: "auto",
                                                    }}
                                                    alt=""
                                                />
                                            </Box>
                                        )}
                                        <Box>
                                            {question.crm_survey_form_question_type_id === eSurveyFormQuestionTypeId.SELECT_SINGLE_RADIO && (
                                                <>
                                                    {question.options?.map((option, j) => (
                                                        <>
                                                            <FormControl key={j}>
                                                                <Radio
                                                                    value={option.text}
                                                                    checked={answerText.some(answer => answer.crm_survey_form_question_id === question.id && answer.answer_text === option.text)}
                                                                    onChange={() => {
                                                                        handleRadioChange(question.id, option.text);
                                                                    }}
                                                                />
                                                                <FormControl.Label>{option.text}</FormControl.Label>
                                                            </FormControl>

                                                        </>
                                                    ))}
                                                    {question.is_required && error.some(err => err.question_id === question.id) && (
                                                        <Box sx={{ mt: 2 }}>
                                                            <span style={{ marginRight: "5px", color: 'red' }}><AlertIcon size={"small"} /></span>
                                                            <span style={{ marginRight: "5px", color: 'red' }}>
                                                                <TextTranslated value={error.find(err => err.question_id === question.id)?.message ?? ''} />
                                                            </span>
                                                        </Box>
                                                    )}
                                                </>
                                            )}
                                            {question.crm_survey_form_question_type_id === eSurveyFormQuestionTypeId.SELECT_MULTIPLE_CHECKLIST && (
                                                <>
                                                    {question.options?.map((option, j) => (
                                                        <FormControl key={j}>
                                                            <Checkbox
                                                                value={option.text}
                                                                checked={answerText.some(answer => answer.crm_survey_form_question_id === question.id && answer.answer_text === option.text)}
                                                                onChange={(e) => handleCheckboxChange(question.id, option.text, e.target.checked)}
                                                            />
                                                            <FormControl.Label>{option.text}</FormControl.Label>
                                                        </FormControl>
                                                    ))}
                                                    {question.is_required && error.some(err => err.question_id === question.id) && (
                                                        <Box sx={{ mt: 2 }}>
                                                            <span style={{ marginRight: "5px", color: 'red' }}><AlertIcon size={"small"} /></span>
                                                            <span style={{ marginRight: "5px", color: 'red' }}>
                                                                <TextTranslated value={error.find(err => err.question_id === question.id)?.message ?? ''} />
                                                            </span>
                                                        </Box>
                                                    )}
                                                </>
                                            )}
                                            {question.crm_survey_form_question_type_id === eSurveyFormQuestionTypeId.TEXT_INPUT && (
                                                <>
                                                    <TextInput
                                                        block
                                                        placeholder="Nhập câu trả lời của bạn"
                                                        onChange={(e) => handleTextInputChange(question.id, e.target.value)}
                                                    />
                                                    {question.is_required && error.some(err => err.question_id === question.id) && (
                                                        <Box sx={{ mt: 2 }}>
                                                            <span style={{ marginRight: "5px", color: 'red' }}><AlertIcon size={"small"} /></span>
                                                            <span style={{ marginRight: "5px", color: 'red' }}>
                                                                <TextTranslated value={error.find(err => err.question_id === question.id)?.message ?? ''} />
                                                            </span>
                                                        </Box>
                                                    )}
                                                </>
                                            )}
                                            {(question.crm_survey_form_question_type_id === eSurveyFormQuestionTypeId.SELECT_SINGLE_COMBOBOX && question.crm_survey_question_type_id == 0) && (
                                                <>
                                                    <CrmSurveyComboBox
                                                        crm_survey_form_question_id={question.id}
                                                        dm_coso_id={dm_coso_id}
                                                        fieldValue={0}
                                                        align="left"
                                                        value={
                                                            Array.isArray(answerText)
                                                                ? answerText.find(answer => answer.crm_survey_form_question_id === question.id)?.crm_survey_form_question_option_id || 0
                                                                : 0
                                                        }
                                                        onValueChanged={(id: number, data?: ICrmSurveyFormQuestionDataSource) => {
                                                            if (data) {
                                                                handleValueChanged(question.crm_survey_form_id, question.id, id, data.text);
                                                            } else {
                                                                handleValueChanged(question.crm_survey_form_id, question.id, id, '');
                                                            }
                                                        }}
                                                        width={'100%'}
                                                        isShowClearButton={true}
                                                    />
                                                    {question.is_required && error.some(err => err.question_id === question.id) && (
                                                        <Box sx={{ mt: 2 }}>
                                                            <span style={{ marginRight: "5px", color: 'red' }}><AlertIcon size={"small"} /></span>
                                                            <span style={{ marginRight: "5px", color: 'red' }}>
                                                                <TextTranslated value={error.find(err => err.question_id === question.id)?.message ?? ''} />
                                                            </span>
                                                        </Box>
                                                    )}
                                                </>
                                            )}
                                            {(question.crm_survey_form_question_type_id === eSurveyFormQuestionTypeId.SELECT_SINGLE_COMBOBOX && question.crm_survey_question_type_id == eSurveyQuestionTypeId.dm_truong_id) && (
                                                <>
                                                    <CrmSurveyComboBox
                                                        crm_survey_form_question_id={question.id}
                                                        dm_coso_id={dm_coso_id}
                                                        fieldValue={dm_truong_id}
                                                        align="left"
                                                        value={
                                                            Array.isArray(answerText)
                                                                ? answerText.find(answer => answer.crm_survey_form_question_id === question.id)?.crm_survey_form_question_option_id || 0
                                                                : 0
                                                        }
                                                        onValueChanged={(id: number, data?: ICrmSurveyFormQuestionDataSource) => {
                                                            if (data) {
                                                                handleValueChanged(question.crm_survey_form_id, question.id, id, data.text);
                                                            } else {
                                                                handleValueChanged(question.crm_survey_form_id, question.id, id, '');
                                                            }
                                                            setDmTruongId(id)
                                                        }}
                                                        width={'100%'}
                                                        isShowClearButton={true}
                                                    />
                                                    {question.is_required && error.some(err => err.question_id === question.id) && (
                                                        <Box sx={{ mt: 2 }}>
                                                            <span style={{ marginRight: "5px", color: 'red' }}><AlertIcon size={"small"} /></span>
                                                            <span style={{ marginRight: "5px", color: 'red' }}>
                                                                <TextTranslated value={error.find(err => err.question_id === question.id)?.message ?? ''} />
                                                            </span>
                                                        </Box>
                                                    )}
                                                </>
                                            )}
                                            {(question.crm_survey_form_question_type_id === eSurveyFormQuestionTypeId.SELECT_SINGLE_COMBOBOX && question.crm_survey_question_type_id === eSurveyQuestionTypeId.dm_khoi_id) && (
                                                <>
                                                    <CrmSurveyComboBox
                                                        crm_survey_form_question_id={question.id}
                                                        dm_coso_id={dm_coso_id}
                                                        fieldValue={dm_truong_id}
                                                        align="left"
                                                        value={
                                                            Array.isArray(answerText)
                                                                ? answerText.find(answer => answer.crm_survey_form_question_id === question.id)?.crm_survey_form_question_option_id || 0
                                                                : 0
                                                        }
                                                        onValueChanged={(id: number, data?: ICrmSurveyFormQuestionDataSource) => {
                                                            if (data) {
                                                                handleValueChanged(question.crm_survey_form_id, question.id, id, data.text);
                                                            } else {
                                                                handleValueChanged(question.crm_survey_form_id, question.id, id, '');
                                                            }
                                                        }}
                                                        width={'100%'}
                                                        isShowClearButton={true}
                                                    />
                                                    {question.is_required && error.some(err => err.question_id === question.id) && (
                                                        <Box sx={{ mt: 2 }}>
                                                            <span style={{ marginRight: "5px", color: 'red' }}><AlertIcon size={"small"} /></span>
                                                            <span style={{ marginRight: "5px", color: 'red' }}>
                                                                <TextTranslated value={error.find(err => err.question_id === question.id)?.message ?? ''} />
                                                            </span>
                                                        </Box>
                                                    )}
                                                </>
                                            )}
                                            {(question.crm_survey_form_question_type_id === eSurveyFormQuestionTypeId.SELECT_SINGLE_COMBOBOX && question.crm_survey_question_type_id == eSurveyQuestionTypeId.dm_he_id) && (
                                                <>
                                                    <CrmSurveyComboBox
                                                        crm_survey_form_question_id={question.id}
                                                        dm_coso_id={dm_coso_id}
                                                        fieldValue={dm_he_id}
                                                        align="left"
                                                        value={
                                                            Array.isArray(answerText)
                                                                ? answerText.find(answer => answer.crm_survey_form_question_id === question.id)?.crm_survey_form_question_option_id || 0
                                                                : 0
                                                        }
                                                        onValueChanged={(id: number, data?: ICrmSurveyFormQuestionDataSource) => {
                                                            if (data) {
                                                                handleValueChanged(question.crm_survey_form_id, question.id, id, data.text);
                                                            } else {
                                                                handleValueChanged(question.crm_survey_form_id, question.id, id, '');
                                                            }
                                                            setDmHeId(id);
                                                        }}
                                                        width={'100%'}
                                                        isShowClearButton={true}
                                                    />
                                                    {question.is_required && error.some(err => err.question_id === question.id) && (
                                                        <Box sx={{ mt: 2 }}>
                                                            <span style={{ marginRight: "5px", color: 'red' }}><AlertIcon size={"small"} /></span>
                                                            <span style={{ marginRight: "5px", color: 'red' }}>
                                                                <TextTranslated value={error.find(err => err.question_id === question.id)?.message ?? ''} />
                                                            </span>
                                                        </Box>
                                                    )}
                                                </>
                                            )}
                                            {question.crm_survey_form_question_type_id === eSurveyFormQuestionTypeId.DATE_BOX && (
                                                <>
                                                    <FormControl>
                                                        <TextInput
                                                            sx={{ width: '100%' }}
                                                            type="date"
                                                            value={
                                                                Array.isArray(answerText) && answerText.length > 0
                                                                    ? answerText.find(answer => answer.crm_survey_form_question_id === question.id)?.answer_text || moment(new Date()).format("YYYY-MM-DD")
                                                                    : moment(new Date()).format("YYYY-MM-DD")
                                                            }
                                                            onChange={(e) => {
                                                                const selectedDate = e.target.value;
                                                                handleValueChanged(question.crm_survey_form_id, question.id, 0, moment(selectedDate).format("YYYY-MM-DD"));
                                                            }}
                                                        />
                                                    </FormControl>
                                                    {question.is_required && error.some(err => err.question_id === question.id) && (
                                                        <Box sx={{ mt: 2 }}>
                                                            <span style={{ marginRight: "5px", color: 'red' }}><AlertIcon size={"small"} /></span>
                                                            <span style={{ marginRight: "5px", color: 'red' }}>
                                                                <TextTranslated value={error.find(err => err.question_id === question.id)?.message ?? ''} />
                                                            </span>
                                                        </Box>
                                                    )}
                                                </>
                                            )}
                                        </Box>
                                    </Box>
                                </SurveyFormBlock>
                            ))}
                            <Box sx={{
                                display: "flex",
                                justifyContent: "center",

                            }}>
                                <Button
                                    text="Cập nhật"
                                    variant="primary"
                                    size="medium"
                                    isLoading={isSaving}
                                    onClick={handleSubmitForm}
                                />
                            </Box>
                        </Box>
                    </>
                )}
            </Box>
        </Box >
    );
};

export default SurveyFormGoLivePage;
