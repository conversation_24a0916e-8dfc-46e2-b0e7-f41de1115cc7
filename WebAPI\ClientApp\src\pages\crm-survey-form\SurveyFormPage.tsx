import { KebabHorizontalIcon, PencilIcon, PlusIcon, TrashIcon } from "@primer/octicons-react";
import { ActionList, ActionMenu, Box, IconButton, useConfirm } from "@primer/react";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { crmSurveyFormApi } from "../../api/crmSurveyFormApi";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import Text from "../../components/ui/text";
import { NotifyHelper } from "../../helpers/toast";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { ICrmSurveyForm } from "../../models/response/crm-survey/ICrmSurveyForm";
import { ICrmSurveyFormVm } from "../../models/response/crm-survey/ICrmSurveyFormVm";
import SurveyFormPreview from "./SurveyFormPreview";

const SurveyForm = () => {
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

    const [selectedFormVm, setSelectedFormVm] = useState<ICrmSurveyFormVm>();
    const [forms, setForms] = useState<ICrmSurveyForm[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedId, setSelectedId] = useState(0);
    const dispatch = useDispatch();
    const nav = useNavigate();
    const confirm = useConfirm();
    useEffect(() => {
        handleGetAllAsync();
    }, [nam_hoc, dm_coso_id])
    useEffect(() => {
        if (selectedId > 0)
            handleGetDetailAsync();
    }, [selectedId])
    const handleGetAllAsync = async () => {
        setIsLoading(true)
        const res = await crmSurveyFormApi.selectAll();
        if (res.is_success) {
            setForms([...res.data.filter((x: any) => x.nam_hoc === nam_hoc && x.dm_coso_id === dm_coso_id)])
        } else {
            NotifyHelper.Error(res.message ?? "Error")
        }
        setIsLoading(false)
    }
    const handleGetDetailAsync = async () => {
        setIsLoading(true)
        const res = await crmSurveyFormApi.selectDetail(selectedId);
        if (res.is_success) {
            setSelectedFormVm(res.data)
        } else {
            // NotifyHelper.Error(res.message ?? "Error")
        }
        setIsLoading(false)
    }
    const handleDeleteAsync = async (id: number) => {
        if (await confirm({
            content: "Bạn có chắc chắn muốn xóa form này?",
            title: "Lưu ý",
            cancelButtonContent: "Không xóa",
            confirmButtonContent: "Xác nhận xóa",
            confirmButtonType: "danger"
        })) {
            const res = await crmSurveyFormApi.delete(id);
            if (res.is_success) {
                handleGetAllAsync();
                NotifyHelper.Success("Success")
            } else {
                NotifyHelper.Error(res.message ?? "Error")
            }
        }
    }

    // const handleRefresh = useCallback(() => {
    // 	dispatch(actions.formWrapper.formItem.S());
    // 	onSelectionChanged([]);
    // }, [dm_truong_selected_id]);
    return (
        <Box sx={{
            display: "flex"
        }}>
            <Box id="form-list" sx={{
                flex: 1,
                p: 3,
                maxWidth: '60%'
            }}>
                <Box sx={{ display: "flex", mb: 3 }}>
                    <Box sx={{ flex: 1 }}>
                        <Text text="Danh sách" sx={{ fontSize: 24, fontWeight: "600" }} />
                    </Box>
                    <Button text="Thêm mới" variant="primary" size="medium" leadingVisual={PlusIcon}
                        onClick={() => {
                            nav(`../../crm_survey/form/0`);
                        }}
                    />
                </Box>
                <Box sx={{
                    height: window.innerHeight - 90 - 140,
                    overflowY: "auto",
                    // pr: 3
                }}>
                    <ActionList showDividers variant="full" selectionVariant="single">
                        {forms.map(form => {
                            return (
                                <ActionList.Item onSelect={() => {
                                    setSelectedId(form.id)
                                }}
                                    selected={form.id === selectedId}
                                >

                                    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                                        <Box sx={{ flexShrink: 0 }}>
                                            <Box sx={{
                                                backgroundImage: `url("${form.thumbail_img}")`,
                                                height: "100px",
                                                width: "100px",
                                                borderRadius: "5px",
                                                backgroundRepeat: "no-repeat",
                                                backgroundPosition: "center",
                                                backgroundSize: "cover",
                                                border: "1px solid rgba(125, 125, 125, 0.5)"
                                            }} />
                                        </Box>
                                        <Box sx={{ flex: 1 }}>
                                            <Box sx={{ fontWeight: "600", fontSize: "14px", mb: 1 }}>{form.name}</Box>
                                            <Box sx={{
                                                fontSize: "12px",
                                                color: "fg.muted",
                                                whiteSpace: "pre-line"
                                            }}
                                                className="limit2Line"
                                            >
                                                <div dangerouslySetInnerHTML={{ __html: form.description }} />
                                            </Box>
                                        </Box>
                                    </Box>

                                    <ActionList.TrailingVisual>
                                        <ActionMenu>
                                            <ActionMenu.Anchor>
                                                <IconButton icon={KebabHorizontalIcon} variant="invisible" aria-label="Open column options" />
                                            </ActionMenu.Anchor>

                                            <ActionMenu.Overlay>
                                                <ActionList>
                                                    <ActionList.Item onSelect={() => {
                                                        nav(`../../crm_survey/form/${form.id}`);

                                                    }}>
                                                        <ActionList.LeadingVisual>
                                                            <PencilIcon />
                                                        </ActionList.LeadingVisual>
                                                        <TextTranslated value="Base.Label.Edit" />
                                                    </ActionList.Item>
                                                    <ActionList.Divider />
                                                    <ActionList.Item variant="danger"
                                                        onSelect={() => {
                                                            handleDeleteAsync(form.id)
                                                        }}
                                                    >
                                                        <ActionList.LeadingVisual>
                                                            <TrashIcon />
                                                        </ActionList.LeadingVisual>
                                                        <TextTranslated value="Base.Label.Delete" />
                                                    </ActionList.Item>
                                                </ActionList>
                                            </ActionMenu.Overlay>
                                        </ActionMenu>
                                    </ActionList.TrailingVisual>

                                </ActionList.Item>
                            )
                        })}

                    </ActionList>
                </Box>
            </Box>
            <Box
                id="form-preview"
                sx={{
                    backgroundColor: "#F0EBF8",
                    p: 2,
                    height: window.innerHeight - 90 - 50,
                    maxHeight: "calc(100vh - 150px)",
                    overflowY: "auto",
                    width: "40%"
                }}
            >
                <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
                    <Box sx={{ flex: 1 }}>
                        <Text text="Preview" sx={{
                            fontSize: "20px",
                            fontWeight: "600",
                            color: "fg.muted"
                        }} />
                    </Box>
                    {selectedFormVm &&
                        <Box>
                            <Button text="Chỉnh sửa" leadingVisual={PencilIcon} variant="invisible"
                                onClick={() => {
                                    nav(`../../crm_survey/form/${selectedFormVm.form.id}`);
                                }}
                            />
                        </Box>
                    }
                </Box>
                {selectedFormVm &&
                    <SurveyFormPreview
                        data={selectedFormVm}
                    />
                }


            </Box>
        </Box >
    );
};

export default SurveyForm;