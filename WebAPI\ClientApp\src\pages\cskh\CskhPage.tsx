import {
    SyncIcon,
    PlusIcon
} from "@primer/octicons-react";
import { Box, CounterLabel, IssueLabelToken, Label, useConfirm } from "@primer/react";
import React, { useEffect, useState } from 'react';
import { chamSocKhachHangApi } from "../../api/chamSocKhachHang<PERSON>pi";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import Text from "../../components/ui/text";
import TextWithEllipse from "../../components/ui/text-with-ellipse";
import { useCommonContext } from "../../contexts/common";
import { ts_chamsockhachhang } from "../../models/response/cskh/ts_chamsockhachhang";
import PhuHuynhProfile from '../phu-huynh-profile';
import PhuHuynhProfileNew from '../phu-huynh-profile/PhuHuynhProfileNew';

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho filter
interface FilterItem {
    label: string;
    value: string;
    count: number;
}

// Component FilterButton
interface FilterButtonProps {
    label: string;
    count: number;
    isActive: boolean;
    onClick: () => void;
}

const FilterButton: React.FC<FilterButtonProps> = ({ label, count, isActive, onClick }) => {
    return (
        <Button
            onClick={onClick}
            variant="invisible"
            sx={{
                borderRadius: 3,
                border: "1px solid",
                borderColor: "border.default",
                backgroundColor: isActive ? "accent.emphasis" : "#fff",
                color: isActive ? '#fff' : "fg.default"
            }}
        // count={count}
        >
            {label}
            {count !== undefined &&
                <CounterLabel sx={{
                    backgroundColor: isActive ? "#fff" : "border.muted",
                    ml: 1
                }}>
                    {count}
                </CounterLabel>
            }

        </Button>
    );
};

// Component FilterStatusBar
interface FilterStatusBarProps {
    filters: FilterItem[];
    onFilterChange: (value: string) => void;
    activeFilter: string;
}

const FilterStatusBar: React.FC<FilterStatusBarProps> = ({ filters, onFilterChange, activeFilter }) => {
    return (
        <Box
            style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 2,
            }}
        >
            {filters.map((filter) => (
                <FilterButton
                    key={filter.value}
                    label={filter.label}
                    count={filter.count}
                    isActive={activeFilter === filter.value}
                    onClick={() => onFilterChange(filter.value)}
                />
            ))}
        </Box>
    );
};

const CskhPage: React.FC = () => {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [cskhs, setCskhs] = useState<ts_chamsockhachhang[]>([]);
    const [filteredData, setFilteredData] = useState<ts_chamsockhachhang[]>([]);
    const [isShowEditModal, setIsShowEditModal] = useState<boolean>(false);
    const [editingData, setEditingData] = useState<ts_chamsockhachhang | undefined>(undefined);
    const [activeFilter, setActiveFilter] = useState<string>('all');
    const { translate } = useCommonContext();
    const confirm = useConfirm();
    const [isShowPHModal, setIsShowPHModal] = useState<boolean>(false);
    const [isShowPHNewModal, setIsShowPHNewModal] = useState<boolean>(false);
    const [selectedId, setSelectedId] = useState<number>(0);

    // Xác định các bộ lọc và đếm số lượng
    const getFilterCounts = (data: ts_chamsockhachhang[]): FilterItem[] => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Đếm các mục cho từng trạng thái
        const processed = data.filter(item => item.da_xu_ly === true).length;
        const unprocessed = data.filter(item => item.da_xu_ly !== true).length;
        const hasAppointment = data.filter(item => item.ngay_hen_tra_loi).length;

        const overdue = data.filter(item => {
            if (!item.ngay_hen_tra_loi || item.da_xu_ly) return false;
            const dueDate = new Date(item.ngay_hen_tra_loi);
            return dueDate < today;
        }).length;

        const needToday = data.filter(item => {
            if (!item.ngay_hen_tra_loi || item.da_xu_ly) return false;
            const dueDate = new Date(item.ngay_hen_tra_loi);
            dueDate.setHours(0, 0, 0, 0);
            return dueDate.getTime() === today.getTime();
        }).length;

        const needTomorrow = data.filter(item => {
            if (!item.ngay_hen_tra_loi || item.da_xu_ly) return false;
            const dueDate = new Date(item.ngay_hen_tra_loi);
            dueDate.setHours(0, 0, 0, 0);
            return dueDate.getTime() === tomorrow.getTime();
        }).length;

        return [
            { label: "Tất cả", value: "all", count: data.length },
            { label: "Đã xử lý", value: "processed", count: processed },
            { label: "Chưa xử lý", value: "unprocessed", count: unprocessed },
            { label: "Có ngày hẹn", value: "has_appointment", count: hasAppointment },
            { label: "Đã quá hạn", value: "overdue", count: overdue },
            { label: "Cần xử lý trong ngày", value: "need_today", count: needToday },
            { label: "Cần xử lý trong ngày tiếp theo", value: "need_tomorrow", count: needTomorrow }
        ];
    };

    const [filters, setFilters] = useState<FilterItem[]>([
        { label: "Tất cả", value: "all", count: 0 },
        { label: "Đã xử lý", value: "processed", count: 0 },
        { label: "Chưa xử lý", value: "unprocessed", count: 0 },
        { label: "Có ngày hẹn", value: "has_appointment", count: 0 },
        { label: "Đã quá hạn", value: "overdue", count: 0 },
        { label: "Cần xử lý trong ngày", value: "need_today", count: 0 },
        { label: "Cần xử lý trong ngày tiếp theo", value: "need_tomorrow", count: 0 }
    ]);

    useEffect(() => {
        handleReload();
    }, []);

    // Cập nhật bộ lọc mỗi khi dữ liệu thay đổi
    useEffect(() => {
        const updatedFilters = getFilterCounts(cskhs);
        setFilters(updatedFilters);
        handleFilterChange(activeFilter);
    }, [cskhs]);

    const handleReload = async (): Promise<void> => {
        setIsLoading(true);
        try {
            const response = await chamSocKhachHangApi.selectAll();
            if (response && response.data) {
                setCskhs(response.data);
            }
        } catch (error) {
            console.error("Error loading data:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleFilterChange = (filterValue: string): void => {
        setActiveFilter(filterValue);
        filterData(filterValue);
    };

    const filterData = (filterValue: string): void => {
        if (!cskhs.length) return;

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        let result: ts_chamsockhachhang[];

        switch (filterValue) {
            case 'processed':
                result = cskhs.filter(item => item.da_xu_ly === true);
                break;

            case 'unprocessed':
                result = cskhs.filter(item => item.da_xu_ly !== true);
                break;

            case 'has_appointment':
                result = cskhs.filter(item => item.ngay_hen_tra_loi);
                break;

            case 'overdue':
                result = cskhs.filter(item => {
                    if (!item.ngay_hen_tra_loi || item.da_xu_ly) return false;
                    const dueDate = new Date(item.ngay_hen_tra_loi);
                    return dueDate < today;
                });
                break;

            case 'need_today':
                result = cskhs.filter(item => {
                    if (!item.ngay_hen_tra_loi || item.da_xu_ly) return false;
                    const dueDate = new Date(item.ngay_hen_tra_loi);
                    dueDate.setHours(0, 0, 0, 0);
                    return dueDate.getTime() === today.getTime();
                });
                break;

            case 'need_tomorrow':
                result = cskhs.filter(item => {
                    if (!item.ngay_hen_tra_loi || item.da_xu_ly) return false;
                    const dueDate = new Date(item.ngay_hen_tra_loi);
                    dueDate.setHours(0, 0, 0, 0);
                    return dueDate.getTime() === tomorrow.getTime();
                });
                break;

            default:
                result = [...cskhs];
        }

        setFilteredData(result);
    };

    const handleDelete = async (id: number): Promise<void> => {
        if (await confirm({
            content: translate("Base.Label.DeleteConfirm.Content"),
            title: translate("Base.Label.Confirm"),
            cancelButtonContent: translate("Base.Label.Close"),
            confirmButtonContent: translate("Base.Label.Delete"),
            confirmButtonType: "danger"
        })) {
            try {
                // Thêm API xóa ở đây nếu có
                // await chamSocKhachHangApi.delete(id);
                handleReload();
            } catch (error) {
                console.error("Error deleting data:", error);
            }
        }
    };

    const showEditModal = (data?: ts_chamsockhachhang): void => {
        setEditingData(data);
        setIsShowEditModal(true);
    };

    const hideEditModal = (): void => {
        setIsShowEditModal(false);
        setEditingData(undefined);
    };

    const handleSave = async (): Promise<void> => {
        hideEditModal();
        await handleReload();
    };

    const handleCustomerClick = (data: ts_chamsockhachhang): void => {
        // Giả sử id khách hàng được lưu trong data.id
        setSelectedId(data.ts_phuhuynh_id);
        setIsShowPHModal(true);
    };

    // Hàm định dạng ngày/tháng đẹp hơn
    const formatDate = (dateString: string | null | undefined): string => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    };

    return (
        <Box sx={{
            p: 3
        }}>
            <Box sx={{ mb: 2 }}>
                <Text text="Chăm sóc khách hàng" sx={{
                    fontSize: 24,
                    fontWeight: 600
                }} />
            </Box>

            <DataTable
                height={`${window.innerHeight - 250}px`}
                data={filteredData}
                isLoading={isLoading}
                // title={translate("Chăm sóc khách hàng")}
                titleComponent={
                    <Box>
                        <FilterStatusBar
                            filters={filters}
                            onFilterChange={handleFilterChange}
                            activeFilter={activeFilter}
                        />

                    </Box>
                }
                searchEnable
                paging={{
                    enable: true,
                    pageSizeItems: [20, 50, 200, 500, 1000],
                }}
                actionComponent={
                    <Box sx={{
                        display: "flex"
                    }}>
                        <Button
                            text="Thêm mới"
                            leadingVisual={PlusIcon}
                            size="medium"
                            variant="invisible"
                            sx={{
                                marginLeft: 1,
                                borderRadius: '6px'
                            }}
                            onClick={() => setIsShowPHNewModal(true)}
                        />
                        <Button
                            text="Base.Label.Refresh"
                            leadingVisual={SyncIcon}
                            size="medium"
                            sx={{
                                marginLeft: 1,
                                borderRadius: '6px'
                            }}
                            onClick={handleReload}
                        />
                    </Box>
                }
                columns={[
                    {
                        dataField: "da_xu_ly",
                        width: 100,
                        caption: translate("Trạng thái"),
                        cellRender: (data: ts_chamsockhachhang) => {
                            return (
                                <>
                                    {data.da_xu_ly &&
                                        <IssueLabelToken text="Đã xử lý" fillColor="#dafbe1" style={{
                                            color: "#1f883d"
                                        }} />
                                    }
                                    {!data.da_xu_ly &&
                                        <IssueLabelToken text="Chưa xử lý" fillColor="#ffebe9" style={{
                                            color: "#cf222e"
                                        }} />
                                    }
                                </>
                            );

                        }
                    },
                    {
                        dataField: "ten_khach_hang",
                        caption: translate("Tên khách hàng"),
                        isMainColumn: true,
                        width: 180,
                        cellRender: (data: ts_chamsockhachhang) => {
                            return (
                                <Box sx={{ m: -1 }}>
                                    <Button text={data.ten_khach_hang}
                                        variant="invisible"
                                        sx={{
                                            color: "fg.default"
                                        }}
                                        onClick={() => handleCustomerClick(data)}
                                    />
                                </Box>
                            );
                        }
                    },
                    {
                        dataField: "dien_thoai",
                        caption: translate("Điện thoại"),
                        width: 110,
                        cellRender: (data: ts_chamsockhachhang) => {
                            return (
                                <div style={{
                                    fontFamily: 'monospace',
                                    fontSize: '13px',
                                    letterSpacing: '0.5px',
                                    color: '#24292e',
                                    textAlign: 'center'
                                }}>
                                    {data.dien_thoai || '—'}
                                </div>
                            );
                        }
                    },
                    {
                        dataField: "noi_dung",
                        caption: translate("Nội dung"),
                        // isMainColumn: true,
                        width: "500px",
                        cellRender: (data: ts_chamsockhachhang) => {
                            return (
                                <Box>
                                    <TextWithEllipse text={`${data.noi_dung} ${data.ghi_chu ? `(${data.ghi_chu})` : ``}`} lineNumber={1} allowToggleText sx={{
                                        // color: "fg.muted",
                                        // fontWeight: 400
                                    }} />
                                    {/* {data.ghi_chu &&
                                        <TextWithEllipse text={`Ghi chú: ${data.noi_dung}`} lineNumber={1} allowToggleText sx={{
                                            color: "fg.muted",
                                            fontWeight: 400
                                        }} />
                                    } */}
                                </Box>
                            );
                        },


                    },

                    {
                        dataField: "ket_qua_xu_ly",
                        caption: translate("Trạng thái"),
                        width: 250,
                        cellRender: (data: ts_chamsockhachhang) => {
                            return (
                                <Box sx={{ color: "fg.muted" }}>
                                    {!data.da_xu_ly &&
                                        <>
                                            {data.ngay_hen_tra_loi &&
                                                <Box >Hẹn trả lời ngày <b>{formatDate(data.ngay_hen_tra_loi)}</b></Box>
                                            }
                                        </>
                                    }
                                    {data.da_xu_ly &&
                                        <TextWithEllipse text={` Đã xử lý ngày ${formatDate(data.ngay_xu_ly)} ${data.ket_qua_xu_ly ? ` (${data.ket_qua_xu_ly})` : ""}`} lineNumber={1} allowToggleText sx={{
                                            color: "fg.muted",
                                            fontWeight: 400
                                        }} />
                                    }
                                </Box>

                            );
                        },
                    },

                ]}
            />

            {isShowPHModal && (
                <PhuHuynhProfile
                    id={selectedId}
                    onClose={() => {
                        setIsShowPHModal(false);
                    }}
                    onSuccess={() => {
                        handleReload();
                        setIsShowPHModal(false);
                    }}
                />
            )}
            {isShowPHNewModal && (
                <PhuHuynhProfileNew
                    onClose={() => {
                        setIsShowPHNewModal(false);
                    }}
                    onSuccess={() => {
                        handleReload();
                        setIsShowPHNewModal(false);
                    }}
                />
            )}
        </Box>
    );
};

export default CskhPage;