import { GearIcon } from "@primer/octicons-react";
import { Box, UnderlineNav } from "@primer/react";
import { useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import TrangThaiHocSinh from "../../trang-thai-hoc-sinh";
import TrangThai<PERSON>huHuynh from "../../trang-thai-phu-huynh";

const HocSinhPhuHuynhPage = () => {
  const { tab } = useParams();
  const navigate = useNavigate();

  const selectedTab = useMemo(() => {
    if (tab === "ts_phuhuynh_adm_status") return "ts_phuhuynh_adm_status";
    return "dm_trangthaihocsinh";
  }, [tab]);

  return (
    <Box>
      <UnderlineNav aria-label="tabs">
        <UnderlineNav.Item
          icon={GearIcon}
          aria-current={
            selectedTab === "dm_trangthaihocsinh" ? "page" : undefined
          }
          onClick={() => {
            navigate("/dm_trangthaihocsinh/dm_trangthaihocsinh");
          }}
        >
          Trạng thái học sinh
        </UnderlineNav.Item>
        <UnderlineNav.Item
          icon={GearIcon}
          aria-current={
            selectedTab === "ts_phuhuynh_adm_status" ? "page" : undefined
          }
          onClick={() => {
            navigate("/dm_trangthaihocsinh/ts_phuhuynh_adm_status");
          }}
        >
          Trạng thái phụ huynh
        </UnderlineNav.Item>
      </UnderlineNav>
      <Box>
        {(!tab || selectedTab === "dm_trangthaihocsinh") && (
          <TrangThaiHocSinh />
        )}
        {selectedTab === "ts_phuhuynh_adm_status" && <TrangThaiPhuHuynh />}
      </Box>
    </Box>
  );
};

export default HocSinhPhuHuynhPage;
