import { KebabHorizontalIcon, PencilIcon, PlusIcon, SyncIcon, TrashIcon } from "@primer/octicons-react";
import { ActionList, ActionMenu, Box, useConfirm } from "@primer/react";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Button from "../../../../components/ui/button";
import DataTable from "../../../../components/ui/data-table";
import Text from "../../../../components/ui/text";
import { useCommonContext } from "../../../../contexts/common";
import { ePageBaseStatus } from "../../../../models/ePageBaseStatus";
import { IDmNguonBietToiType } from "../../../../models/response/category/IDmNguonBietToiType";
import { actions } from "../../../../state/actions/actionsWrapper";
import { RootState } from "../../../../state/reducers";
import TruongEditModal from "./TruongEditModal";
import { useCommonSelectedHook } from "../../../../hooks/useCommonSelectedHook";

const TruongPage = () => {
    const { status, nguonBietToiTypes, isShowEditModal, editingData } = useSelector((x: RootState) => x.crmCategoryWrapper.nguonBietToiType)
    const { translate } = useCommonContext();
    const { dm_coso_id } = useCommonSelectedHook();
    
    const confirm = useConfirm();
    const dispatch = useDispatch();
    useEffect(() => {
        if (status === ePageBaseStatus.is_not_initialization || status === ePageBaseStatus.is_need_reload) {
            handleReload();
        }
    }, [status])
    const handleReload = () => {
        dispatch(actions.crmCategoryWrapper.nguonBietToiType.LOAD_START(''))
    }
    const handleDelete = async (id: number) => {
        if (await confirm({
            content: translate("Base.Label.DeleteConfirm.Content"),
            title: translate("Base.Label.Confirm"),
            cancelButtonContent: translate("Base.Label.Close"),
            confirmButtonContent: translate("Base.Label.Delete"),
            confirmButtonType: "danger"
        })) {
            dispatch(actions.crmCategoryWrapper.nguonBietToiType.DELETE_START(id))
        }
    }

    return (
        <Box sx={{
            p: 3
        }}>
            <DataTable
                data={nguonBietToiTypes}
                isLoading={status === ePageBaseStatus.is_loading}
                title={"NguonBietToiType.Caption"}
                searchEnable
                paging={{
            enable: true,
            pageSizeItems: [20, 50, 200, 500, 1000],
        }}
                actionComponent={
                    <Box sx={{
                        display: "flex"
                    }}>
                        <Button text="Base.Label.AddNew" leadingVisual={PlusIcon}
                            size="medium"
                            variant="primary"
                            onClick={() => {
                                dispatch(actions.crmCategoryWrapper.nguonBietToiType.SHOW_EDIT_MODAL(undefined))
                            }}
                        />
                        <Button text="Base.Label.Refresh" leadingVisual={SyncIcon}
                            size="medium"
                            sx={{ ml: 1 }}
                            onClick={() => {
                                handleReload();
                            }}
                        />
                    </Box>
                }
                columns={[
                    {
                        dataField: "name",
                        caption: translate("NguonBietToiType.Name"),
                    },
                    {
                        dataField: "name_en",
                        caption: translate("NguonBietToiType.NameEn"),
                    },
                    {
                        id: "cmd",
                        caption: "",
                        width: "50px",
                        align: "center",
                        cellRender: (data: IDmNguonBietToiType) => {
                            return (
                                <ActionMenu>
                                    <ActionMenu.Button icon={KebabHorizontalIcon} variant="invisible" sx={{ m: -1 }}>&nbsp;</ActionMenu.Button>
                                    <ActionMenu.Overlay width="auto">
                                        <ActionList>
                                            <ActionList.Item onSelect={() => {
                                                dispatch(actions.crmCategoryWrapper.nguonBietToiType.SHOW_EDIT_MODAL(data))
                                            }}>
                                                <ActionList.LeadingVisual><PencilIcon /></ActionList.LeadingVisual>
                                                <Text text='Base.Label.Edit' />
                                            </ActionList.Item>
                                            <ActionList.Item variant="danger" onSelect={() => {
                                                handleDelete(data.id)
                                            }}>
                                                <ActionList.LeadingVisual><TrashIcon /></ActionList.LeadingVisual>
                                                <Text text='Base.Label.Delete' />
                                            </ActionList.Item>
                                        </ActionList>
                                    </ActionMenu.Overlay>
                                </ActionMenu>
                            );
                        }
                    }
                ]}
            />
            {isShowEditModal &&
                <TruongEditModal />
            }
        </Box>
    );
};

export default TruongPage;