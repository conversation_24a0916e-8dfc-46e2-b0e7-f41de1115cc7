import {
  KebabHorizontalIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
} from "@primer/octicons-react";
import {
  ActionList,
  ActionMenu,
  Box,
  IconButton,
  useConfirm,
} from "@primer/react";
import { useEffect, useState } from "react";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import HeDetailModal from "./HeDetailModal";
import { IHe } from "../../models/response/he/IHe";
import { heApi } from "../../api/heApi";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
const He = () => {
  const [isShowModal, setIsShowModal] = useState(false);
  const [selectedId, setSelectedId] = useState(0);
  const [Hes, setHes] = useState<IHe[]>([]);
  const { translate } = useCommonContext();
  const { dm_coso_id } = useCommonSelectedHook();
  const confirm = useConfirm();

  useEffect(() => {
    handleReloadAsync();
  }, [dm_coso_id]);

  const handleReloadAsync = async () => {
    const res = await heApi.selectAll();
    if (res.is_success) {
      if (dm_coso_id) {
        const filteredData = res.data.filter((he: IHe) => he.dm_coso_id === dm_coso_id);
        setHes(filteredData);
      } else {
        setHes(res.data);
      }
    } else {
      NotifyHelper.Error(res.message ?? "");
    }
  };

  const handeDelete = async (id: number) => {
    if (
      await confirm({
        content: "Bạn có chắc chắn muốn xóa hệ này?",
        title: "Lưu ý",
        cancelButtonContent: "Không xóa",
        confirmButtonContent: "Xóa hệ",
        confirmButtonType: "danger",
      })
    ) {
      const res = await heApi.delete(id);
      if (res.is_success) {
        NotifyHelper.Success("Success");
        handleReloadAsync();
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    }
  };
  return (
    <Box sx={{ p: 3 }}>
      <DataTable
        height={`${window.innerHeight - 250}px`}
        columns={[
          {
            id: "cmd",
            width: "50px",
            caption: "#",
            align: "center",
            cellRender: (data: IHe) => {
              return (
                <ActionMenu>
                  <ActionMenu.Anchor>
                    <IconButton
                      icon={KebabHorizontalIcon}
                      variant="invisible"
                      aria-label="Open column options"
                    />
                  </ActionMenu.Anchor>

                  <ActionMenu.Overlay>
                    <ActionList>
                      <ActionList.Item
                        onSelect={() => {
                          setIsShowModal(true);
                          if (data.id !== undefined) {
                            setSelectedId(data.id);
                          }
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <PencilIcon />
                        </ActionList.LeadingVisual>
                        <TextTranslated value="Base.Label.Edit" />
                      </ActionList.Item>
                      <ActionList.Divider />
                      <ActionList.Item
                        variant="danger"
                        onSelect={() => {
                          if (data.id !== undefined) {
                            handeDelete(data.id);
                          }
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <TrashIcon />
                        </ActionList.LeadingVisual>
                        <TextTranslated value="Base.Label.Delete" />
                      </ActionList.Item>
                    </ActionList>
                  </ActionMenu.Overlay>
                </ActionMenu>
              );
            },
          },
          {
            dataField: "ma_he",
            width: "auto",
            caption: `Mã hệ`,
            cellRender: (data: IHe) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ma_he}
                </Box>
              );
            },
          },
          {
            dataField: "ten_he",
            width: "auto",
            isMainColumn: true,
            caption: `Tên hệ (Vi)`,
            cellRender: (data: IHe) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_he || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "ten_he_en",
            width: "auto",
            caption: `Tên hệ (En) `,
            cellRender: (data: IHe) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_he_en || "..."}
                </Box>
              );
            },
          }
        ]}
        data={Hes}
        title="Hệ"
        subTitle={`${translate("Base.Label.TotalCount")}: ${Hes.length} ${dm_coso_id ? '(Đã lọc theo cơ sở)' : ''}`}
        searchEnable
        actionComponent={
          <>
            <Button
              text={translate("Base.Label.Create")}
              leadingVisual={PlusIcon}
              variant="primary"
              size="medium"
              onClick={() => {
                setIsShowModal(true);
                setSelectedId(0);
              }}
            />
          </>
        }
      />
      {isShowModal && (
        <HeDetailModal
          id={selectedId}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReloadAsync();
            setIsShowModal(false);
          }}
        />
      )}
    </Box>
  );
};

export default He;
