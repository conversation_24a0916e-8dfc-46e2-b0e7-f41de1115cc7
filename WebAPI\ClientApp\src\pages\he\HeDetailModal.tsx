import { FormControl } from "@primer/react";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { heApi } from "../../api/heApi";
import Button from "../../components/ui/button";
import Modal from "../../components/ui/modal";
import ModalActions from "../../components/ui/modal/ModalActions";
import TextInput from "../../components/ui/text-input";
import { NotifyHelper } from "../../helpers/toast";
import { IHe } from "../../models/response/he/IHe";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";

interface IHeDetailModalProps {
  id: number;
  onClose: () => void;
  onSuccess: () => void;
}

const HeDetailModal = (props: IHeDetailModalProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { dm_coso_id } = useCommonSelectedHook();
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<IHe>({
    defaultValues: {
      id: props.id,
      ma_he: "",
      ten_he: "",
      ten_he_en: "",
      dm_truong_id: 0,
      dm_coso_id: dm_coso_id,
    },
  });

  const handleGetVm = useCallback(async () => {
    const res = await heApi.detail(props.id);
    if (res.is_success) {
      const data: IHe = {
        ...res.data,
        dm_coso_id: dm_coso_id, // Ensure we use the selected cơ sở
      };
      reset(data);
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
  }, [props.id, dm_coso_id, reset]);

  useEffect(() => {
    if (props.id > 0) {
      handleGetVm();
    }
  }, [props.id, handleGetVm]);

  const onSubmit = async (data: any) => {
    setIsSaving(true);
    let res: any;

    const submitData = {
      ...data,
      id: props.id,
      dm_coso_id: dm_coso_id
    };

    if (props.id > 0) {
      res = await heApi.update(submitData);
    } else {
      res = await heApi.insert(submitData);
    }

    if (res.is_success) {
      NotifyHelper.Success("Success");
      props.onSuccess();
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
    setIsSaving(false);
  };

  return (
    <Modal
      isOpen
      onClose={props.onClose}
      // width={"60%"}
      sx={{
        width:"60%"
      }}
      title={props.id > 0 ? "Update" : "Add new"}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="row">
          <div className="col-md-12 mb-3">
            <FormControl>
              <FormControl.Label>Mã hệ</FormControl.Label>
              <TextInput
                block
                name="ma_he"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền mã hệ"
              />
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Tên hệ (Vi)</FormControl.Label>
              <TextInput
                block
                name="ten_he"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền tên hệ (Vi)"
              />
            </FormControl>
          </div>

          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Tên hệ (En)</FormControl.Label>
              <TextInput
                block
                name="ten_he_en"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền tên hệ (En)"
              />
            </FormControl>
          </div>
        </div>

        <ModalActions>
          <Button text="Base.Label.Close" onClick={props.onClose} />
          <Button
            text="Base.Label.Save"
            variant="primary"
            type="submit"
            isLoading={isSaving}
          />
        </ModalActions>
      </form>
    </Modal>
  );
};

export default HeDetailModal;