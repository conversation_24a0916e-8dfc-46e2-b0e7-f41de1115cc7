import { Box } from "@primer/react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { PlaceHolder } from "../../components/place-holder";
import Modal from "../../components/ui/modal";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { crmHocSinhApi } from "./../../api/crmHocSinhApi";
import BaoPhiLayout from "./bao-phi";
import CanBoNhanVienLayout from "./canbo-nhanvien/CanBoNhanVienLayout";
import { DangKyDichVu } from "./dich-vu";
import GhiDanhPage from "./ghi-danh";
import QuanHeGiaDinhLayout from "./gia-dinh/QuanHeGiaDinhLayout";
import { GiayToNopForm } from "./giay-to-nop";
import HinhThucMienGiam from "./hinh-thuc-mien-giam";
import { HocSinhProfileListTab } from "./list-tab";
import { hocSinhProfileTabs } from "./list-tab/HocSinhProfileListTab";
import { HocSinhNameAndAvavtar } from "./name-and-avatar";
import ProfileForm from "./profile-form/ProfileForm";
import SucKhoeHocSinhPage from "./suc-khoe";
import { ThiDauVao } from "../thi-dau-vao";

interface HocSinhProfileProps {
  id: number;
  value?: number;
  height?: number;
  phuHuynh?: any;
  onClose: () => void;
  onSuccess: () => void;
}

interface FormRef {
  handleSaveChanges: () => void;
}

const HocSinhProfile = (props: HocSinhProfileProps) => {
  const { id } = useParams<{ id: string }>();
  const [hocSinh, setHocSinh] = useState<any>({ id: props.id });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState<string>("profile");
  const [profileKey, setProfileKey] = useState(0);
  const componentDidMount = useRef(false);
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
  const profileFormRef = useRef<FormRef | null>(null);

  const handleSave = () => {
    profileFormRef.current?.handleSaveChanges();
  };

  // Effect để xử lý initial hocSinh ID
  useEffect(() => {
    if (props.value !== undefined && props.value >= 0) {
      setHocSinh({ id: props.value });
    } else {
      setHocSinh({ id: parseInt(id || "0", 10) });
    }
  }, [props.value, id]);

  // Effect cho việc load data và mount component
  useEffect(() => {
    componentDidMount.current = true;

    const initialLoad = async () => {
      if (hocSinh.id > 0) {
        await handleGetHocSinhData();
        setProfileKey(prev => prev + 1);
      }
    };

    initialLoad();

    return () => {
      componentDidMount.current = false;
    };
  }, [hocSinh.id]);

  const handleGetHocSinhData = async () => {
    setIsLoading(true);
    try {
      const res = await crmHocSinhApi.getById(hocSinh.id, nam_hoc);
      if (!componentDidMount.current) return;
      if (res.is_success) {
        setHocSinh(res.data);
      }
    } catch (error) {
      console.error("Error fetching student data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = async (tab: string) => {
    setSelectedTab(tab);
    if (tab === "profile" && hocSinh.id > 0) {
      await handleGetHocSinhData();
      setProfileKey(prev => prev + 1);
    }
  };

  const hocSinhProfileTab = useMemo(() => {
    return hocSinhProfileTabs.find((x) => x.id === selectedTab);
  }, [selectedTab]);

  return (
    <Modal
      isOpen
      onClose={props.onClose}
      // width={"90%"}
      sx={{
        width:"90%"
      }}
      title={"Thông tin học sinh"}
    >
      <Box sx={{
        p: 1,
        overflow: "hidden"
      }}>
        <Box
          className="top-side"
          mb={0}
          pb={1}
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
        >
          {isLoading && <PlaceHolder line_number={2} />}
          {!isLoading && (
            <Box
              display="flex"
              flexDirection="row"
              alignItems="center"
              flexGrow={1}
            >
              <HocSinhNameAndAvavtar key={hocSinh.id} hocSinh={hocSinh} />
              <Box flexGrow={1}>
                <HocSinhProfileListTab
                  selectedTab={selectedTab}
                  hocSinhId={hocSinh.id}
                  onTabSelectionChanged={handleTabChange}
                />
              </Box>
            </Box>
          )}
        </Box>

        <Box
          className="bottom-side"
          overflow="auto"
          height={props.height ?? window.innerHeight - 300}
        >
          <Box className="content">
            <Box p={2}>
              {selectedTab === "profile" && (
                <ProfileForm
                  ref={profileFormRef}
                  key={`${hocSinh.id}-${profileKey}`} // Thêm profileKey vào key
                  hocSinh={hocSinh}
                  onSubmitSuccess={(id: any) => {
                    if (hocSinh.id > 0) {
                      handleGetHocSinhData();
                    } else {
                      setHocSinh({ id });
                    }
                    props.onSuccess();
                  }}
                />
              )}
              {selectedTab === "familly" && (
                <QuanHeGiaDinhLayout hocSinh={hocSinh} />
              )}
              {selectedTab === "cbnv" && (
                <CanBoNhanVienLayout hocSinh={hocSinh} />
              )}
              {selectedTab === "thidauvao" && (
                <ThiDauVao hocSinh={hocSinh} />
              )}

              {selectedTab === "health" && (
                <SucKhoeHocSinhPage ts_hocsinh_id={hocSinh.id} />
              )}
              {selectedTab === "discount" && (
                <HinhThucMienGiam hocSinh={hocSinh} />
              )}
              {selectedTab === "service" && (
                <DangKyDichVu hocSinh={hocSinh} />
              )}
              {selectedTab === "registration" && (
                <GhiDanhPage hocSinh={hocSinh} />
              )}
              {selectedTab === "debit_note" && (
                <BaoPhiLayout hocSinh={hocSinh} />
              )}
              {selectedTab === "saleset" && (
                <DangKyDichVu hocSinh={hocSinh} />
              )}
              {selectedTab === "document" && (
                <GiayToNopForm
                  hocSinh={hocSinh}
                  onUploadedAvatar={handleGetHocSinhData}
                />
              )}
            </Box>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
}
export default HocSinhProfile;