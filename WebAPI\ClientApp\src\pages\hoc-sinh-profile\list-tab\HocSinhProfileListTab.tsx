import { CopilotWarningIcon, FileDirectoryIcon, GiftIcon, HeartFillIcon, IdBadgeIcon, LogIcon, PackageIcon, PeopleIcon, TasklistIcon, TrophyIcon } from "@primer/octicons-react";
import { Box, UnderlineNav } from "@primer/react";
import React from 'react';

type OcticonType = typeof IdBadgeIcon;

interface HocSinhProfileTabProps {
  id: string;
  name: string;
  icon: OcticonType;
  description: string;
}

interface HocSinhProfileListTabProps {
  selectedTab: string;
  onTabSelectionChanged: (id: string) => void;
  hocSinhId: number;
}

export const hocSinhProfileTabs: HocSinhProfileTabProps[] = [
  { id: "profile", name: "<PERSON><PERSON> sơ học sinh", icon: IdBadgeIcon, description: "Thông tin đăng ký, địa chỉ, người liên hệ , ..." },
  { id: "familly", name: "<PERSON><PERSON> hệ gia đình", icon: PeopleIcon, description: "Thông tin bố, mẹ, ng<PERSON><PERSON>i gi<PERSON>m hộ, ..." },
   { id: "cbnv", name: "<PERSON><PERSON> hệ CBNV", icon: CopilotWarningIcon  , description: "Thông tin con em giáo viên, bộ đội, công an, ..." },
      { id: "thidauvao", name: "Thi đầu vào", icon: TrophyIcon   , description: "Thông tin thi đầu vào" },

   { id: "document", name: "Hồ sơ, giấy tờ", icon: FileDirectoryIcon, description: "Thông tin các giấy tờ, hồ sơ đã nộp" },
   { id: "health", name: "Thông tin sức khỏe", icon: HeartFillIcon, description: "Thông tin liên quan tiểu sử bệnh tật" },
 { id: "discount", name: "Ưu đãi", icon: GiftIcon, description: "Chọn thông tin liên quan tới ưu đãi" },
  { id: "saleset", name: "Dịch vụ", icon: PackageIcon , description: "Chọn thông tin liên quan tới dịch vụ" },
   { id: "registration", name: "Ghi danh", icon: TasklistIcon, description: "" },

   { id: "debit_note", name: "Báo phí", icon: LogIcon, description: "" },


];

const HocSinhProfileListTab: React.FC<HocSinhProfileListTabProps> = ({ selectedTab, onTabSelectionChanged, hocSinhId }) => {
  return (
    <Box>
      <UnderlineNav aria-label="tabs">
        {hocSinhProfileTabs
          .filter((tab) => hocSinhId > 0 || tab.id === "profile")
          .map((tab) => (

            <UnderlineNav.Item
              key={tab.id}
              icon={tab.icon}
              aria-current={tab.id === selectedTab ? "page" : undefined}
              onClick={() => onTabSelectionChanged(tab.id)}
            >
              {tab.name}
            </UnderlineNav.Item>
          ))}
      </UnderlineNav>
    </Box>
  );
};

export default HocSinhProfileListTab;