import { CalendarIcon, InfoIcon } from "@primer/octicons-react";
import { Box, FormControl } from "@primer/react";
import { forwardRef, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { crmHocSinhApi } from "../../../api/crmHocSinhApi";
import ComboboxHe from "../../../components/combobox-he";
import ComboboxKhoi from "../../../components/combobox-khoi";
import ComboboxLop from "../../../components/combobox-lop";
import ComboboxDanToc from "../../../components/combobox-dantoc";
import ComboboxGioiTinh from "../../../components/combobox-gioitinh";
import ComboboxHuyen from "../../../components/combobox-huyen";
import { ComboboxNamHoc } from "../../../components/combobox-namhoc";
import { ComboboxNguonBietToi } from "../../../components/combobox-nguonbiettoi";
import ComboboxQuanHe from "../../../components/combobox-quanhe/ComboboxQuanHe";
import ComboboxQuocTich from "../../../components/combobox-quoctich";
import ComboboxTinh from "../../../components/combobox-tinh";
import ComboboxTonGiao from "../../../components/combobox-tongiao";
import { ComboboxTruong } from "../../../components/combobox-truong";
import ComboboxXa from "../../../components/combobox-xa";
import { FormGroup } from "../../../components/form-group";
import MyButton from "../../../components/ui/button/Button";
import TextInput from "../../../components/ui/text-input";
import { useCommonContext } from "../../../contexts/common";
import { NotifyHelper } from "../../../helpers/toast";
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";
import { HocSinhFormSubmitRequest } from "../../../models/request/hoc-sinh/HocSinhFormSubmitRequest";
import { IHocSinh } from "../../../models/response/crm-hocsinh/IHocsinh";
import { ComboboxTrangThaiHocSinh } from "../../../components/combobox-trangthaihocsinh";
import StyledDatePicker from "../../../components/react-datepicker-style/ReactDatePicker";

interface PhuHuynh {
  ho_ten?: string;
  dien_thoai?: string;
  email?: string;
  dm_quanhe_id?: number;
}

interface ProfileFormProps {
  hocSinh: IHocSinh;
  phuHuynh?: PhuHuynh;
  onSubmitSuccess: (id: number) => void;
  height?: number;
}

interface FormRef {
  handleSaveChanges: () => void;
}


const ProfileForm = forwardRef<FormRef, ProfileFormProps>(
  ({ hocSinh, onSubmitSuccess, phuHuynh, height }, ref) => {
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
    const { translate } = useCommonContext();
    const [selectedProvince, setSelectedProvince] = useState<any>(
      hocSinh.dm_tinh_id || null
    );
    const [selectedDistrict, setSelectedDistrict] = useState<any>(
      hocSinh.dm_huyen_id || null
    );
    const [selectedCommune, setSelectedCommune] = useState<any>(
      hocSinh.dm_xa_id || null
    );

    const [isSaving, setIsSaving] = useState(false);
    const [formData, setFormData] = useState<IHocSinh>({ ...hocSinh });
    const {
      register,
      handleSubmit,
      control,
      trigger,
      setValue,
      formState: { errors },
    } = useForm<IHocSinh>({ mode: "all", defaultValues: formData });

    useEffect(() => {
      if (hocSinh) {
        let _data = {
          ...hocSinh,
          nam_hoc: nam_hoc,
          dm_coso_id: dm_coso_id,
          ngay_sinh: hocSinh.ngay_sinh ? new Date(hocSinh.ngay_sinh) : null,
          ngay_nhap_hoc: hocSinh.ngay_nhap_hoc ? new Date(hocSinh.ngay_nhap_hoc) : null,
          ngaythi_dauvao_dukien: hocSinh.ngaythi_dauvao_dukien ? new Date(hocSinh.ngaythi_dauvao_dukien) : null,
        };
        setFormData(_data);

        // Update form values for the comboboxes
        if (hocSinh.dm_truong_id) {
          console.log('ProfileForm - Setting dm_truong_id:', hocSinh.dm_truong_id);
          setValue("dm_truong_id", hocSinh.dm_truong_id);
        }
        if (hocSinh.dm_khoi_id) {
          setValue("dm_khoi_id", hocSinh.dm_khoi_id);
        }
        if (hocSinh.dm_he_id) {
          setValue("dm_he_id", hocSinh.dm_he_id);
        }
        if (hocSinh.dm_lop_id) {
          setValue("dm_lop_id", hocSinh.dm_lop_id);
        }
      }
    }, [hocSinh, nam_hoc, dm_coso_id, setValue]);

    const handleProvinceChange = (value: any) => {
      setSelectedProvince(value);
      setSelectedDistrict(null);
      setSelectedCommune(null);
      setFormData((prev) => ({
        ...prev,
        dm_tinh_id: value,
        dm_huyen_id: 0,
        dm_xa_id: 0,
      }));
    };
    const handleDistrictChange = (value: any) => {
      setSelectedDistrict(value);
      setSelectedCommune(null);
      setFormData((prev) => ({
        ...prev,
        dm_huyen_id: value,
        dm_xa_id: 0,
      }));
    };
    const handleCommuneChange = (value: any) => {
      setSelectedCommune(value);
      setFormData((prev) => ({
        ...prev,
        dm_xa_id: value,
      }));
    };

    const handleKhoiChange = (value: number) => {
      setFormData((prev) => ({
        ...prev,
        dm_khoi_id: value,
        dm_he_id: 0,
        dm_lop_id: 0,
      }));
      setValue("dm_he_id", 0);
      setValue("dm_lop_id", 0);
    };


    const handleSaveChanges = async (data: any) => {
      setIsSaving(true);
      const formDataSubmit: HocSinhFormSubmitRequest = {
        id: data.id,
        ma_hs: data.ma_hs,
        nam_hoc: data.nam_hoc ?? nam_hoc,
        ho_ten: data.ho_ten ?? "",
        ho_ten_en: data.ho_ten_en ?? "",
        ngay_sinh_timestamp: data.ngay_sinh
          ? new Date(data.ngay_sinh).getTime()
          : 0,
        dm_gioitinh_id: data.dm_gioitinh_id,
        dia_chi_noi_sinh: data.dia_chi_noi_sinh ?? "",
        truong_hoc_cu: data.truong_hoc_cu ?? "",
        ho_ten_lien_he: data.ho_ten_lien_he ?? "",
        dien_thoai_lien_he: data.dien_thoai_lien_he ?? "",
        email_lien_he: data.email_lien_he ?? "",
        dm_chiendich_id: data.dm_chiendich_id,
        dm_quanhe_id: data.dm_quanhe_id,
        dm_nguonbiettoi_id: data.dm_nguonbiettoi_id,
        dm_tinh_id: data.dm_tinh_id,
        dm_huyen_id: data.dm_huyen_id,
        dm_xa_id: data.dm_xa_id,
        dia_chi_tt: data.dia_chi_tt ?? "",
        dm_truong_id: data.dm_truong_id,
        dm_khoi_id: data.dm_khoi_id || formData.dm_khoi_id,
        dm_lop_id: data.dm_lop_id || formData.dm_lop_id,
        dm_he_id: data.dm_he_id,
        dm_quoctich_id: data.dm_quoctich_id,
        dm_coso_id: data.dm_coso_id ?? dm_coso_id,
        ngaythi_dauvao_dukien: data.ngaythi_dauvao_dukien ?? undefined,
        ngay_nhap_hoc: data.ngay_nhap_hoc ?? null,
        que_quan: data.que_quan ?? "",
        nam_nhap_hoc: data.nam_nhap_hoc ?? "",
        email_hocsinh: data.email_hocsinh ?? "",
        dien_thoai_hocsinh: data.dien_thoai_hocsinh ?? "",
        ma_so_bhxh: data.ma_so_bhxh ?? "",
        loai_dang_ky_bhxh: data.loai_dang_ky_bhxh ?? "",
        so_tien_bhxh: data.so_tien_bhxh ?? 0,
        noi_dang_ky_kcb_bandau: data.noi_dang_ky_kcb_bandau ?? "",
        doi_tuong_chinh_sach: data.doi_tuong_chinh_sach ?? "",
        ma_cua_bo: data.ma_cua_bo ?? "",
        chung_minh_thu_nguoilienhe: "",
        noi_cap_chung_minh_thu_nguoilienhe: "",
        ngay_cap_chung_minh_thu_nguoilienhe: undefined,
        ngay_sinh_nguoilienhe: undefined,
        quoc_tich_id_nguoilienhe: data.dm_quoctich_id,
        nghe_nghiep_nguoilienhe: "",
        noi_lam_viec_nguoilienhe: "",
        dia_chi_thuong_tru_nguoilienhe: "",
        dm_dantoc_id: data.dm_dantoc_id,
        dm_tongiao_id: data.dm_tongiao_id,
        ma_dinh_danh: data.ma_dinh_danh ?? "",
        tt_suc_khoe: data.tt_suc_khoe ?? "",
        dia_chi_hientai: data.dia_chi_hientai ?? "",
        chieu_cao: data.chieu_cao ?? "",
        can_nang: data.can_nang ?? "",
        so_dien_thoai_bo: "",
        so_dien_thoai_me: "",
        ho_chieu: data.ho_chieu ?? "",
        ngay_ketthuc_hoc: data.ngay_ketthuc_hoc ?? undefined,
        ma_dinh_danh_csdl: data.ma_dinh_danh_csdl ?? "",
        ma_so_bhyt: data.ma_so_bhyt ?? "",
        ghi_chu_chung: data.ghi_chu_chung ?? '',

        dm_trangthaihocsinh_id:data.dm_trangthaihocsinh_id ?? 0,
        is_nhucau_noitru: data.is_nhucau_noitru ?? false,
        laconthu:data.laconthu ?? 1

      };
      console.log("formDataSubmit:", formDataSubmit);

      const isValid = await trigger();
      if (!isValid) {
        setIsSaving(false);
        return;
      }

      let res;
      if (formData.id > 0) {
        // Cập nhật học sinh
        if (formData.ma_hs !== "") {
          const resHS = await crmHocSinhApi.selectByMaHs(
            formData.ma_hs,
            dm_coso_id
          );
          if (resHS.is_success && resHS.data && formData.id !== resHS.data.id) {
            NotifyHelper.Error("Mã học sinh này đã tồn tại");
            setIsSaving(false);
            return;
          }
        }
        res = await crmHocSinhApi.update(formDataSubmit);
      } else {
        // Thêm mới học sinh
        const resHS = await crmHocSinhApi.selectByMaHs(
          formData.ma_hs,
          dm_coso_id
        );
        if (resHS.is_success && resHS.data) {
          NotifyHelper.Error("Mã học sinh này đã tồn tại");
          setIsSaving(false);
          return;
        }
        debugger;
        res = await crmHocSinhApi.insert(formDataSubmit);
      }

      if (res.is_success) {
        NotifyHelper.Success(
          formData.id > 0 ? "Cập nhật thành công" : "Thêm mới thành công"
        );
        onSubmitSuccess(res.data);
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
      setIsSaving(false);
    };

    return (
      <div>
        <div
          style={{
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <form onSubmit={handleSubmit(handleSaveChanges)}>
            <div className="row">
              <div
                className="col-md-12"
                style={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #ececec",
                  paddingTop: "5px",
                  fontSize: "17px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    color: "#133E87",
                  }}
                >
                  <InfoIcon size={14} />
                  <p style={{ color: "#050C9C", margin: 0, marginLeft: "5px" }}>
                    {translate("THÔNG TIN HỌC SINH")}
                  </p>
                </div>
              </div>
              <div className="col-md-12" style={{ paddingTop: "10px" }}>
                <div className="row">
                  <div className="col-md-4">
                    <FormGroup label={"Năm học"}>
                      <Controller
                        name="nam_hoc"
                        control={control as any} // hoặc khai báo kiểu cụ thể
                        rules={{ required: "Vui lòng chọn năm học" }}
                        render={({ field }) => (
                          <ComboboxNamHoc
                            value={field.value}
                            onValueChanged={async (value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                nam_hoc: value,
                              }));

                              // Kiểm tra có phải đang edit không
                              if (hocSinh.id > 0) {
                                try {
                                  const res = await crmHocSinhApi.getHSCCById(
                                    hocSinh.id,
                                    value
                                  );
                                  if (res.is_success && res.data) {
                                    // Cập nhật các giá trị từ API
                                    setFormData((prev) => ({
                                      ...prev,
                                      dm_truong_id: res.data.dm_truong_id || 0,
                                      dm_khoi_id: res.data.dm_khoi_id || 0,
                                      dm_he_id: res.data.dm_he_id || 0,
                                      dm_lop_id: res.data.dm_lop_id || 0,
                                      dm_trangthaihocsinh_id:
                                        res.data.dm_trangthaihocsinh_id || 0,
                                    }));

                                    const defaultValues = {
                                      dm_truong_id: res.data.dm_truong_id || 0,
                                      dm_khoi_id: res.data.dm_khoi_id || 0,
                                      dm_he_id: res.data.dm_he_id || 0,
                                      dm_lop_id: res.data.dm_lop_id || 0,
                                      dm_trangthaihocsinh_id:
                                        res.data.dm_trangthaihocsinh_id || 0,
                                    };

                                    Object.entries(defaultValues).forEach(
                                      ([key, value]) => {
                                        setValue(key as keyof IHocSinh, value);
                                      }
                                    );
                                  } else {
                                    setFormData((prev) => ({
                                      ...prev,
                                      dm_truong_id: 0,
                                      dm_khoi_id: 0,
                                      dm_he_id: 0,
                                      dm_lop_id: 0,
                                      dm_trangthaihocsinh_id: 0,
                                    }));

                                    // Reset form values
                                    const defaultValues = {
                                      dm_truong_id: 0,
                                      dm_khoi_id: 0,
                                      dm_he_id: 0,
                                      dm_lop_id: 0,
                                      dm_trangthaihocsinh_id: 0,
                                    };

                                    Object.entries(defaultValues).forEach(
                                      ([key, value]) => {
                                        setValue(key as keyof IHocSinh, value);
                                      }
                                    );
                                  }
                                } catch (error) {
                                  console.error("Error fetching data:", error);
                                }
                              }
                            }}
                          />
                        )}
                      />
                    </FormGroup>
                  </div>
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Mã học sinh</FormControl.Label>
                      <TextInput
                        block
                        name="ma_hs"
                        register={register}
                        errors={errors}
                        required={false}
                        validateMessage="Vui lòng điền mã học sinh"
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Trạng thái học sinh</FormControl.Label>
                      <Controller
                        name="dm_trangthaihocsinh_id"
                        control={control}
                        rules={{ required: "Vui lòng chọn trạng thái học sinh" }}
                        render={({ field }) => (
                          <ComboboxTrangThaiHocSinh
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_trangthaihocsinh_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {errors.dm_trangthaihocsinh_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_trangthaihocsinh_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Họ tên (Vi)</FormControl.Label>
                      <TextInput
                        block
                        name="ho_ten"
                        register={register}
                        errors={errors}
                        required
                        validateMessage="Vui lòng điền họ tên tiếng Việt"
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Họ tên (En)</FormControl.Label>
                      <TextInput
                        block
                        name="ho_ten_en"
                        register={register}
                        errors={errors}
                        required
                        validateMessage="Vui lòng điền họ tên tiếng Anh"
                      />
                    </FormControl>
                  </div>

                  <div className="col-md-4 mb-2">
  <FormControl>
    <FormControl.Label>Ngày sinh</FormControl.Label>
    <Controller
      name="ngay_sinh"
      control={control}
      rules={{ required: "Vui lòng điền ngày sinh" }}
      render={({ field }) => (
        <StyledDatePicker
          selected={field.value ? new Date(field.value) : null}
          onChange={(date: Date | null) => {
            field.onChange(date);
            setFormData((prev) => ({
              ...prev,
              ngay_sinh: date,
            }));
          }}
          error={errors.ngay_sinh?.message}
        />
      )}
    />
  </FormControl>
</div>
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Giới tính</FormControl.Label>
                      <Controller
                        name="dm_gioitinh_id"
                        control={control}
                        rules={{ required: "Vui lòng chọn giới tính" }}
                        render={({ field }) => (
                          <ComboboxGioiTinh
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_gioitinh_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {errors.dm_gioitinh_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_gioitinh_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Dân tộc</FormControl.Label>
                      <Controller
                        name="dm_dantoc_id"
                        control={control}
                        rules={{ required: "Vui lòng chọn dân tộc" }}
                        render={({ field }) => (
                          <ComboboxDanToc
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_dantoc_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {errors.dm_dantoc_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_dantoc_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                    {/* <FormGroup label={"Dân tộc"}>
                      <ComboboxDanToc
                        value={formData.dm_dantoc_id}
                        onValueChanged={(value) => {
                          // setFormData({ ...formData, nam_hoc: value });
                          setFormData((prev) => ({
                            ...prev,
                            dm_dantoc_id: value,
                          }));
                          //trigger("dm_dantoc_id");
                        }}
                      />
                    </FormGroup> */}
                  </div>
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Tôn giáo</FormControl.Label>
                      <Controller
                        name="dm_tongiao_id"
                        control={control}
                        rules={{ required: "Vui lòng chọn tôn giáo" }}
                        render={({ field }) => (
                          <ComboboxTonGiao
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_tongiao_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {errors.dm_tongiao_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_tongiao_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Quốc tịch</FormControl.Label>
                      <Controller
                        name="dm_quoctich_id"
                        control={control}
                        rules={{ required: "Vui lòng chọn quốc tịch" }}
                        render={({ field }) => (
                          <ComboboxQuocTich
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_quoctich_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {errors.dm_quoctich_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_quoctich_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>

                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Năm nhập học</FormControl.Label>
                      <Controller
                        name="nam_nhap_hoc"
                        control={control}
                        rules={{
                          required: "Vui lòng chọn năm nhập học",
                          validate: (value) => {
                            if (!value || !formData.nam_hoc) return true;
                            const namHoc = parseInt(
                              formData.nam_hoc?.split(" - ")[0]
                            );
                            const namNhapHoc = parseInt(value?.split(" - ")[0]);
                            return (
                              namHoc >= namNhapHoc ||
                              "Năm nhập học phải nhỏ hơn hoặc bằng năm học"
                            );
                          },
                        }}
                        render={({ field }) => (
                          <ComboboxNamHoc
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                nam_nhap_hoc: value,
                              }));
                              trigger("nam_nhap_hoc");
                            }}
                          />
                        )}
                      />
                      {errors.nam_nhap_hoc && (
                        <FormControl.Validation variant="error">
                          {errors.nam_nhap_hoc.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>
                  <div className="col-md-2">
  <FormControl>
    <FormControl.Label>Ngày nhập học</FormControl.Label>
    <Controller
      name="ngay_nhap_hoc"
      control={control}
      render={({ field }) => (
        <StyledDatePicker
          selected={field.value ? new Date(field.value) : null}
          onChange={(date: Date | null) => {
            field.onChange(date);
            setFormData((prev) => ({
              ...prev,
              ngay_nhap_hoc: date,
            }));
          }}
          error={errors.ngay_nhap_hoc?.message}
        />
      )}
    />
  </FormControl>
</div>
                  <div className="col-md-2">
                    <FormControl>
                      <FormControl.Label>Là con thứ</FormControl.Label>
                      <TextInput
                        block
                        type="number"
                        name="laconthu"
                        register={register}
                        errors={errors}
                        required
                        validateMessage="Vui lòng điền thông tin"
                      />
                    </FormControl>
                  </div>
                  {/* <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Ngày kết thúc học</FormControl.Label>
                      <TextInput
                        block
                        type="date"
                        value={
                          formData.ngay_ketthuc_hoc instanceof Date
                            ? formData.ngay_ketthuc_hoc.toISOString().split("T")[0]
                            : "" // Nếu không phải là Date, trả về chuỗi rỗng
                        }
                        onChange={(e) => {
                          const value = e.target.value;
                          setFormData((prev) => ({
                            ...prev,
                            ngay_ketthuc_hoc: value ? new Date(value) : null, // Chuyển đổi giá trị sang Date
                          }));
                          //  trigger("ngay_nhap_hoc");
                        }}
                        name="ngay_ketthuc_hoc"
                        register={register}
                        errors={errors}
                        required={false}
                        validateMessage="Vui lòng điền ngày nhập học"
                      />
                    </FormControl>
                  </div> */}
                </div>
              </div>
              <div
                className="col-md-12"
                style={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #ececec",
                  paddingTop: "15px",
                  fontSize: "17px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    color: "#133E87",
                  }}
                >
                  <InfoIcon size={14} />
                  <p style={{ color: "#050C9C", margin: 0, marginLeft: "5px" }}>
                    {translate("THÔNG TIN ĐĂNG KÝ")}
                  </p>
                </div>
              </div>

              <div className="col-md-12" style={{ paddingTop: "10px" }}>
                <div className="row">
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Chọn cấp học</FormControl.Label>
                      <Controller
                        name="dm_truong_id"
                        control={control}
                        rules={{ required: "Vui lòng Chọn cấp học" }}
                        render={({ field }) => (
                          <ComboboxTruong
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_truong_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {errors.dm_truong_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_truong_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Chọn khối</FormControl.Label>
                      <Controller
                        name="dm_khoi_id"
                        control={control}
                        rules={{ required: "Vui lòng chọn khối" }}
                        render={({ field }) => (
                          <ComboboxKhoi
                            dm_truong_id={formData.dm_truong_id}
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              handleKhoiChange(value);
                            }}
                          />
                        )}
                      />
                      {errors.dm_khoi_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_khoi_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Chọn hệ</FormControl.Label>
                      <Controller
                        name="dm_he_id"
                        control={control}
                        rules={{ required: "Vui lòng chọn hệ" }}
                        render={({ field }) => (
                          <ComboboxHe
                            value={field.value}
                            dm_khoi_id={formData.dm_khoi_id}
                            dm_truong_id={formData.dm_truong_id}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_he_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {errors.dm_he_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_he_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Chọn lớp</FormControl.Label>
                      <Controller
                        name="dm_lop_id"
                        control={control}
                        rules={{ required: "Vui lòng chọn lớp" }}
                        render={({ field }) => (
                          <ComboboxLop
                            dm_truong_id={formData.dm_truong_id}
                            nam_hoc={formData.nam_hoc}
                            dm_khoi_id={formData.dm_khoi_id}
                            dm_he_id={formData.dm_he_id}
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_lop_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {errors.dm_lop_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_lop_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>

                  {/* <div className="col-md-4">
                <FormControl>
                  <FormControl.Label>
                    Ngày thi đầu vào dự kiến
                  </FormControl.Label>
                  <TextInput
                    block
                    type="date"
                    value={
                      formData.ngaythi_dauvao_dukien instanceof Date
                        ? formData.ngaythi_dauvao_dukien
                            .toISOString()
                            .split("T")[0]
                        : "" // Nếu không phải là Date, trả về chuỗi rỗng
                    }
                    onChange={(e) => {
                      const value = e.target.value;
                      setFormData((prev) => ({
                        ...prev,
                        ngaythi_dauvao_dukien: value ? new Date(value) : null, // Chuyển đổi giá trị sang Date
                      }));
                      trigger("ngaythi_dauvao_dukien");
                    }}
                  />
                </FormControl>
              </div> */}
                </div>
              </div>
              <div
                className="col-md-12"
                style={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #ececec",
                  paddingTop: "12px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    color: "#133E87",
                    fontSize: "17px",
                  }}
                >
                  <InfoIcon size={14} />
                  <p style={{ color: "#050C9C", margin: 0, marginLeft: "5px" }}>
                    {translate("ĐỊA CHỈ HỌC SINH")}
                  </p>
                </div>
              </div>

              <div className="col-md-12" style={{ paddingTop: "10px" }}>
                <div className="row">
                  <div className="col-md-4">
                    <FormGroup label={"Tỉnh thường trú"}>
                      <ComboboxTinh
                        value={selectedProvince}
                        onValueChanged={handleProvinceChange}
                      />
                    </FormGroup>
                  </div>
                  <div className="col-md-4">
                    <FormGroup label={"Huyện thường trú"}>
                      <ComboboxHuyen
                        dm_tinh_id={selectedProvince}
                        value={selectedDistrict}
                        onValueChanged={handleDistrictChange}
                        key={`district-${selectedProvince}`}
                      />
                    </FormGroup>
                  </div>
                  <div className="col-md-4">
                    <FormGroup label={"Xã thường trú"}>
                      <ComboboxXa
                        dm_huyen_id={selectedDistrict}
                        value={selectedCommune}
                        onValueChanged={handleCommuneChange}
                        key={`commune-${selectedProvince}-${selectedDistrict}`}
                      />
                    </FormGroup>
                  </div>
                  <div className="col-md-12 mb-2">
                    <FormControl>
                      <FormControl.Label>Nơi ở hiện tại</FormControl.Label>
                      <TextInput
                        block
                        name="dia_chi_hientai"
                        register={register}
                        errors={errors}
                        required={false}
                        validateMessage="Vui lòng điền địa chỉ thường trú"
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-12 mb-2">
                    <FormControl>
                      <FormControl.Label>Địa chỉ thường trú</FormControl.Label>
                      <TextInput
                        block
                        name="dia_chi_tt"
                        register={register}
                        errors={errors}
                        required={false}
                        validateMessage="Vui lòng điền địa chỉ thường trú"
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-12 mb-2">
                    <FormControl>
                      <FormControl.Label>Nơi sinh</FormControl.Label>
                      <TextInput
                        block
                        name="dia_chi_noi_sinh"
                        register={register}
                        errors={errors}
                        required={false}
                        validateMessage="Vui lòng điền nơi sinh"
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-12">
                    <FormControl>
                      <FormControl.Label>Quê quán</FormControl.Label>
                      <TextInput
                        block
                        name="que_quan"
                        register={register}
                        errors={errors}
                        required={false}
                        validateMessage="Vui lòng điền quê quán"
                      />
                    </FormControl>
                  </div>
                </div>
              </div>
              <div
                className="col-md-12"
                style={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #ececec",
                  paddingTop: "15px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    color: "#133E87",
                    fontSize: "17px",
                  }}
                >
                  <InfoIcon size={14} />
                  <p style={{ color: "#050C9C", margin: 0, marginLeft: "5px" }}>
                    {translate("THÔNG TIN KHÁC")}
                  </p>
                </div>
              </div>

              <div className="col-md-12" style={{ paddingTop: "10px" }}>
                <div className="row">
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Mã định danh cá nhân</FormControl.Label>
                      <TextInput
                        block
                        name="ma_dinh_danh"
                        register={register}
                        errors={errors}
                        required={false}
                        validateMessage="Vui lòng điền mã định danh"
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Mã định danh CSDL</FormControl.Label>
                      <TextInput
                        block
                        name="ma_dinh_danh_csdl"
                        register={register}
                        errors={errors}
                        required={false}
                        validateMessage="Vui lòng điền mã định danh CSDL"
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Nguồn biết tới</FormControl.Label>
                      <Controller
                        name="dm_nguonbiettoi_id"
                        control={control}
                        rules={{ required: "Vui lòng chọn nguồn biết tới" }}
                        render={({ field }) => (
                          <ComboboxNguonBietToi
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_nguonbiettoi_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {errors.dm_nguonbiettoi_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_nguonbiettoi_id.message}
                        </FormControl.Validation>
                      )}
                    </FormControl>
                  </div>
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Mã số BHYT</FormControl.Label>
                      <TextInput
                        block
                        name="ma_so_bhyt"
                        register={register}
                        errors={errors}
                        required={false}
                        validateMessage="Vui lòng điền trường học cũ"
                      />
                    </FormControl>
                  </div>

                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Trường cũ</FormControl.Label>
                      <TextInput
                        block
                        name="truong_hoc_cu"
                        register={register}
                        errors={errors}
                        required={false}
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Ghi chú chung</FormControl.Label>
                      <TextInput
                        block
                        name="ghi_chu_chung"
                        register={register}
                        errors={errors}
                        required={false}
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-4">
  <FormControl>
    <div className="d-flex align-items-center mt-3">
      <input
        type="checkbox"
        id="nhucau-noitru-checkbox"
        {...register('is_nhucau_noitru')}
        checked={formData.is_nhucau_noitru || false}
        onChange={(e) => {
          setValue('is_nhucau_noitru', e.target.checked);
          setFormData(prev => ({
            ...prev,
            is_nhucau_noitru: e.target.checked
          }));
        }}
      />
      <FormControl.Label
        htmlFor="nhucau-noitru-checkbox"
      >
        {translate("Có nhu cầu nội trú")}
      </FormControl.Label>
    </div>
  </FormControl>
</div>
                </div>
              </div>

              <div
                className="col-md-12"
                style={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #ececec",
                  paddingTop: "15px",
                  fontSize: "17px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    color: "#133E87",
                  }}
                >
                  <InfoIcon size={14} />
                  <p style={{ color: "#050C9C", margin: 0, marginLeft: "5px" }}>
                    {translate("THÔNG TIN LIÊN HỆ")}
                  </p>
                </div>
              </div>

              <div className="col-md-12" style={{ paddingTop: "10px" }}>
                <div className="row">
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Quan hệ</FormControl.Label>
                      <Controller
                        name="dm_quanhe_id"
                        control={control}
                        //rules={{ required: "Vui lòng chọn quan hệ" }}
                        render={({ field }) => (
                          <ComboboxQuanHe
                            value={field.value}
                            onValueChanged={(value) => {
                              field.onChange(value);
                              setFormData((prev) => ({
                                ...prev,
                                dm_quanhe_id: value,
                              }));
                            }}
                          />
                        )}
                      />
                      {/* {errors.dm_quanhe_id && (
                        <FormControl.Validation variant="error">
                          {errors.dm_quanhe_id.message}
                        </FormControl.Validation>
                      )} */}
                    </FormControl>
                  </div>
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Họ tên</FormControl.Label>
                      <TextInput
                        block
                        name="ho_ten_lien_he"
                        register={register}
                        errors={errors}
                        //required
                        //validateMessage="Vui lòng điền họ tên người liên hệ"
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-4">
                    <FormControl>
                      <FormControl.Label>Điện thoại</FormControl.Label>
                      <TextInput
                        block
                        name="dien_thoai_lien_he"
                        register={register}
                        errors={errors}
                        //required
                        //validateMessage="Vui lòng điền điện thoại người liên hệ"
                      />
                    </FormControl>
                  </div>
                  <div className="col-md-4 mb-2">
                    <FormControl>
                      <FormControl.Label>Email</FormControl.Label>
                      <TextInput
                        block
                        name="email_lien_he"
                        register={register}
                        errors={errors}
                        //required
                        //validateMessage="Vui lòng điền email người liên hệ"
                      />
                    </FormControl>
                  </div>
                </div>
                <div
                  className="col-md-12 fixed-bottom"
                  style={{
                    paddingTop: "10px",
                    display: "flex",
                    justifyContent: "center",
                  }}
                >
                  <MyButton
                    text="Base.Label.Save"
                    variant="primary"
                    type="submit"
                    isLoading={isSaving}
                  />
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    );
  }
);

export default ProfileForm;
