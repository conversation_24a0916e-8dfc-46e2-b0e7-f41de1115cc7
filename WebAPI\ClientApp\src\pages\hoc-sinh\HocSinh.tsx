import {
  DownloadIcon,
  KebabHorizontalIcon,
  MoveToTopIcon,
  NoteIcon,
  PencilIcon,
  PersonIcon,
  PlusIcon,
  SyncIcon,
  TrashIcon,
  UploadIcon,
  WorkflowIcon,
} from "@primer/octicons-react";
import {
  ActionList,
  ActionMenu,
  Box,
  IconButton,
  Link,
  useConfirm,
} from "@primer/react";
import moment from "moment";
import { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import * as XLSX from "xlsx";
import { crmHocSinhApi } from "../../api/crmHocSinhApi";
import { ComboboxTruong } from "../../components/combobox-truong";
import { Infomation } from "../../components/hoc-sinh/HocSinhRender";
import HocSinhShortInfo from "../../components/hoc-sinh/HocSinhShortInfo";
import SelectBoxTrangThaiHocSinhMultiple from "../../components/selectbox-trangthai-hocsinh-multiple";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import Text from "../../components/ui/text";
import TextWithEllipse from "../../components/ui/text-with-ellipse";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { ePageBaseStatus } from "../../models/ePageBaseStatus";
import { IHocSinhSelectRequest } from "../../models/request/hoc-sinh/IHocSinhSelectRequest";
import { IHocSinhItemResponse } from "../../models/response/crm-hocsinh/IHocSinhItemResponse ";
import { actions } from "../../state/actions/actionsWrapper";
import { RootState } from "../../state/reducers";
import HocSinhProfile from "../hoc-sinh-profile";
import PhuHuynhProfile from "../phu-huynh-profile";
import DoiTrangThaiHocSinhModal from "./doi-trang-thai-modal/DoiTrangThaiHocSinhModal";
import ImportHocSinhModal from "./import-hoc-sinh-modal/ImportHocSinhModal";
import ImportThiDauVaoModal from "../thi-dau-vao/import-thi-dau-vao-modal/ImportThiDauVaoModal";

const HocSinhPage = ({
  onValueChanged,
}: {
  onValueChanged?: (values: { dm_trangthaihocsinh_ids: string }) => void;
}) => {
  const { status, hocSinhs } = useSelector(
    (state: RootState) => state.crmHocSinh
  );
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
  const { translate } = useCommonContext();
  const confirm = useConfirm();
  const dispatch = useDispatch();

  const { dm_truong_selected_id } = useSelector((x: RootState) => x.common);
  const [dm_trangthaihocsinh_ids, setDmTrangThaiIds] = useState<number[]>([]);
  const [isShowModal, setIsShowModal] = useState(false);
  const [selectedId, setSelectedId] = useState(0);
  const [hocSinhEditing, setHocSinhEditing] = useState({ id: 0 });
  const [allHocSinhs, setAllHocSinhs] = useState<any[]>([]);
  const [isShowModalImport, setIsShowModalImport] = useState(false);
  const [isShowModalImportThiDauVao, setIsShowModalImportThiDauVao] = useState(false);
  const [danhSachHocSinhSlectedId, setDanhSachHocSinhSlectedId] = useState<
    number[]
  >([]);
  const [isDoiTrangThai, setIsDoiTrangThai] = useState(false);
  const [isShowModalDoiTrangThai, setIsShowModalDoiTrangThai] = useState(false);
  const [selectedPHId, setSelectedPHId] = useState(0);

  const onSelectionChanged = (selectedRowKeys: number[]) => {
    setDanhSachHocSinhSlectedId(selectedRowKeys);
  };
  const [isShowPHModal, setIsShowPHModal] = useState(false);
  const isInitialMount = useRef(true);

  useEffect(() => {
    const loadAllHocSinh = async () => {
      const payload = {
        nam_hoc: nam_hoc,
        searchText: "",
        dm_trangthaihocsinh_ids: [],
        dm_truong_id: dm_truong_selected_id || 0,
        dm_coso_id: dm_coso_id || 1,
        dm_khoi_id: 0,
        dm_he_id: 0,
        dm_lop_id: 0,
      };
      const res = await crmHocSinhApi.select(payload);
      if (res.is_success) {
        setAllHocSinhs(res.data);
      }
    };

    loadAllHocSinh();
  }, [nam_hoc, dm_truong_selected_id, dm_coso_id]);



  const handleReload = useCallback(() => {
    const payload: IHocSinhSelectRequest = {
      nam_hoc: nam_hoc,
      searchText: "",
      dm_trangthaihocsinh_ids: dm_trangthaihocsinh_ids,
      dm_truong_id: dm_truong_selected_id,
      dm_coso_id: dm_coso_id,
      dm_khoi_id: 0,
      dm_he_id: 0,
      dm_lop_id: 0,
    };
    console.log('HocSinh - handleReload called with payload:', payload);
    dispatch(actions.crmHocSinh.LOAD_START(payload));
  }, [
    nam_hoc,
    dm_trangthaihocsinh_ids,
    dm_truong_selected_id,
    dm_coso_id,
    dispatch,
  ]);

  useEffect(() => {
    if (status === ePageBaseStatus.is_not_initialization ||
        status === ePageBaseStatus.is_need_reload ||
        isInitialMount.current) {

      if (isInitialMount.current) {
        isInitialMount.current = false;
      }

      handleReload();
    }
  }, [status, handleReload]);

  // Auto reload when any filter changes
  useEffect(() => {
    console.log('HocSinh - Filter change detected:', {
      nam_hoc,
      dm_coso_id,
      dm_truong_selected_id,
      dm_trangthaihocsinh_ids,
      isInitialMount: isInitialMount.current
    });
    if (!isInitialMount.current) {
      handleReload();
    }
  }, [nam_hoc, dm_coso_id, dm_truong_selected_id, dm_trangthaihocsinh_ids, handleReload]);




  const handeDelete = async (id: number) => {
    if (
      await confirm({
      content: ``,
        title: `${translate("Base.Label.Warning")}`,
        cancelButtonContent: `Không xóa`,
        confirmButtonContent: `Xóa`,
        confirmButtonType: "danger",
      })
    ) {
      const res = await crmHocSinhApi.delete(id);
      if (res.is_success) {
        NotifyHelper.Success("Success");
        handleReload();
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    }
  };

  const handleEditClick = (data: any) => {
    setHocSinhEditing(data); // Cập nhật hocSinhEditing với dữ liệu hiện tại
    setIsShowModal(true); // Mở modal
  };
  const handleCloseDetailModal = useCallback(() => {
    setIsShowModalImport(false);
    setIsShowModalImportThiDauVao(false);
  }, []);

  const handleExportExcel = () => {
    const exportData = hocSinhs.map((item) => ({
      "Mã học sinh": item.ma_hs,
      "Họ tên": item.ho_ten,
      "Trạng thái": item.trang_thai,
      "Ngày sinh": moment(item.ngay_sinh).format("DD/MM/YYYY"),
      "Giới tính": item.gioi_tinh,
      "Quốc tịch": item.quoc_tich,
      "Dân tộc": item.dan_toc,
      "Tôn giáo": item.ton_giao,
      "Địa chỉ": item.dia_chi_tt,
      Trường: item.ten_truong,
      Khối: item.ten_khoi,
      Lớp: item.ten_lop,
      Hệ: item.ten_he,
      "Năm nhập học": item.nam_nhap_hoc,
      "Ngày đăng ký": moment(item.ngay_dang_ky).format("DD/MM/YYYY"),
      "Họ tên cha": item.ho_ten_lien_he_cha,
      "Điện thoại cha": item.dien_thoai_lien_he_cha,
      "Email cha": item.email_lien_he_cha,
      "Họ tên mẹ": item.ho_ten_lien_he_me,
      "Điện thoại mẹ": item.dien_thoai_lien_he_me,
      "Email mẹ": item.email_lien_he_me,
    }));
    const ws = XLSX.utils.json_to_sheet(exportData);
    if (!ws["!ref"]) {
      return;
    }
    const columnWidths = [
      { wch: 15 }, // Mã học sinh
      { wch: 25 }, // Họ tên
      { wch: 15 }, // Trạng thái
      { wch: 15 }, // Ngày sinh
      { wch: 10 }, // Giới tính
      { wch: 15 }, // Quốc tịch
      { wch: 15 }, // Dân tộc
      { wch: 15 }, // Tôn giáo
      { wch: 30 }, // Địa chỉ
      { wch: 25 }, // Trường
      { wch: 10 }, // Khối
      { wch: 10 }, // Lớp
      { wch: 20 }, // Hệ
      { wch: 15 }, // Năm nhập học
      { wch: 15 }, // Ngày đăng ký
      { wch: 25 }, // Họ tên cha
      { wch: 15 }, // Điện thoại cha
      { wch: 25 }, // Email cha
      { wch: 25 }, // Họ tên mẹ
      { wch: 15 }, // Điện thoại mẹ
      { wch: 25 }, // Email mẹ
    ];
    ws["!cols"] = columnWidths;
    const headerStyle = {
      font: {
        bold: true,
        color: { rgb: "FFFFFF" },
      },
      fill: {
        fgColor: { rgb: "4472C4" },
      },
      alignment: {
        horizontal: "center",
        vertical: "center",
      },
      border: {
        top: { style: "thin" },
        bottom: { style: "thin" },
        left: { style: "thin" },
        right: { style: "thin" },
      },
    };
    const range = XLSX.utils.decode_range(ws["!ref"]);
    const headerRange = {
      s: { r: 0, c: 0 },
      e: { r: 0, c: range.e.c },
    };
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: col });
      if (!ws[cellRef]) continue;
      ws[cellRef].s = headerStyle;
    }
    ws["!rows"] = [{ hpt: 30 }];
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Danh sách học sinh");
    const title = `DANH SÁCH HỌC SINH ${moment().format("DD/MM/YYYY")}`;
    const wsTitle = XLSX.utils.aoa_to_sheet([[title]]);
    const titleStyle = {
      font: {
        bold: true,
        size: 16,
        color: { rgb: "000000" },
      },
      alignment: {
        horizontal: "center",
        vertical: "center",
      },
    };
    wsTitle["A1"].s = titleStyle;
    wsTitle["!merges"] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: range.e.c } }];
    XLSX.writeFile(
      wb,
      `Danh_sach_hoc_sinh_${moment().format("DD-MM-YYYY")}.xlsx`,
      { bookType: "xlsx", type: "binary" }
    );
  };

  return (
    <Box
      sx={{
        p: 3,
        overflowX: "auto",
      }}
    >
      <DataTable
        height={`${window.innerHeight - 280}px`}
        data={hocSinhs}
        isLoading={status === ePageBaseStatus.is_loading}
        title={translate("Học sinh")}
        subTitle={`${translate("Base.Label.TotalCount")}: ${hocSinhs.length}`}
        searchEnable
        paging={{
          enable: true,
          pageSizeItems: [20, 50, 200, 500, 1000],
        }}
        actionComponent={
          <Box
            sx={{
              display: "flex",
            }}
          >
            <SelectBoxTrangThaiHocSinhMultiple
              selectedValue={dm_trangthaihocsinh_ids}
              onSelectionChanged={(ids) => {
                setDmTrangThaiIds(ids);
                onValueChanged &&
                  onValueChanged({
                    dm_trangthaihocsinh_ids: ids.join(","),
                  });
              }}
              maxWidth={300}
              isShowClearBtn={true}
              hocSinhs={allHocSinhs}
            />

            <ComboboxTruong
              value={dm_truong_selected_id}
              onValueChanged={(value) => {
                console.log('HocSinh - ComboboxTruong onValueChanged called with value:', value);
                dispatch(
                  actions.common.changedValueHeaderComboboxTruong(value)
                );
              }}
              isShowClearButton={true}
            />
            <Button
              sx={{ ml: 1 }}
              text="Base.Label.AddNew"
              leadingVisual={PlusIcon}
              size="medium"
              variant="primary"
              onClick={() => {
                handleEditClick([]);
              }}
            />

            <ActionMenu>
              <ActionMenu.Button variant="default" sx={{ ml: 1 }}>
                <Text text="Thao tác" />
              </ActionMenu.Button>
              <ActionMenu.Overlay width="auto">
                <ActionList>
                  <ActionList.Item
                    onSelect={() => {
                      if (danhSachHocSinhSlectedId.length > 0) {
                        setIsDoiTrangThai(true);
                        setIsShowModalDoiTrangThai(true);
                      } else {
                        NotifyHelper.Warning("Vui lòng chọn học sinh trước");
                      }
                    }}
                  >
                    <ActionList.LeadingVisual>
                      <MoveToTopIcon />
                    </ActionList.LeadingVisual>
                    <Text
                      text={`Đổi trạng thái (${danhSachHocSinhSlectedId.length})`}
                    />
                  </ActionList.Item>

                   <ActionList.Item onSelect={() => setIsShowModalImportThiDauVao(true)}>
                    <ActionList.LeadingVisual>
                      <UploadIcon />
                    </ActionList.LeadingVisual>
                    <Text text="Import thi đầu vào" />
                  </ActionList.Item>

                  <ActionList.Item onSelect={() => setIsShowModalImport(true)}>
                    <ActionList.LeadingVisual>
                      <UploadIcon />
                    </ActionList.LeadingVisual>
                    <Text text="Base.Label.Import" />
                  </ActionList.Item>

                  <ActionList.Item onSelect={handleExportExcel}>
                    <ActionList.LeadingVisual>
                      <DownloadIcon />
                    </ActionList.LeadingVisual>
                    <Text text="Export" />
                  </ActionList.Item>
                </ActionList>
              </ActionMenu.Overlay>
            </ActionMenu>
            <Button
              text="Base.Label.Refresh"
              leadingVisual={SyncIcon}
              size="medium"
              sx={{ ml: 1 }}
              onClick={() => {
                handleReload();
              }}
            />
          </Box>
        }
        selection={{
          mode: "multiple",
          keyExpr: "id",
          selectedRowKeys: danhSachHocSinhSlectedId,
          onSelectionChanged(keys) {
            onSelectionChanged(keys);
          },
        }}
        columns={[
          {
            id: "cmd",
            width: "50px",
            caption: "#",
            align: "center",
            fixed: true,
            cellRender: (data: IHocSinhItemResponse) => {
              return (
                <ActionMenu>
                  <ActionMenu.Anchor>
                    <IconButton
                      icon={KebabHorizontalIcon}
                      variant="invisible"
                      aria-label="Open column options"
                    />
                  </ActionMenu.Anchor>

                  <ActionMenu.Overlay>
                    <ActionList>
                      <ActionList.Item onSelect={() => handleEditClick(data)}>
                        <ActionList.LeadingVisual>
                          <PencilIcon />
                        </ActionList.LeadingVisual>
                        <TextTranslated value="Base.Label.Edit" />
                      </ActionList.Item>
                      <ActionList.Divider />
                      <ActionList.Item
                        variant="danger"
                        onSelect={() => {
                          handeDelete(data.id);
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <TrashIcon />
                        </ActionList.LeadingVisual>
                        <TextTranslated value="Base.Label.Delete" />
                      </ActionList.Item>
                    </ActionList>
                  </ActionMenu.Overlay>
                </ActionMenu>
              );
            },
          },
          {
            dataField: "trang_thai",
            width: "150px",
            caption: translate("Trạng thái"),
            fixed: true,
            isMainColumn: true,
            cellRender: (data: IHocSinhItemResponse) => {
              let color: any = "fg.default";
              switch (data.trang_thai) {
                case "New Enrollment":
                  color = "accent.emphasis";
                  break;
                case "Thôi học":
                  color = "danger.emphasis";
                  break;
                case "Học sinh lên lớp chưa đóng phí":
                  color = "attention.emphasis";
                  break;

                default:
                  break;
              }
              return (
                <Box
                  sx={{
                    color: color,
                    // color: "attention.emphasis"
                  }}
                // variant={
                //   data.trang_thai === "New Enrollment"
                //     ? "accent"
                //     : data.trang_thai === "Re-Enrollment"
                //       ? "success"
                //       : data.trang_thai === "Tiềm năng"
                //         ? "sponsors"
                //         : data.trang_thai === "Thôi học"
                //           ? "danger"
                //           : "default"
                // }
                >
                  <TextWithEllipse text={data.trang_thai} lineNumber={1} />
                </Box>
              );
            },
          },
          // {
          //   dataField: "avatar",
          //   width: "100px",
          //   caption: translate("Avatar"),
          //   fixed: true,
          //   cellRender: (data: IHocSinhItemResponse) => {
          //     return <PreviewableAvatar
          //       avatarUrl={data.avatar}
          //       fullName={data.ho_ten}
          //       size="xlarge"
          //     />
          //   },
          // },

          {
            dataField: "ho_ten",
            width: "200px",
            caption: translate("Học sinh"),
            cellRender: (data: IHocSinhItemResponse) => {
              const infos: Infomation[] = [
                {
                  title: "",
                  data: data.ho_ten,
                  icon: <PersonIcon size={14} />,
                  fontWeight: "500",
                },
                {
                  title: "",
                  data: data.ho_ten_en,
                  icon: <WorkflowIcon size={14} />,
                },
                { title: "", data: data.ma_hs, icon: <NoteIcon size={14} /> },
              ];
              return (
                // <HocSinhRender infors={infos} />
                <HocSinhShortInfo data={data} />
              );
            },
          },
          // {
          //   dataField: "thong_tin_2",
          //   caption: translate("Thông tin"),
          //   cellRender: (data: IHocSinhItemResponse) => {
          //     const infos: Infomation[] = [
          //       {
          //         title: "Ngày sinh",
          //         data: moment(data.ngay_sinh).format("DD/MM/YYYY"),
          //         icon: <CalendarIcon size={14} />,
          //       },
          //       {
          //         title: "Giới tính",
          //         data: data.gioi_tinh,
          //         icon: <InfoIcon size={14} />,
          //       },
          //     ];
          //     return <HocSinhRender infors={infos} />;
          //   },
          // },
          // {
          //   dataField: "thong_tin_1",
          //   caption: translate("Quốc tịch"),
          //   cellRender: (data: IHocSinhItemResponse) => {
          //     const infos: Infomation[] = [
          //       { title: 'Quốc tịch', data: data.quoc_tich, icon: <GlobeIcon size={14} /> },
          //       { title: 'Dân tộc', data: data.dan_toc, icon: <InfoIcon size={14} /> },
          //       { title: 'Tôn giáo', data: data.ton_giao, icon: <InfoIcon size={14} /> },
          //     ];
          //     return (
          //       <HocSinhRender infors={infos} />
          //     )
          //   },
          // },

          {
            dataField: "thong_lop",
            caption: translate("Trường lớp"),
            width: "250px",
            cellRender: (data: IHocSinhItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    // fontWeight: 600
                  }}
                >
                  <Box sx={{ display: "flex" }}>
                    <Box sx={{ fontWeight: 600 }}>{data.ten_lop}</Box>
                  </Box>
                  <Box sx={{ color: "fg.muted" }}>
                    {data.ten_khoi}, Hệ {data.ten_he}
                  </Box>
                </Box>
              );
            },
          },
          {
            dataField: "nam_nhap_hoc",
            caption: translate("Nhập học"),
            align: "center",
            width: "100px",
            cellRender: (data: IHocSinhItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  <Box sx={{ display: "flex" }}>
                    <Box sx={{ fontWeight: 600 }}>{data.nam_nhap_hoc}</Box>
                  </Box>
                  <Box sx={{ color: "fg.muted" }}>
                    {moment(data.ngay_dang_ky).format("DD/MM/YYYY")}
                  </Box>
                </Box>
              );
            },
          },

          {
            dataField: "thong_tin_cha",
            caption: translate("Thông tin cha"),
            width: "200px",
            cellRender: (data: IHocSinhItemResponse) => {
              const handleRowClick = () => {
                setSelectedPHId(data.id_lien_he_cha);
                setIsShowPHModal(true);
              };
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  <Box sx={{ display: "flex" }} onClick={handleRowClick}
                  >
                    <Link href="#" underline>
                      <span style={{ fontWeight: "600", }}>
                        {data.ho_ten_lien_he_cha}
                      </span>
                    </Link>
                  </Box>
                  <Box sx={{ color: "fg.muted" }}>
                    <TextWithEllipse
                      text={`${data.dien_thoai_lien_he_cha ?? ""} - ${data.email_lien_he_cha ?? ""
                        }`}
                      lineNumber={1}
                    />
                  </Box>
                </Box>
              );
            },
          },
          {
            dataField: "thong_tin_me",
            width: "200px",
            caption: translate("Thông tin mẹ"),
            cellRender: (data: IHocSinhItemResponse) => {
              const handleRowClick = () => {
                setSelectedPHId(data.id_lien_he_me);
                setIsShowPHModal(true);
              };
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  <Box sx={{ display: "flex" }} onClick={handleRowClick}>
                    <Link href="#" underline>
                      <span style={{ fontWeight: "600", }}>
                        {data.ho_ten_lien_he_me}
                      </span>
                    </Link>
                  </Box>
                  <Box sx={{ color: "fg.muted" }}>
                    <TextWithEllipse
                      text={`${data.dien_thoai_lien_he_me ?? ""} - ${data.email_lien_he_me ?? ""
                        }`}
                      lineNumber={1}
                    />
                  </Box>
                </Box>
              );
            },
          },
          {
            dataField: "thong_tin_lien_he",
            caption: translate("Thông tin liên hệ "),
            width: "200px",
            cellRender: (data: IHocSinhItemResponse) => {
              const handleRowClick = () => {
                setSelectedPHId(data.id_lien_he_nguoilienhe);
                setIsShowPHModal(true);
              };
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  <Box sx={{ display: "flex" }} onClick={handleRowClick}>
                    <Box sx={{ fontWeight: 600 }}><Link href="#" underline>
                      <span style={{ fontWeight: "600", }}>
                        {data.ho_ten_lien_he}
                      </span>
                    </Link></Box>{" "}
                    <Box sx={{ color: "fg.muted", ml: 2 }}>
                      {data.quan_he_nguoi_lien_he}
                    </Box>
                  </Box>
                  <Box sx={{ color: "fg.muted" }}>
                    <TextWithEllipse
                      text={`${data.dien_thoai_lien_he ?? ""} - ${data.email_lien_he ?? ""
                        }`}
                      lineNumber={1}
                    />
                  </Box>
                </Box>
              );
            },
          },
          {
            dataField: "dia_chi_hientai",
            caption: translate("Địa chỉ hiện tại"),
            width: "200px",
            cellRender: (data: IHocSinhItemResponse) => {
              return (
                <TextWithEllipse text={data.dia_chi_hientai} lineNumber={1} />
              );
            },
          },
        ]}
      />
      {isShowModal && (
        <HocSinhProfile
          id={hocSinhEditing.id}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReload();
            // setIsShowModal(false);
          }}
        />
      )}
      {isShowModalImport && (
        <ImportHocSinhModal
          onClose={handleCloseDetailModal}
          animationOf={`#btnImport`}
          dm_truong_id={dm_truong_selected_id}
          nam_hoc={nam_hoc}
        />
      )}
      {isShowModalDoiTrangThai && (
        <DoiTrangThaiHocSinhModal
          isOpen={isShowModalDoiTrangThai}
          onClose={() => setIsShowModalDoiTrangThai(false)}
          hocSinhIds={danhSachHocSinhSlectedId}
          onSuccess={() => {
            handleReload();
          }}
        />
      )}
      {isShowPHModal && (
        <PhuHuynhProfile
          id={selectedPHId}
          onClose={() => {
            setIsShowPHModal(false);
          }}
          onSuccess={() => {
            handleReload();
            setIsShowPHModal(false);
          }}
        />
      )}
      {isShowModalImportThiDauVao && (
        <ImportThiDauVaoModal
          onClose={handleCloseDetailModal}
          animationOf={`#btnImportThiDauVao`}
        />
      )}
    </Box>
  );
};

export default HocSinhPage;
