import { CheckIcon, DownloadIcon, LinkIcon, UploadIcon, XIcon } from "@primer/octicons-react";
import { Box, FormControl, Link, TextInput } from '@primer/react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { uploadApi } from '../../../api/uploadApi';
import { hocSinhApi } from '../../../api/hocSinhApi';
import { appInfo } from '../../../AppInfo';
import { AnimationPopup } from '../../../components/modal';
import { PopUpForm, PopUpFormActions } from '../../../components/pop-up-form/PopUpForm';
import Steps, { IStepData } from '../../../components/steps/Steps';
import Button from '../../../components/ui/button';
import { useCommonContext } from '../../../contexts/common';
import { NotifyHelper } from '../../../helpers/toast';
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";


interface IImportHocSinhModalProps {
    dm_truong_id: number;
    nam_hoc: string;
    onClose: () => void;
    animationOf: string;
}

const _steps: IStepData[] = [
    {
        id: 1,
        name: "Upload file",
        is_active: true
    },
    {
        id: 2,
        name: "Validate",
        is_active: false
    },
    {
        id: 3,
        name: "Import",
        is_active: false
    }
];

const ImportHocSinhModal = (props: IImportHocSinhModalProps) => {
    const { translate } = useCommonContext();
    const { onClose, animationOf, dm_truong_id } = props;

    const [stepId, setStepId] = useState(1);
    const [uploadedFileName, setUploadedFileName] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const componentDidMount = useRef<boolean>(false);
    const [dataSource, setDataSource] = useState<any[]>([]);
    const [dataSourceValidated, setDataSourceValidated] = useState<any[]>([]);
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
    const modalId = `import-modal-${Math.random().toString(36).substring(7)}`;

    const notValidCount = useMemo<number>(() => {
      return dataSourceValidated.filter((x) => x.ma_loi !== undefined && x.ma_loi !== "").length;
    }, [dataSourceValidated]);

    const handleSelectFile = () => {
        const input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.setAttribute(
            'accept',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'
        );
        input.click();
        input.onchange = async () => {
            var file = input && input.files ? input.files[0] : null;
            if (file) {
                setIsLoading(true);
                const res = await uploadApi.uploadExcel(file);
                if (res.is_success) {
                    setUploadedFileName(res.data.fileNameId);
                    setStepId(2);
                } else {
                    NotifyHelper.Error(res.message ?? "Error");
                }
                setIsLoading(false);
            }
        };
        input.remove();
    };

    useEffect(() => {
        componentDidMount.current = true;
        if (uploadedFileName !== "") {
            handleReadUploadedFile();
        }
        return () => {
            componentDidMount.current = false;
        };
    }, [uploadedFileName]);

    // Add styles to make modal full screen
    useEffect(() => {
        // Create style element for full screen modal
        const styleElement = document.createElement('style');
        styleElement.innerHTML = `
            #${modalId} > div {
                max-width: 100% !important;
                width: 100% !important;
                height: 100vh !important;
                margin: 0 !important;
                border-radius: 0 !important;
            }
            #${modalId} .popup-header {
                padding: 16px 24px;
            }
            #${modalId} {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 1000;
            }
            /* Custom table scrolling styles */
            .import-hoc-sinh-container table {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
            }

            /* Smooth scrollbars */
            .table-container::-webkit-scrollbar {
                height: 8px;
                width: 8px;
            }

            .table-container::-webkit-scrollbar-track {
                background: #f6f8fa;
                border-radius: 4px;
            }

            .table-container::-webkit-scrollbar-thumb {
                background: #d1d5da;
                border-radius: 4px;
            }

            .table-container::-webkit-scrollbar-thumb:hover {
                background: #c1c8cd;
            }
        `;
        document.head.appendChild(styleElement);

        // Apply ID to the modal once it renders
        setTimeout(() => {
            const modalElement = document.querySelector('.animation-popup');
            if (modalElement) {
                modalElement.id = modalId;
            }
        }, 100);

        return () => {
            document.head.removeChild(styleElement);
        };
    }, [modalId]);

    const handleReadUploadedFile = async () => {
        setIsLoading(true);
        const res = await uploadApi.readExcel({
            fileName: uploadedFileName,
            sheetIndex: 0,
        });

        if (!componentDidMount.current) return;

        if (res.is_success) {
            setDataSource(res.data);
            await handleValidateImportFile();
        }
        setIsLoading(false);
    };

    const handleValidateImportFile = async () => {
      setIsLoading(true);
      const datasend = {
          fileName: uploadedFileName,
          sheetIndex: 0
      };
      const res = await hocSinhApi.ValidateImportHocSinh(datasend, nam_hoc, dm_coso_id);
      if (!componentDidMount.current) return;
      if (res.is_success) {
          setDataSourceValidated(res.data);
      }
      setIsLoading(false);
    };

    const handleImport = async () => {
        setIsLoading(true);
        const datasend = {
            fileName: uploadedFileName,
            sheetIndex: 0
        };
        const res = await hocSinhApi.ImportHocSinh(datasend, nam_hoc, dm_coso_id);
        if (!componentDidMount.current) return;
        if (res.is_success) {
            setStepId(4); // Chuyển sang bước 4 sau khi lưu thành công
            NotifyHelper.Success("Success!");
        } else {
            NotifyHelper.Error("Error");
        }
        setIsLoading(false);
    };

    const tableHeaders = useMemo(() => {
        if (!dataSourceValidated || dataSourceValidated.length === 0) return [];
        const keys = Object.keys(dataSourceValidated[0]).filter((key) => key !== "0" && key !== "ID");
        return ['Kết quả', ...keys];
    }, [dataSourceValidated]);

    return (
        <AnimationPopup
            animationOf={animationOf}
            width="100%"  // Set width to 100% - this prop seems to be accepted
            title='Import học sinh'
            onClose={onClose}
        >
            <Box style={{
                display: "flex",
                flexDirection: "column",
                height: "calc(100vh - 220px)",
                overflow: "hidden"
            }} className="import-hoc-sinh-container">
                <Box style={{ marginBottom: "24px" }}>
                    <Steps steps={_steps.map(x => ({
                        ...x,
                        is_active: x.id === stepId
                    }))} />
                </Box>

                <Box style={{ display: "flex", marginBottom: "24px" }}>
                    <div style={{ display: 'flex', gap: '8px', alignItems: 'center', width: '100%' }}>
                        <Button
                            leadingVisual={UploadIcon}
                            text='Upload file'
                            onClick={handleSelectFile}
                            size='medium'
                            isLoading={isLoading}
                        />
                        {uploadedFileName && (
                            <TextInput
                                style={{ width: 400 }}
                                value={uploadedFileName}
                                readOnly
                            />
                        )}
                        <Box style={{ flex: 1 }} />
                        <Link href={`${appInfo.baseApiURL.replace('/api', '')}/TEMPLATE/IMPORT_HOCSINH.xlsx`} target='_blank'>
                            <Button
                                text={translate('Tải mẫu import học sinh tại đây')}
                                variant='invisible'
                                leadingVisual={DownloadIcon}
                            />
                        </Link>
                    </div>
                </Box>

                {stepId >= 2 && (
                    <Box style={{
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                        overflow: "auto",
                        overflowX: "auto",
                        overflowY: "auto"
                    }}>
                        {notValidCount > 0 && (
                            <Box sx={{ marginBottom: '16px' }}>
                                <FormControl.Validation variant='error'>
                                    Có {notValidCount} dòng không hợp lệ
                                </FormControl.Validation>
                            </Box>
                        )}

                        {isLoading ? (
                            <Box style={{ textAlign: 'center', padding: '20px' }}>
                                Đang tải...
                            </Box>
                        ) : (
                            <div className="table-container" style={{
                                overflowX: 'auto',
                                overflowY: 'auto',
                                maxHeight: 'calc(100vh - 350px)',
                                border: '1px solid #e1e4e8',
                                borderRadius: '6px'
                            }}>
                                <table style={{
                                    width: '100%',
                                    borderCollapse: 'collapse',
                                    minWidth: 'max-content'
                                }}>
                                    <thead style={{
                                        backgroundColor: '#f6f8fa',
                                        position: 'sticky',
                                        top: 0,
                                        zIndex: 1
                                    }}>
                                        <tr>
                                            {tableHeaders.map((header, index) => (
                                                <th key={index} style={{
                                                    padding: '12px 16px',
                                                    textAlign: 'left',
                                                    borderBottom: '1px solid #e1e4e8',
                                                    borderRight: index < tableHeaders.length - 1 ? '1px solid #e1e4e8' : 'none',
                                                    fontWeight: '600',
                                                    fontSize: '14px',
                                                    whiteSpace: 'nowrap',
                                                    minWidth: header === 'Kết quả' ? '200px' :
                                                             header === 'STT' ? '80px' :
                                                             header === 'Mã học sinh' ? '150px' :
                                                             header === 'Họ tên' ? '200px' :
                                                             header === 'Ngày sinh' ? '120px' :
                                                             header === 'Giới tính' ? '100px' : '180px'
                                                }}>
                                                    {header}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {dataSourceValidated.map((row, rowIndex) => (
                                            <tr key={rowIndex} style={{
                                                backgroundColor: rowIndex % 2 === 0 ? '#ffffff' : '#f6f8fa'
                                            }}>
                                                {/* Cột kết quả */}
                                                <td style={{
                                                    padding: '12px 16px',
                                                    borderBottom: '1px solid #e1e4e8',
                                                    borderRight: '1px solid #e1e4e8',
                                                    verticalAlign: 'top',
                                                    maxWidth: '200px'
                                                }}>
                                                    {row.err ? (
                                                        <FormControl.Validation variant='error'>
                                                            <div style={{
                                                                whiteSpace: 'pre-line',
                                                                fontSize: '12px',
                                                                lineHeight: '1.4'
                                                            }}>
                                                                {row.err}
                                                            </div>
                                                        </FormControl.Validation>
                                                    ) : (
                                                        <FormControl.Validation variant='success'>
                                                            ✓ Hợp lệ
                                                        </FormControl.Validation>
                                                    )}
                                                </td>

                                                {/* Các cột dữ liệu */}
                                                {tableHeaders.slice(1).map((header, cellIndex) => (
                                                    <td key={cellIndex} style={{
                                                        padding: '12px 16px',
                                                        borderBottom: '1px solid #e1e4e8',
                                                        borderRight: cellIndex < tableHeaders.length - 2 ? '1px solid #e1e4e8' : 'none',
                                                        whiteSpace: 'nowrap',
                                                        fontSize: '14px'
                                                    }}>
                                                        {row[header] || ''}
                                                    </td>
                                                ))}
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </Box>
                )}

                <div style={{
                    position: 'sticky',
                    bottom: 0,
                    backgroundColor: 'white',
                    padding: '16px 0',
                    borderTop: '1px solid #e1e4e8'
                }}>
                    <PopUpFormActions>
                        <Button
                            leadingVisual={XIcon}
                            text='Base.Label.Close'
                            onClick={onClose}
                        />
                        {stepId === 2 && (
                            <Button
                                leadingVisual={CheckIcon}
                                variant='primary'
                                isLoading={isLoading}
                                text={'ThoiKhoaBieuImport.Label.Save'}
                                onClick={handleImport}
                                disabled={notValidCount > 0}
                            />
                        )}
                    </PopUpFormActions>
                </div>
            </Box>
        </AnimationPopup>
    );
};

export default ImportHocSinhModal;