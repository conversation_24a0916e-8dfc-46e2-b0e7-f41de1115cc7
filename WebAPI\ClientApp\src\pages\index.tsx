import { LazyPage } from '../components/loadable';


export { default as HomePage } from './home/<USER>';


export const LopHanhChinhPage = LazyPage(import('./lop-hanh-chinh'));

export const SurveyForm = LazyPage(import('./crm-survey-form/SurveyForm'));
export const SurveyFormPage = LazyPage(import('./crm-survey-form'));
export const SurveyLayout = LazyPage(import('./crm-survey'));
export const SurveyRespone = LazyPage(import('./survey-respone'));

export const NguonBietToiPage = LazyPage(import('./crm-category/dm_nguonbiettoi/NguonBietToiPage'));
export const NguonBietToiLayout = LazyPage(import('./crm-category/NguonBietToiLayout'));
export const FoThucDonLayout = LazyPage(import('./thuc-don/FoThucDonLayout'));

export const HocSinhPhuHuynhPage = LazyPage(import('./danh-muc/hoc_sinh_phu_huynh'));
export const TrangThaiHocSinhPage = LazyPage(import('./trang-thai-hoc-sinh'));
export const TrangThaiPhuHuynhPage = LazyPage(import('./trang-thai-phu-huynh'));
export const LoaiGiayTo = LazyPage(import('./loai-giay-to'));
export const SendEmailLayout = LazyPage(import('./email/send-email/SendEmailLayout'));
export const SendNewEmailPage = LazyPage(import('./email/send-email/SendNewEmailPage'));
export const AcceptanceFormPage = LazyPage(import('./acceptance-form'));
export const SucKhoeLayout = LazyPage(import('./suc-khoe/SucKhoeLayout'));
export const QuanHeGiaDinhLayout = LazyPage(import('./hoc-sinh-profile/gia-dinh/QuanHeGiaDinhLayout'));
export const ThongTinCbnvPage = LazyPage(import('./thongtin-cbnv/ThongTinCbnvPage'));
export const BaoPhiLayout = LazyPage(import('./hoc-sinh-profile/bao-phi/BaoPhiLayout'));
export const ThongKePage = LazyPage(import('./thong-ke'));

export const HocSinhLayout = LazyPage(import('./hoc-sinh/HocSinh2Layout'));
export const Truong = LazyPage(import('./truong'));
export const He = LazyPage(import('./he'));
export const Khoi = LazyPage(import('./khoi'));
export const KhoiHePage = LazyPage(import('./khoi/khoi_he'));
export const Lop = LazyPage(import('./lop'));
export const ThangDiem = LazyPage(import('./quan-ly-thi/thang-diem'));
export const MonThi = LazyPage(import('./mon-thi'));
export const KyThi = LazyPage(import('./ky-thi'));
export const KyThiMonThi = LazyPage(import('./kythi-monthi'));
export const ThiSinh = LazyPage(import('./quan-ly-thi/thi-sinh/ThiSinhPage'));
export const DanhSachThiPage = LazyPage(import('./quan-ly-thi/danh-sach-thi'));
export const PhongThiPage = LazyPage(import('./quan-ly-thi/phong-thi'));
export const NhapDiemThiPage = LazyPage(import('./quan-ly-thi/nhap-diem-thi'));
export const KetQuaThiPage = LazyPage(import('./quan-ly-thi/ket-qua-thi'));
export const NoiTruLayout = LazyPage(import('./noi-tru/NoiTruLayout'));
export const TongHopLayout = LazyPage(import('./tong-hop/TongHopLayout'));
export const CskhPage = LazyPage(import('./cskh'));
export const PhuHuynhPage = LazyPage(import('./phu-huynh'));
export const ThoiHocBaoLuu = LazyPage(import('./thoi-hoc-bao-luu'));
export const MonThiDauVaoPage = LazyPage(import('./monthidauvao'));
export const ThiDauVaoPage = LazyPage(import('./thi-dau-vao/ThiDauVaoPage'));
export const TrangThaiThiSinhPage = LazyPage(import('./quan-ly-thi/trang-thai-thi-sinh'));


