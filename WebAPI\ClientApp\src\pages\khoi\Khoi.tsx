import {
  KebabHorizontalIcon,
  PencilIcon,
  PlusIcon,
  SyncIcon,
  TrashIcon,
} from "@primer/octicons-react";
import {
  ActionList,
  ActionMenu,
  Box,
  Checkbox,
  IconButton,
  useConfirm,
} from "@primer/react";
import { useEffect, useState } from "react";
import { khoiApi } from "../../api/khoiApi";
import { ComboboxTruong } from "../../components/combobox-truong";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { IKhoi } from "../../models/response/khoi/IKhoi";
import KhoiDetailModal from "./KhoiDetailModal";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { categorySourceApi } from "../../api/categorySourceApi";

const Khoi = () => {
  const [isShowModal, setIsShowModal] = useState(false);
  const [selectedId, setSelectedId] = useState(0);
  const [khois, setKhois] = useState<IKhoi[]>([]);
  const { translate } = useCommonContext();
  const { dm_coso_id } = useCommonSelectedHook();
  const [dm_truong_selected_id, setDmTruongSelectedId] = useState<number>(0);
  const confirm = useConfirm();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    handleReloadAsync();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dm_coso_id, dm_truong_selected_id]);

  const handleReloadAsync = async () => {
    try {
      setIsLoading(true);

      let data: IKhoi[] = [];

      if (dm_truong_selected_id > 0) {
        // If a school is selected, get khois for that school
        const res = await categorySourceApi.SelectKhoisByTruong(dm_truong_selected_id);
        if (res.is_success) {
          data = res.data;
        } else {
          NotifyHelper.Error(res.message ?? "");
        }
      } else {
        // Get all khois
        const res = await khoiApi.selectAll();
        if (res.is_success) {
          data = res.data;

          // If coso is selected, filter by coso
          if (dm_coso_id) {
            // Get all schools for this coso
            const truongRes = await categorySourceApi.SelectTruongs();
            if (truongRes.is_success) {
              const truongsInCoso = truongRes.data.filter(
                (truong: { id: number; dm_coso_id: number }) => truong.dm_coso_id === dm_coso_id
              );

              // Filter khois by schools in this coso
              data = data.filter((khoi: IKhoi) =>
                truongsInCoso.some((truong: { id: number }) => truong.id === khoi.dm_truong_id)
              );
            }
          }
        } else {
          NotifyHelper.Error(res.message ?? "");
        }
      }

      setKhois(data);
    } catch (error) {
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    } finally {
      setIsLoading(false);
    }
  };

  const handeDelete = async (id: number) => {
    if (
      await confirm({
        content: "Bạn có chắc chắn muốn xóa khối này?",
        title: "Lưu ý",
        cancelButtonContent: "Không xóa",
        confirmButtonContent: "Xóa khối",
        confirmButtonType: "danger",
      })
    ) {
      try {
        const res = await khoiApi.delete(id);
        if (res.is_success) {
          NotifyHelper.Success("Success");
          handleReloadAsync();
        } else {
          NotifyHelper.Error(res.message ?? "");
        }
      } catch (error) {
        NotifyHelper.Error("Có lỗi xảy ra khi xóa");
      }
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <DataTable
        height={`${window.innerHeight - 250}px`}
        actionComponent={
          <Box
            sx={{
              display: "flex",
              gap: 2
            }}
          >
            <ComboboxTruong
              value={dm_truong_selected_id}
              onValueChanged={(value) => {
                setDmTruongSelectedId(value);
              }}
              isShowClearButton={true}
            />
            <Button
              text="Base.Label.Refresh"
              leadingVisual={SyncIcon}
              size="medium"
              onClick={handleReloadAsync}
              disabled={isLoading}
            />
            <Button
              text={translate("Base.Label.Create")}
              leadingVisual={PlusIcon}
              variant="primary"
              size="medium"
              onClick={() => {
                setIsShowModal(true);
                setSelectedId(0);
              }}
              disabled={isLoading}
            />
          </Box>
        }
        columns={[
          {
            id: "cmd",
            width: "50px",
            caption: "#",
            align: "center",
            cellRender: (data: IKhoi) => {
              return (
                <Box sx={{ m: -1 }}>
                  <ActionMenu>
                    <ActionMenu.Anchor>
                      <IconButton
                        icon={KebabHorizontalIcon}
                        variant="invisible"
                        aria-label="Open column options"
                      />
                    </ActionMenu.Anchor>

                    <ActionMenu.Overlay>
                      <ActionList>
                        <ActionList.Item
                          onSelect={() => {
                            setIsShowModal(true);
                            if (data.id !== undefined) {
                              setSelectedId(data.id);
                            }
                          }}
                        >
                          <ActionList.LeadingVisual>
                            <PencilIcon />
                          </ActionList.LeadingVisual>
                          <TextTranslated value="Base.Label.Edit" />
                        </ActionList.Item>
                        <ActionList.Divider />
                        <ActionList.Item
                          variant="danger"
                          onSelect={() => {
                            if (data.id !== undefined) {
                              handeDelete(data.id);
                            }
                          }}
                        >
                          <ActionList.LeadingVisual>
                            <TrashIcon />
                          </ActionList.LeadingVisual>
                          <TextTranslated value="Base.Label.Delete" />
                        </ActionList.Item>
                      </ActionList>
                    </ActionMenu.Overlay>
                  </ActionMenu>
                </Box>
              );
            },
          },
          {
            dataField: "ma_khoi",
            width: "auto",
            caption: `Mã khối`,
            cellRender: (data: IKhoi) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ma_khoi}
                </Box>
              );
            },
          },
          {
            dataField: "ten_khoi",
            width: "auto",
            isMainColumn: true,
            caption: `Tên khối (Vi)`,
            cellRender: (data: IKhoi) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_khoi || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "ten_khoi_en",
            width: "auto",
            caption: `Tên khối (En) `,
            cellRender: (data: IKhoi) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_khoi_en || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "is_show_campaign",
            width: "120px",
            caption: "Show Campaign",
            align: "center",
            cellRender: (data: IKhoi) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center"
                  }}
                >
                  <Checkbox
                    checked={!!data.is_show_campaign}
                    disabled
                  />
                </Box>
              );
            },
          }
        ]}
        data={khois}
        title="Khối"
        subTitle={`${translate("Base.Label.TotalCount")}: ${khois.length} ${dm_coso_id ? '(Đã lọc theo cơ sở)' : ''}`}
        searchEnable
      />
      {isShowModal && (
        <KhoiDetailModal
          dm_truong_id={dm_truong_selected_id}
          id={selectedId}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReloadAsync();
            setIsShowModal(false);
          }}
        />
      )}
    </Box>
  );
};

export default Khoi;

