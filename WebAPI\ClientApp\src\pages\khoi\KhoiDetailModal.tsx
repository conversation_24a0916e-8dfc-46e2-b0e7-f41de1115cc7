import { Box, Checkbox, FormControl } from "@primer/react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Button from "../../components/ui/button";
import Modal from "../../components/ui/modal";
import ModalActions from "../../components/ui/modal/ModalActions";
import TextInput from "../../components/ui/text-input";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { IHe } from "../../models/response/he/IHe";

import { IKhoi } from "../../models/response/khoi/IKhoi";
import { khoiApi } from "../../api/khoiApi";
import { ComboboxTruong } from "../../components/combobox-truong";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";

interface IKhoiDetailModalProps {
  dm_truong_id?:number;
  id: number;
  onClose: () => void;
  onSuccess: () => void;
}
const KhoiDetailModal = (props: IKhoiDetailModalProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { translate } = useCommonContext();
  const { dm_coso_id } = useCommonSelectedHook();
  const [formData, setFormData] = useState<IKhoi>({
    id: props.id,
    ma_khoi: "",
    ten_khoi: "",
    ten_khoi_en: "",
    dm_truong_id: props.dm_truong_id || 0,
    is_show_campaign: false,
  });

  const {
    register,
    handleSubmit,
    clearErrors,
    reset,
    setError,
    control,
    setValue,
    formState: { errors },
  } = useForm<IKhoi>({
    defaultValues: formData,
  });

  useEffect(() => {
    if (props.id > 0) {
      handleGetVm();
    } else {
      // Nếu là thêm mới và có dm_truong_id từ props
      if (props.dm_truong_id) {
        setValue("dm_truong_id", props.dm_truong_id);
        setFormData(prev => ({
          ...prev,
          dm_truong_id: props.dm_truong_id
        }));
      }
    }
  }, [props.id, props.dm_truong_id]);

  const handleGetVm = async () => {
    const res = await khoiApi.detail(props.id);
    if (res.is_success) {
      const data: IKhoi = {
        ...res.data,
      };
      setFormData(data);
      reset(data);
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
  };
  const handleTruongChange = (value: number) => {
    setValue("dm_truong_id", value);
    setFormData((prev) => ({
      ...prev,
      dm_truong_id: value,
    }));
  };

  const onSubmit = async (data: any) => {
    setIsSaving(true);
    let res: any;

    // Make sure we have the truong_id from the selected truong
    const truong_id = formData.dm_truong_id || props.dm_truong_id || 0;

    // Ensure is_show_campaign is properly set as a boolean
    const is_show_campaign = !!data.is_show_campaign;

    // Get the truong details to ensure it belongs to the correct coso
    const submitData = {
      ...data,
      id: props.id,
      dm_truong_id: truong_id,
      is_show_campaign: is_show_campaign
    };

    if (props.id > 0) {
      res = await khoiApi.update(submitData);
    } else {
      res = await khoiApi.insert(submitData);
    }
    if (res.is_success) {
      NotifyHelper.Success("Success");
      props.onSuccess();
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
    setIsSaving(false);
  };

  return (
    <Modal
      isOpen
      onClose={props.onClose}
      // width={"60%"}
      sx={{
        width:"60%"
      }}
      title={props.id > 0 ? "Update" : "Add new"}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="row">
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Mã khối</FormControl.Label>
              <TextInput
                block
                name="ma_khoi"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền mã khối"
              />
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Tên khối (Vi)</FormControl.Label>
              <TextInput
                block
                name="ten_khoi"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền tên khối (Vi)"
              />
            </FormControl>
          </div>

          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Tên khối (En)</FormControl.Label>
              <TextInput
                block
                name="ten_khoi_en"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền tên khối (En)"
              />
            </FormControl>
          </div>

          <div className="col-md-6 mb-3">
            <FormControl.Label>Trường</FormControl.Label>
            <div className="combobox-container">
              <ComboboxTruong
                value={formData.dm_truong_id}
                onValueChanged={handleTruongChange}
              />
            </div>
          </div>

          <div className="col-md-6 mb-3">
            <FormControl>
              <Checkbox
                {...register("is_show_campaign")}
                onChange={(e) => {
                  setValue("is_show_campaign", e.target.checked);
                  setFormData((prev) => ({
                    ...prev,
                    is_show_campaign: e.target.checked,
                  }));
                }}
                checked={!!formData.is_show_campaign}
              />
              <FormControl.Label>Show Campaign</FormControl.Label>
            </FormControl>
          </div>
        </div>

        <ModalActions>
          <Button text="Base.Label.Close" onClick={props.onClose} />
          <Button
            text="Base.Label.Save"
            variant="primary"
            type="submit"
            isLoading={isSaving}
          />
        </ModalActions>
      </form>
    </Modal>
  );
};

export default KhoiDetailModal;
