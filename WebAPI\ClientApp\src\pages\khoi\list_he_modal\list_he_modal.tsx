import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Spinner,
  Text,
  ActionList,
  Checkbox,
} from '@primer/react';
import Modal from '../../../components/ui/modal';
import ModalActions from '../../../components/ui/modal/ModalActions';
import { khoiApi } from '../../../api/khoiApi';
import { NotifyHelper } from '../../../helpers/toast';
import { IHe } from "../../../models/response/he/IHe";

interface ListHeModalProps {
  dm_khoi_selected: number;
  list_hes: IHe[];
  listHeById: IHe[];
  onCancel: () => void;
  onSuccess: (selectedHe: IHe[]) => void;
}

export const ListHeModal: React.FC<ListHeModalProps> = ({
  dm_khoi_selected,
  list_hes,
  listHeById,
  onCancel,
  onSuccess,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedHe, setSelectedHe] = useState<IHe[]>([]);

  useEffect(() => {
    setSelectedHe(listHeById);
  }, [listHeById]);

  const handleSaveChanges = async () => {
    try {
      setIsLoading(true);

      // Xác định những hệ bị xóa
      const heToDelete = listHeById.filter(
        he => !selectedHe.some(selected => selected.id === he.id)
      );

      // Nếu có hệ cần xóa, gọi API xóa trước
      if (heToDelete.length > 0) {
        const deleteRes = await khoiApi.delete_he_by_khoi_id({
          id: dm_khoi_selected,
          dm_he_ids: heToDelete.map(val => val.id),
        });
        if (!deleteRes.is_success) {
          NotifyHelper.Error(deleteRes.message ?? "Lỗi khi xóa hệ");
          return;
        }
      }

      // Sau đó cập nhật danh sách hệ mới
      const updateRes = await khoiApi.update_he_by_khoi_id({
        id: dm_khoi_selected,
        dm_he_ids: selectedHe.map(val => val.id),
      });

      if (updateRes.is_success) {
        NotifyHelper.Success("Cập nhật thành công");
        onSuccess(selectedHe);
      } else {
        NotifyHelper.Error(updateRes.message ?? "Lỗi khi cập nhật");
      }
    } catch (error) {
      NotifyHelper.Error("Có lỗi xảy ra");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleHe = (he: IHe) => {
    setSelectedHe(prev => {
      const isSelected = prev.some(item => item.id === he.id);
      if (isSelected) {
        return prev.filter(item => item.id !== he.id);
      } else {
        return [...prev, he];
      }
    });
  };

  const filteredHe = list_hes;

  const isInitialHe = (he: IHe) => listHeById.some(initial => initial.id === he.id);

  return (
    <Modal
      isOpen={true}
      onClose={onCancel}
      title={`Chọn Hệ (${selectedHe.length} đã chọn)`}
      width="large"
    >
      {/* Content */}
      <Box sx={{
        minHeight: '400px',
        maxHeight: '60vh',
        overflowY: 'auto',
        p: 3,
        backgroundColor: 'canvas.subtle'
      }}>
        <ActionList>
          {filteredHe.map(he => (
            <ActionList.Item
              key={he.id}
              sx={{
                backgroundColor: 'canvas.default',
                mb: 1,
                borderRadius: 1,
                '&:hover': {
                  backgroundColor: 'canvas.subtle'
                }
              }}
            >
              <Box sx={{
                display: 'grid',
                gridTemplateColumns: '40px 100px 1fr auto',
                alignItems: 'center',
                width: '100%',
                p: 2
              }}>
                <Checkbox
                  checked={selectedHe.some(item => item.id === he.id)}
                  onChange={() => toggleHe(he)}
                />
                <Text sx={{ color: 'fg.muted' }}>{he.ma_he}</Text>
                <Text>{he.ten_he}</Text>
                {isInitialHe(he) && (
                  <Text sx={{ fontSize: 12, color: 'success.emphasis' }}>
                    Đã chọn
                  </Text>
                )}
              </Box>
            </ActionList.Item>
          ))}
        </ActionList>
      </Box>

      {/* Footer */}
      <ModalActions>
        <Button
          variant="invisible"
          onClick={onCancel}
          disabled={isLoading}
          sx={{ mr: 2 }}
        >
          Đóng
        </Button>
        <Button
          variant="primary"
          onClick={handleSaveChanges}
          disabled={isLoading}
        >
          {isLoading ? (
            <Spinner size="small" />
          ) : (
            <>
              Lưu thay đổi
              {selectedHe.length > 0 && ` (${selectedHe.length})`}
            </>
          )}
        </Button>
      </ModalActions>
    </Modal>
  );
};