import React, { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Box,
  Button,
  FormControl,
  TextInput,
  Text,
} from "@primer/react";
import { useCommonContext } from "../../../contexts/common";
import { dm_loaigiayto } from "../../../models/response/category/dm_loaigiayto";
import { loaiGiayToApi } from "../../../api/loaiGiayToApi";
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";
import { NotifyHelper } from "../../../helpers/toast";
import Modal from "../../../components/ui/modal";

interface DetailLoaiGiayToModalProps {
  id: number;
  onSuccess: () => void;
  onCancel: () => void;
}

const defaultFormData: dm_loaigiayto = {
  id: 0,
  loai_giay_to: "",
  loai_giay_to_en: "",
  is_active: false,
  huong_dan: "",
  is_allow_multifile: false,
  is_avatar: false,
  is_batbuoc: false,
  dm_coso_id: 0,
  is_noponline: false,
  is_nopoffline: false,
};

type FormData = {
  loai_giay_to: string;
  loai_giay_to_en:string;
  huong_dan: string;
  is_active: boolean;
  is_allow_multifile: boolean;
  is_avatar: boolean;
  is_batbuoc: boolean;
  is_noponline: boolean;
  is_nopoffline: boolean;

};

const DetailLoaiGiayToModal: React.FC<DetailLoaiGiayToModalProps> = ({
  id,
  onSuccess,
  onCancel,
}) => {
  const { translate } = useCommonContext();
  const { dm_coso_id } = useCommonSelectedHook();
  const [formData, setFormData] = useState<dm_loaigiayto>(defaultFormData);
  const [isSaving, setIsSaving] = useState(false);
  const componentDidMount = useRef(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: defaultFormData,
  });

  const handleGetDetailLoaiGiayTo = useCallback(async () => {
    if (id > 0) {
      try {
        const res = await loaiGiayToApi.detail(id);
        if (!componentDidMount.current) return;
        if (res.is_success) {
          setFormData(res.data);
          Object.keys(res.data).forEach((key) => {
            setValue(key as keyof FormData, res.data[key]);
          });
        } else {
          NotifyHelper.Error(res.message ?? "");
        }
      } catch (error) {
        NotifyHelper.Error("Có lỗi xảy ra");
      }
    } else {
      setFormData({
        ...defaultFormData,
        dm_coso_id,
      });
    }
  }, [id, dm_coso_id, setValue]);

  useEffect(() => {
    componentDidMount.current = true;
    handleGetDetailLoaiGiayTo();
    return () => {
      componentDidMount.current = false;
    };
  }, [handleGetDetailLoaiGiayTo]);

  const onSubmit = async (data: FormData) => {
    setIsSaving(true);
    try {
      const submitData: dm_loaigiayto = {
        id,
        dm_coso_id,
        loai_giay_to: data.loai_giay_to,
        loai_giay_to_en: data.loai_giay_to_en,
        huong_dan: data.huong_dan || "",
        is_active: data.is_active,
        is_allow_multifile: data.is_allow_multifile,
        is_avatar: data.is_avatar,
        is_batbuoc: data.is_batbuoc,
        is_noponline: data.is_noponline,
        is_nopoffline: data.is_nopoffline,
      };

      const res =
        id > 0
          ? await loaiGiayToApi.update(submitData)
          : await loaiGiayToApi.insert(submitData);

      if (res.is_success) {
        NotifyHelper.Success("Lưu thành công");
        onSuccess();
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      NotifyHelper.Error("Có lỗi xảy ra");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Modal
      isOpen
      onClose={onCancel}
      width="large"
      title={id > 0 ? "Cập nhật" : "Thêm mới"}
      shouldCloseOnOverlayClick={false}
    >
      <Box as="form" onSubmit={handleSubmit(onSubmit)}>
        <Box>
          {/* Text Fields */}
          <FormControl required>
            <FormControl.Label sx={{ fontWeight: "semibold" }}>
              Tên giấy tờ
            </FormControl.Label>
            <TextInput
              {...register("loai_giay_to", {
                required: "Vui lòng nhập tên giấy tờ",
              })}
              placeholder="Nhập tên giấy tờ"
              block
            />
            {errors.loai_giay_to && (
              <Text sx={{ color: "danger.fg", fontSize: 12, mt: 1 }}>
                {errors.loai_giay_to.message}
              </Text>
            )}
          </FormControl>
          <FormControl required sx={{ mt: 3 }}>
            <FormControl.Label sx={{ fontWeight: "semibold" }}>
              Tên giấy tờ (En)
            </FormControl.Label>
            <TextInput
              {...register("loai_giay_to_en", {
                required: "Vui lòng nhập tên giấy tờ (En)",
              })}
              placeholder="Nhập tên giấy tờ (En)"
              block
            />
            {errors.loai_giay_to_en && (
              <Text sx={{ color: "danger.fg", fontSize: 12, mt: 1 }}>
                {errors.loai_giay_to_en.message}
              </Text>
            )}
          </FormControl>
          <FormControl sx={{ mt: 3 }}>
            <FormControl.Label sx={{ fontWeight: "semibold" }}>
              Mô tả
            </FormControl.Label>
            <TextInput
              {...register("huong_dan")}
              placeholder="Nhập mô tả"
              block
            />
          </FormControl>

          {/* Checkboxes Group */}
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "repeat(2, 1fr)",
              gap: 3,
              mt: 3,
              borderTop: "1px solid",
              borderColor: "border.default",
              pt: 3,
            }}
          >
            <FormControl>
              <label>
                <input
                  type="checkbox"
                  {...register("is_active")}
                  style={{ marginRight: "8px" }}
                />
                Đang áp dụng
              </label>
            </FormControl>

            <FormControl>
              <label>
                <input
                  type="checkbox"
                  {...register("is_noponline")}
                  style={{ marginRight: "8px" }}
                />
                Nộp Online
              </label>
            </FormControl>

            <FormControl>
              <label>
                <input
                  type="checkbox"
                  {...register("is_batbuoc")}
                  style={{ marginRight: "8px" }}
                />
                Bắt buộc
              </label>
            </FormControl>

            <FormControl>
              <label>
                <input
                  type="checkbox"
                  {...register("is_nopoffline")}
                  style={{ marginRight: "8px" }}
                />
                Nộp Offline
              </label>
            </FormControl>
          </Box>
        </Box>

        {/* Footer */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            mt:3,
            borderTop: "1px solid",
            borderColor: "border.default",
            pt: 3,
          }}
        >
          <Button onClick={onCancel}>{translate("Đóng")}</Button>
          <Button type="submit" variant="primary" disabled={isSaving}>
            {isSaving
              ? "Đang lưu..."
              : translate(id > 0 ? "Cập nhật" : "Thêm mới")}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default DetailLoaiGiayToModal;
