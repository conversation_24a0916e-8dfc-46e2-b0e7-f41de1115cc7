import React, { useEffect, useState } from "react";
import { Box, Button, Text, Dialog, TextInput } from "@primer/react";
import { SearchIcon } from "@primer/octicons-react";
import { useCommonContext } from "../../../contexts/common";
import { NotifyHelper } from "../../../helpers/toast";
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";
import { truongApi } from "../../../api/truongApi";
import { loaiGiayToApi } from "../../../api/loaiGiayToApi";
import { loaiGiayToNamHocApi } from "../../../api/loaiGiayToNamHocApi";
import { ILoaiGiayToItemRequest } from "../../../models/request/loai-giay-to-nam-hoc/ILoaiGiayToItemRequest";

interface TruongKhoiHe {
  dm_truong_id: number;
  dm_khoi_id: number;
  dm_he_id: number;
  ten_truong: string;
  ten_khoi: string;
  ten_he: string;
}

const LoaiGiayToPhamViApDung: React.FC<{ dm_loaigiayto_id: number }> = ({
  dm_loaigiayto_id,
}) => {
  const [loaiGiayTo, setLoaiGiayTo] = useState<{ loai_giay_to: string }>();
  const [truongKhoiHes, setTruongKhoiHes] = useState<TruongKhoiHe[]>([]);
  const [selectedItems, setSelectedItems] = useState<TruongKhoiHe[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isShowConfirm, setIsShowConfirm] = useState(false);
  const [filterText, setFilterText] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const { translate } = useCommonContext();
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

  useEffect(() => {
    const loadTruongKhoiHe = async () => {
      try {
        setIsLoading(true);
        const res = await truongApi.selectTruongKhoiHe();
        if (res.is_success) {
          // Lọc dữ liệu theo dm_coso_id nếu có
          if (dm_coso_id) {
            // Lấy danh sách trường thuộc cơ sở được chọn
            const truongRes = await truongApi.selectAll();
            if (truongRes.is_success) {
              const truongIds = truongRes.data
                .filter((truong: any) => truong.dm_coso_id === dm_coso_id)
                .map((truong: any) => truong.id);

              // Lọc dữ liệu TruongKhoiHe theo danh sách trường thuộc cơ sở
              const filteredData = res.data.filter((item: TruongKhoiHe) =>
                truongIds.includes(item.dm_truong_id)
              );
              setTruongKhoiHes(filteredData);
            } else {
              setTruongKhoiHes([]);
            }
          } else {
            setTruongKhoiHes(res.data);
          }
        }
      } catch (error) {
        NotifyHelper.Error("Có lỗi khi tải dữ liệu");
        setTruongKhoiHes([]);
      } finally {
        setIsLoading(false);
      }
    };
    loadTruongKhoiHe();
  }, [dm_coso_id]);

  useEffect(() => {
    const loadData = async () => {
      if (dm_loaigiayto_id <= 0) return;

      try {
        setIsLoading(true);
        const [loaiGiayToRes, selectedItemsRes] = await Promise.all([
          loaiGiayToApi.detail(dm_loaigiayto_id),
          loaiGiayToNamHocApi.Select({ nam_hoc, dm_loaigiayto_id, dm_coso_id }),
        ]);

        if (loaiGiayToRes.is_success) setLoaiGiayTo(loaiGiayToRes.data);
        if (selectedItemsRes.is_success)
          setSelectedItems(selectedItemsRes.data);
      } catch {
        NotifyHelper.Error("Có lỗi xảy ra");
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, [dm_loaigiayto_id, nam_hoc, dm_coso_id]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const request: ILoaiGiayToItemRequest = {
        nam_hoc,
        dm_coso_id,
        dm_loaigiayto_id,
        enableAll: false,
        ts_loaigiayto_namhoc: selectedItems.map((item) => ({
          id: 0, // thêm trường id
          dm_loaigiayto_id,
          dm_truong_id: item.dm_truong_id,
          dm_khoi_id: item.dm_khoi_id,
          dm_he_id: item.dm_he_id,
          nam_hoc,
        })),
      };

      const res = await loaiGiayToNamHocApi.Inserts(request);
      if (res.is_success) {
        NotifyHelper.Success("Cập nhật thành công");
        setIsShowConfirm(false);
      } else {
        NotifyHelper.Error(res.message || "Có lỗi xảy ra");
      }
    } catch {
      NotifyHelper.Error("Có lỗi xảy ra");
    } finally {
      setIsSaving(false);
    }
  };

  const filteredData = truongKhoiHes.filter(
    (item) =>
      !filterText ||
      [item.ten_truong, item.ten_khoi, item.ten_he].some((text) =>
        text.toLowerCase().includes(filterText.toLowerCase())
      )
  );

  const isSelected = (item: TruongKhoiHe) =>
    selectedItems.some(
      (x) =>
        x.dm_truong_id === item.dm_truong_id &&
        x.dm_khoi_id === item.dm_khoi_id &&
        x.dm_he_id === item.dm_he_id
    );

  if (!loaiGiayTo) {
    return (
      <Box p={4} textAlign="center">
        <Text>
          Vui lòng chọn 1 loại giấy tờ để xem và điều chỉnh phạm vi áp dụng
        </Text>
      </Box>
    );
  }

  return (
    <Box>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p={3}
        borderBottom="1px solid"
        borderColor="border.default"
      >
        <Text>
          Phạm vi áp dụng của{" "}
          <Text fontWeight="bold">{loaiGiayTo.loai_giay_to}</Text>
        </Text>
        <Button
          variant="primary"
          onClick={() => setIsShowConfirm(true)}
          disabled={isSaving}
        >
          Cập nhật
        </Button>
      </Box>

      <Box p={3}>
        <TextInput
          leadingVisual={SearchIcon}
          placeholder="Tìm kiếm..."
          value={filterText}
          onChange={(e) => setFilterText(e.target.value)}
          sx={{ width: "100%", mb: 3 }}
        />

        <Box sx={{ border: "1px solid #d0d7de" }}>
          <table
            style={{
              width: "100%",
              borderCollapse: "collapse",
              tableLayout: "fixed",
            }}
          >
            <colgroup>
              <col style={{ width: "120px" }} />
              <col style={{ width: "40%" }} />
              <col style={{ width: "30%" }} />
              <col style={{ width: "30%" }} />
            </colgroup>
            <thead style={{ backgroundColor: "#f6f8fa" }}>
              <tr>
                <th
                  style={{
                    padding: "12px 16px",
                    borderBottom: "1px solid #d0d7de",
                    textAlign: "left",
                  }}
                >
                  <Text>Đang sử dụng</Text>
                </th>
                <th
                  style={{
                    padding: "12px 16px",
                    borderBottom: "1px solid #d0d7de",
                    textAlign: "left",
                  }}
                >
                  Tên trường
                </th>
                <th
                  style={{
                    padding: "12px 16px",
                    borderBottom: "1px solid #d0d7de",
                    textAlign: "left",
                  }}
                >
                  Tên khối
                </th>
                <th
                  style={{
                    padding: "12px 16px",
                    borderBottom: "1px solid #d0d7de",
                    textAlign: "left",
                  }}
                >
                  Tên hệ
                </th>
              </tr>
            </thead>
          </table>
          <Box sx={{ maxHeight: "calc(100vh - 320px)", overflowY: "auto" }}>
            <table
              style={{
                width: "100%",
                borderCollapse: "collapse",
                tableLayout: "fixed",
              }}
            >
              <colgroup>
                <col style={{ width: "120px" }} />
                <col style={{ width: "40%" }} />
                <col style={{ width: "30%" }} />
                <col style={{ width: "30%" }} />
              </colgroup>
              <tbody>
                <tr>
                  <td
                    style={{
                      padding: "12px 16px",
                      borderBottom: "1px solid #d0d7de",
                      backgroundColor: "#f6f8fa",
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={selectedItems.length === filteredData.length && filteredData.length > 0}
                      onChange={(e) =>
                        setSelectedItems(e.target.checked ? filteredData : [])
                      }
                    />
                  </td>
                  <td
                    colSpan={3}
                    style={{
                      padding: "12px 16px",
                      borderBottom: "1px solid #d0d7de",
                      backgroundColor: "#f6f8fa",
                      textAlign: "left",
                    }}
                  >
                    <Text>Chọn tất cả</Text>
                  </td>
                </tr>
                {isLoading ? (
                  <tr>
                    <td colSpan={4} style={{ padding: "20px", textAlign: "center" }}>
                      <Text>Đang tải dữ liệu...</Text>
                    </td>
                  </tr>
                ) : filteredData.length === 0 ? (
                  <tr>
                    <td colSpan={4} style={{ padding: "20px", textAlign: "center" }}>
                      <Text>Không có dữ liệu</Text>
                    </td>
                  </tr>
                ) : (
                  filteredData.map((item) => (
                    <tr
                      key={`${item.dm_truong_id}-${item.dm_khoi_id}-${item.dm_he_id}`}
                    >
                      <td
                        style={{
                          padding: "12px 16px",
                          borderBottom: "1px solid #d0d7de",
                        }}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected(item)}
                          onChange={(e) =>
                            setSelectedItems((prev) =>
                              e.target.checked
                                ? [...prev, item]
                                : prev.filter(
                                    (x) =>
                                      x.dm_truong_id !== item.dm_truong_id ||
                                      x.dm_khoi_id !== item.dm_khoi_id ||
                                      x.dm_he_id !== item.dm_he_id
                                  )
                            )
                          }
                        />
                      </td>
                      <td
                        style={{
                          padding: "12px 16px",
                          borderBottom: "1px solid #d0d7de",
                          textAlign: "left",
                        }}
                      >
                        {item.ten_truong}
                      </td>
                      <td
                        style={{
                          padding: "12px 16px",
                          borderBottom: "1px solid #d0d7de",
                          textAlign: "left",
                        }}
                      >
                        {item.ten_khoi}
                      </td>
                      <td
                        style={{
                          padding: "12px 16px",
                          borderBottom: "1px solid #d0d7de",
                          textAlign: "left",
                        }}
                      >
                        {item.ten_he}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </Box>
        </Box>
      </Box>

      <Dialog isOpen={isShowConfirm} onDismiss={() => setIsShowConfirm(false)}>
        <Dialog.Header>{translate("Xác nhận")}</Dialog.Header>
        <Box p={3}>
          <Text>
            {translate("Bạn có chắc chắn muốn cập nhật phạm vi áp dụng ?")}
          </Text>
        </Box>
        <Box
          display="flex"
          justifyContent="flex-end"
          p={3}
          borderTop="1px solid"
          borderColor="border.default"
        >
          <Button onClick={() => setIsShowConfirm(false)}>
            {translate("Đóng")}
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={isSaving}
            sx={{ ml: 2 }}
          >
            {translate("Cập nhật")}
          </Button>
        </Box>
      </Dialog>
    </Box>
  );
};

export default LoaiGiayToPhamViApDung;
