import {
  KebabHorizontalIcon,
  PencilIcon,
  PlusIcon,
  SyncIcon,
  TrashIcon,
} from "@primer/octicons-react";
import {
  ActionList,
  ActionMenu,
  Box,
  IconButton,
  useConfirm,
} from "@primer/react";
import { useEffect, useState } from "react";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";

import { crmLopApi } from "../../api/crmLopApi";
import { ComboboxTruong } from "../../components/combobox-truong";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { ILopItemResponse } from "../../models/response/crm-lop/ILopItemResponse";
import LopDetailModal from "./LopDetailModal";

const Lop = () => {
  const [isShowModal, setIsShowModal] = useState(false);
  const [selectedId, setSelectedId] = useState(0);
  const [Lops, setLops] = useState<ILopItemResponse[]>([]);
  const [filteredLops, setFilteredLops] = useState<ILopItemResponse[]>([]);
  const { translate } = useCommonContext();
  const [dm_truong_selected_id, setDmTruongSelectedId] = useState<number>(0);
  const confirm = useConfirm();
  const [isLoading, setIsLoading] = useState(false);
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

  useEffect(() => {
    handleReloadAsync();
  }, [nam_hoc, dm_coso_id]);

  useEffect(() => {
    if (dm_truong_selected_id) {
      const filtered = Lops.filter(
        (Lop) => Lop.dm_truong_id === dm_truong_selected_id
      );
      setFilteredLops(filtered);
    } else {
      setFilteredLops(Lops);
    }
  }, [dm_truong_selected_id, Lops]);

  const handleReloadAsync = async () => {
    try {
      setIsLoading(true);
      const res = await crmLopApi.selectByNamHoc(nam_hoc);
      if (res.is_success) {
        if (dm_coso_id) {
          // Filter lớp by dm_coso_id
          const filteredData = res.data.filter(
            (lop: ILopItemResponse) => lop.dm_coso_id === dm_coso_id
          );
          setLops(filteredData);
          setFilteredLops(filteredData);
        } else {
          setLops(res.data);
          setFilteredLops(res.data);
        }
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    } finally {
      setIsLoading(false);
    }
  };

  const handeDelete = async (id: number) => {
    if (
      await confirm({
        content: "Bạn có chắc chắn muốn xóa lớp này?",
        title: "Lưu ý",
        cancelButtonContent: "Không xóa",
        confirmButtonContent: "Xóa lớp",
        confirmButtonType: "danger",
      })
    ) {
      try {
        const res = await crmLopApi.delete(id);
        if (res.is_success) {
          NotifyHelper.Success("Success");
          handleReloadAsync();
        } else {
          NotifyHelper.Error(res.message ?? "");
        }
      } catch (error) {
        NotifyHelper.Error("Có lỗi xảy ra khi xóa");
      }
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <DataTable
        height={`${window.innerHeight - 250}px`}
        actionComponent={
          <Box
            sx={{
              display: "flex",
              gap: 2,
            }}
          >
            <ComboboxTruong
              value={dm_truong_selected_id}
              onValueChanged={(value) => {
                setDmTruongSelectedId(value);
              }}
              isShowClearButton={true}
            />
            <Button
              text="Base.Label.Refresh"
              leadingVisual={SyncIcon}
              size="medium"
              onClick={handleReloadAsync}
              disabled={isLoading}
            />
            <Button
              text={translate("Base.Label.Create")}
              leadingVisual={PlusIcon}
              variant="primary"
              size="medium"
              onClick={() => {
                setIsShowModal(true);
                setSelectedId(0);
              }}
              disabled={isLoading}
            />
          </Box>
        }
        columns={[
          {
            id: "cmd",
            width: "50px",
            caption: "#",
            align: "center",
            cellRender: (data: ILopItemResponse) => {
              return (
                <ActionMenu>
                  <ActionMenu.Anchor>
                    <IconButton
                      icon={KebabHorizontalIcon}
                      variant="invisible"
                      aria-label="Open column options"
                    />
                  </ActionMenu.Anchor>

                  <ActionMenu.Overlay>
                    <ActionList>
                      <ActionList.Item
                        onSelect={() => {
                          setIsShowModal(true);
                          if (data.id !== undefined) {
                            setSelectedId(data.id);
                          }
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <PencilIcon />
                        </ActionList.LeadingVisual>
                        <TextTranslated value="Base.Label.Edit" />
                      </ActionList.Item>
                      <ActionList.Divider />
                      <ActionList.Item
                        variant="danger"
                        onSelect={() => {
                          if (data.id !== undefined) {
                            handeDelete(data.id);
                          }
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <TrashIcon />
                        </ActionList.LeadingVisual>
                        <TextTranslated value="Base.Label.Delete" />
                      </ActionList.Item>
                    </ActionList>
                  </ActionMenu.Overlay>
                </ActionMenu>
              );
            },
          },
          {
            dataField: "ten_lop",
            width: "auto",
            caption: `Tên lớp`,
            isMainColumn: true,
            cellRender: (data: ILopItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_lop}
                </Box>
              );
            },
          },
          {
            dataField: "ten_truong",
            width: "auto",
            caption: `Trường`,
            cellRender: (data: ILopItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_truong || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "ten_khoi",
            width: "auto",
            caption: `Khối`,
            cellRender: (data: ILopItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_khoi || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "ten_he",
            width: "auto",
            caption: `Hệ`,
            cellRender: (data: ILopItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_he || "..."}
                </Box>
              );
            },
          },
        ]}
        data={filteredLops}
        title="Lớp"
        subTitle={`${translate("Base.Label.TotalCount")}: ${
          filteredLops.length
        } ${dm_coso_id ? "(Đã lọc theo cơ sở)" : ""}`}
        searchEnable
      />
      {isShowModal && (
        <LopDetailModal
          dm_truong_id={dm_truong_selected_id}
          id={selectedId}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReloadAsync();
            setIsShowModal(false);
          }}
        />
      )}
    </Box>
  );
};

export default Lop;
