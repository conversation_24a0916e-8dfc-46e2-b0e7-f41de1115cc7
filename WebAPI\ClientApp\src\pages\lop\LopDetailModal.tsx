import { FormControl } from "@primer/react";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { crmLopApi } from "../../api/crmLopApi";
import ComboboxHe from "../../components/combobox-he";
import ComboboxKhoi from "../../components/combobox-khoi";
import { ComboboxTruong } from "../../components/combobox-truong";
import Button from "../../components/ui/button";
import Modal from "../../components/ui/modal";
import ModalActions from "../../components/ui/modal/ModalActions";
import TextInput from "../../components/ui/text-input";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { ILop } from "../../models/response/crm-lop/ILop";

interface ILopDetailModalProps {
  dm_truong_id?: number;
  id: number;
  onClose: () => void;
  onSuccess: () => void;
}
const LopDetailModal = (props: ILopDetailModalProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { translate } = useCommonContext();
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

  const [formData, setFormData] = useState<ILop>({
    id: props.id,
    nam_hoc: nam_hoc,
    dm_truong_id: props.dm_truong_id || 0,
    dm_khoi_id: 0,
    dm_he_id: 0,
    ten_lop: "",
    sis_thongtu_id: 0,
    sis_sonhanxet_dinhky_id: 0,
    sis_sonhanxet_thuongxuyen_id: 0,

    ten_lop_en: "",
    tk_ketoan:""
  });

  const {
    register,
    handleSubmit,
    clearErrors,
    reset,
    setError,
    control,
    setValue,
    formState: { errors },
  } = useForm<ILop>({
    defaultValues:formData,
  });

  useEffect(() => {
    if (props.id > 0) {
      handleGetVm();
    } else {
      if (props.dm_truong_id) {
        setValue("dm_truong_id", props.dm_truong_id);
        setFormData(prev => ({
          ...prev,
          dm_truong_id: props.dm_truong_id || 0
        }));
      }
    }
  }, [props.id, props.dm_truong_id]);

  const handleGetVm = async () => {
    const res = await crmLopApi.selectById(props.id);
    if (res.is_success) {
      const data: ILop = {
        ...res.data,
      };
      setFormData(data);
      reset(data);
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
  };
  const handleTruongChange = (value: number) => {
    setValue("dm_truong_id", value);
    // Reset khoi and he when truong changes
    setValue("dm_khoi_id", 0);
    setValue("dm_he_id", 0);
    setFormData((prev) => ({
      ...prev,
      dm_truong_id: value,
      dm_khoi_id: 0,
      dm_he_id: 0,
    }));
  };

  const handleKhoiChange = (value: number) => {
    setValue("dm_khoi_id", value);
    // Reset he when khoi changes
    setValue("dm_he_id", 0);
    setFormData((prev) => ({
      ...prev,
      dm_khoi_id: value,
      dm_he_id: 0,
    }));
  };

  const onSubmit = async (data: any) => {
    setIsSaving(true);
    let res: any;
    if (props.id > 0) {
      res = await crmLopApi.update({
        ...data,
        id: props.id,
        nam_hoc: nam_hoc
      });
    } else {
      res = await crmLopApi.insert({
        ...data,
        id: props.id,
        nam_hoc: nam_hoc
      });
    }
    if (res.is_success) {
      NotifyHelper.Success("Success");
      props.onSuccess();
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
    setIsSaving(false);
  };

  return (
    <Modal
      isOpen
      onClose={props.onClose}
      // width={"60%"}
      sx={{
        width:"60%"
      }}
      title={props.id > 0 ? "Cập nhật" : "Thêm mới"}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="row">
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Trường học</FormControl.Label>
              <Controller
                name="dm_truong_id"
                control={control}
                rules={{ required: "Vui lòng Chọn cấp học" }}
                render={({ field }) => (
                  <ComboboxTruong
                    value={field.value}
                    onValueChanged={(value) => {
                      field.onChange(value);
                      setFormData((prev) => ({
                        ...prev,
                        dm_truong_id: value,
                      }));
                    }}
                  />
                )}
              />
              {errors.dm_truong_id && (
                <FormControl.Validation variant="error">
                  {errors.dm_truong_id.message}
                </FormControl.Validation>
              )}
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Khối học</FormControl.Label>
              <Controller
                name="dm_khoi_id"
                control={control}
                rules={{ required: "Vui lòng chọn khối" }}
                render={({ field }) => (
                  <ComboboxKhoi
                    dm_truong_id={formData.dm_truong_id}
                    value={field.value}
                    onValueChanged={(value) => {
                      field.onChange(value);
                      handleKhoiChange(value);
                    }}
                  />
                )}
              />
              {errors.dm_khoi_id && (
                <FormControl.Validation variant="error">
                  {errors.dm_khoi_id.message}
                </FormControl.Validation>
              )}
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Hệ</FormControl.Label>
              <Controller
                name="dm_he_id"
                control={control}
                rules={{ required: "Vui lòng chọn hệ" }}
                render={({ field }) => (
                  <ComboboxHe
                    value={field.value}
                    dm_khoi_id={formData.dm_khoi_id}
                    onValueChanged={(value) => {
                      field.onChange(value);
                      setFormData((prev) => ({
                        ...prev,
                        dm_he_id: value,
                      }));
                    }}
                  />
                )}
              />
              {errors.dm_he_id && (
                <FormControl.Validation variant="error">
                  {errors.dm_he_id.message}
                </FormControl.Validation>
              )}
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Tên lớp</FormControl.Label>
              <TextInput
                block
                name="ten_lop"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền tên lớp"
              />
            </FormControl>
          </div>

        </div>

        <ModalActions>
          <Button text="Base.Label.Close" onClick={props.onClose} />
          <Button
            text="Base.Label.Save"
            variant="primary"
            type="submit"
            isLoading={isSaving}
          />
        </ModalActions>
      </form>
    </Modal>
  );
};

export default LopDetailModal;
