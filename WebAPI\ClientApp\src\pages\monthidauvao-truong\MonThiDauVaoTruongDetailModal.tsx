import { FormControl } from "@primer/react";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Button from "../../components/ui/button";
import Modal from "../../components/ui/modal";
import ModalActions from "../../components/ui/modal/ModalActions";

import { NotifyHelper } from "../../helpers/toast";
import { monThiDauVaoTruongApi } from "../../api/monThiDauVaoTruongApi";
import { ex_monthidauvao_truong, MonThiDauVaoTruongItemResponse } from "../../models/response/quan-ly-thi/ex_monthidauvao_truong";
import { ComboboxMonThiDauVao } from "../../components/combobox";
import { ComboboxTruong } from "../../components/combobox-truong";

interface IMonThiDauVaoTruongDetailModalProps {
  id: number;
  dm_truong_id?: number; // Thêm prop dm_truong_id
  onClose: () => void;
  onSuccess: () => void;
}

const MonThiDauVaoTruongDetailModal = (props: IMonThiDauVaoTruongDetailModalProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const [allMonThi, setAllMonThi] = useState<MonThiDauVaoTruongItemResponse[]>([]);
  const [isDuplicate, setIsDuplicate] = useState(false);

  // Khởi tạo giá trị mặc định cho form

  const {
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm<ex_monthidauvao_truong>({
    defaultValues: {}
  });

  // Tải tất cả các môn thi đầu vào khi component được tạo
  useEffect(() => {
    const loadAllMonThi = async () => {
      try {
        const res = await monThiDauVaoTruongApi.selectAll();
        if (res.is_success) {
          setAllMonThi(res.data || []);
        }
      } catch (error) {
        console.error("Lỗi khi tải danh sách môn thi đầu vào:", error);
      }
    };

    loadAllMonThi();
  }, []);

  useEffect(() => {
    if (props.id > 0) {
      handleGetVm();
    } else {
      // Nếu là thêm mới, thiết lập giá trị mặc định cho dm_truong_id
      reset({ dm_truong_id: props.dm_truong_id });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.id, props.dm_truong_id]);

  const handleGetVm = async () => {
    try {
      const res = await monThiDauVaoTruongApi.selectById(props.id);
      if (res.is_success) {
        const data: ex_monthidauvao_truong = {
          ...res.data,
        };
        reset(data);
      } else {
        NotifyHelper.Error(res?.data?.message || "Lỗi khi tải dữ liệu");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    }
  };

  // Kiểm tra trùng lặp môn thi
  const checkDuplicate = (ex_monthidauvao_id: number, dm_truong_id: number) => {
    if (!ex_monthidauvao_id || !dm_truong_id) {
      setIsDuplicate(false);
      return false;
    }

    const existingMonThi = allMonThi.filter(
      (item) =>
        item.dm_truong_id === dm_truong_id &&
        item.ex_monthidauvao_id === ex_monthidauvao_id &&
        item.id !== props.id // Bỏ qua bản ghi hiện tại khi đang cập nhật
    );

    const isDuplicated = existingMonThi.length > 0;
    setIsDuplicate(isDuplicated);
    return isDuplicated;
  };

  const onSubmit = async (data: any) => {
    setIsSaving(true);
    try {
      // Kiểm tra trùng lặp môn thi khi thêm mới hoặc cập nhật
      if (checkDuplicate(data.ex_monthidauvao_id, data.dm_truong_id)) {
        NotifyHelper.Error("Môn thi này đã tồn tại cho trường đã chọn. Vui lòng chọn môn thi khác.");
        setIsSaving(false);
        return;
      }

      // Tiếp tục lưu nếu không có trùng lặp
      let res: any;
      if (props.id > 0) {
        res = await monThiDauVaoTruongApi.update({
          ...data,
          id: props.id,
        });
      } else {
        res = await monThiDauVaoTruongApi.insert({
          ...data,
          id: 0, // Đảm bảo id = 0 khi thêm mới
        });
      }
      if (res.is_success) {
        NotifyHelper.Success("Lưu thành công");
        props.onSuccess();
      } else {
        NotifyHelper.Error(res?.data?.message || "Lỗi khi lưu dữ liệu");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi lưu dữ liệu");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Modal
      isOpen
      onClose={props.onClose}
      sx={{
        width: "500px"
      }}
      title={props.id > 0 ? "Cập nhật môn thi đầu vào - trường" : "Thêm mới môn thi đầu vào - trường"}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="row">
          <div className="col-md-12 mb-3">
            <FormControl>
              <FormControl.Label>Môn thi đầu vào</FormControl.Label>
              <Controller
                name="ex_monthidauvao_id"
                control={control}
                rules={{ required: true }}
                render={({ field }) => (
                  <ComboboxMonThiDauVao
                    value={field.value}
                    onValueChanged={(value) => {
                      field.onChange(value);
                      // Lấy giá trị hiện tại của dm_truong_id
                      const dm_truong_id = control._formValues.dm_truong_id;
                      if (value && dm_truong_id) {
                        checkDuplicate(value, dm_truong_id);
                      }
                    }}
                    isShowClearButton={true}
                  />
                )}
              />
              {errors.ex_monthidauvao_id && (
                <FormControl.Validation variant="error">
                  Vui lòng chọn môn thi đầu vào
                </FormControl.Validation>
              )}
              {isDuplicate && (
                <FormControl.Validation variant="error">
                  Môn thi này đã tồn tại cho trường đã chọn
                </FormControl.Validation>
              )}
            </FormControl>
          </div>
          <div className="col-md-12 mb-3">
            <FormControl>
              <FormControl.Label>Trường</FormControl.Label>
              <Controller
                name="dm_truong_id"
                control={control}
                rules={{ required: true }}
                render={({ field }) => (
                  <ComboboxTruong
                    value={field.value}
                    onValueChanged={(value) => {
                      field.onChange(value);
                      // Lấy giá trị hiện tại của ex_monthidauvao_id
                      const ex_monthidauvao_id = control._formValues.ex_monthidauvao_id;
                      if (value && ex_monthidauvao_id) {
                        checkDuplicate(ex_monthidauvao_id, value);
                      }
                    }}
                    isShowClearButton={true}
                  />
                )}
              />
              {errors.dm_truong_id && (
                <FormControl.Validation variant="error">
                  Vui lòng chọn trường
                </FormControl.Validation>
              )}
            </FormControl>
          </div>
        </div>

        <ModalActions>
          <Button text="Base.Label.Close" onClick={props.onClose} />
          <Button
            text="Base.Label.Save"
            variant="primary"
            type="submit"
            isLoading={isSaving}
            disabled={isDuplicate}
          />
        </ModalActions>
      </form>
    </Modal>
  );
};

export default MonThiDauVaoTruongDetailModal;
