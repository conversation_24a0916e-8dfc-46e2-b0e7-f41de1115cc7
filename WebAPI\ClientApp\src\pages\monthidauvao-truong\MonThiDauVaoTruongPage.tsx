import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  Checkbox,
} from "@primer/react";
import {
  SyncIcon,
} from "@primer/octicons-react";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import MyButton from "../../components/ui/button/Button";
import DataTable from "../../components/ui/data-table";
import { monThiDauVaoTruongApi } from "../../api/monThiDauVaoTruongApi";
import { monThiDauVaoApi } from "../../api/monThiDauVaoApi";
import { truongApi } from "../../api/truongApi";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { MonThiDauVaoTruongItemResponse } from "../../models/response/quan-ly-thi/ex_monthidauvao_truong";
import { MonThiDauVaoItemResponse } from "../../models/response/quan-ly-thi/ex_monthidauvao";

interface Truong {
  id: number;
  ten_truong: string;
  ten_truong_en?: string;
  created_user?: string;
  last_modified_user?: string;
}

// Interface để hiển thị môn thi đầu vào với trạng thái đã chọn
interface MonThiDauVaoWithSelection extends MonThiDauVaoItemResponse {
  isSelected: boolean;
  relationId?: number; // ID của bản ghi trong ex_monthidauvao_truong nếu đã được gán
}

const MonThiDauVaoTruongPage: React.FC = () => {
  // Loading states
  const [isLoadingTruongs, setIsLoadingTruongs] = useState(false);
  const [isLoadingMonThi, setIsLoadingMonThi] = useState(false);

  // Data states
  const [truongs, setTruongs] = useState<Truong[]>([]);
  const [allMonThiDauVao, setAllMonThiDauVao] = useState<MonThiDauVaoItemResponse[]>([]);
  const [monThiDauVaoWithSelection, setMonThiDauVaoWithSelection] = useState<MonThiDauVaoWithSelection[]>([]);

  // Selection states
  const [selectedTruong, setSelectedTruong] = useState<Truong | null>(null);

  const {  dm_coso_id } = useCommonSelectedHook();

  const { translate } = useCommonContext();

  // Load danh sách tất cả môn thi đầu vào
  const loadAllMonThiDauVao = useCallback(async () => {
    try {
      const res = await monThiDauVaoApi.selectAll();
      if (res.is_success) {
        setAllMonThiDauVao(res.data || []);
      } else {
        NotifyHelper.Error(res.message || "Có lỗi khi tải dữ liệu môn thi đầu vào");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi khi tải dữ liệu môn thi đầu vào");
    }
  }, []);

  // Load danh sách trường
  const loadTruongs = useCallback(async () => {
    setIsLoadingTruongs(true);
    try {
      const res = await truongApi.selectAll();
      if (res.is_success) {
        // Lọc danh sách trường theo dm_coso_id
        const filteredData = dm_coso_id
          ? res.data.filter((item: any) => item.dm_coso_id === dm_coso_id)
          : res.data;
        setTruongs(filteredData || []);
      } else {
        NotifyHelper.Error(res.message || "Có lỗi khi tải dữ liệu trường");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi khi tải dữ liệu trường");
    } finally {
      setIsLoadingTruongs(false);
    }
  }, [dm_coso_id, setIsLoadingTruongs, setTruongs]);

  // Load danh sách môn thi đầu vào của một trường và tạo dữ liệu với checkbox
  const loadMonThiDauVaoByTruong = useCallback(async (truongId: number) => {
    if (!truongId || allMonThiDauVao.length === 0) return;

    setIsLoadingMonThi(true);
    try {
      const res = await monThiDauVaoTruongApi.selectAll();
      if (res.is_success) {
        // Lọc các môn thi đầu vào của trường được chọn
        const assignedMonThi = res.data.filter((item: MonThiDauVaoTruongItemResponse) =>
          item.dm_truong_id === truongId
        );

        // Tạo danh sách tất cả môn thi đầu vào với trạng thái đã chọn
        const monThiWithSelection: MonThiDauVaoWithSelection[] = allMonThiDauVao.map(monThi => {
          const assignedItem = assignedMonThi.find((assigned: MonThiDauVaoTruongItemResponse) => assigned.ex_monthidauvao_id === monThi.id);
          return {
            ...monThi,
            isSelected: !!assignedItem,
            relationId: assignedItem?.id
          };
        });
        setMonThiDauVaoWithSelection(monThiWithSelection);
      } else {
        NotifyHelper.Error(res?.data?.message || "Có lỗi khi tải dữ liệu môn thi đầu vào");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi khi tải dữ liệu môn thi đầu vào");
    } finally {
      setIsLoadingMonThi(false);
    }
  }, [allMonThiDauVao]);

  // Xử lý khi chọn một trường
  const handleSelectTruong = (truong: Truong) => {
    setSelectedTruong(truong);
    loadMonThiDauVaoByTruong(truong.id);
  };

  // Xử lý khi tick/untick checkbox môn thi
  const handleMonThiCheckboxChange = async (monThi: MonThiDauVaoWithSelection, isChecked: boolean) => {
    if (!selectedTruong) return;

    try {
      if (isChecked) {
        // Thêm môn thi cho trường
        const res = await monThiDauVaoTruongApi.insert({
          id: 0,
          ex_monthidauvao_id: monThi.id,
          dm_truong_id: selectedTruong.id
        });
        if (res.is_success) {
          NotifyHelper.Success("Đã thêm môn thi cho trường");
          // Reload dữ liệu
          loadMonThiDauVaoByTruong(selectedTruong.id);
        } else {
          NotifyHelper.Error(res?.data?.message || "Có lỗi khi thêm môn thi");
        }
      } else {
        // Xóa môn thi khỏi trường
        if (monThi.relationId) {
          const res = await monThiDauVaoTruongApi.delete(monThi.relationId);
          if (res.is_success) {
            NotifyHelper.Success("Đã xóa môn thi khỏi trường");
            // Reload dữ liệu
            loadMonThiDauVaoByTruong(selectedTruong.id);
          } else {
            NotifyHelper.Error(res?.data?.message || "Có lỗi khi xóa môn thi");
          }
        }
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra");
    }
  };

  useEffect(() => {
    loadTruongs();
    loadAllMonThiDauVao();
  }, [loadTruongs, loadAllMonThiDauVao]);

  // Reload môn thi khi allMonThiDauVao thay đổi và có trường được chọn
  useEffect(() => {
    if (selectedTruong && allMonThiDauVao.length > 0) {
      loadMonThiDauVaoByTruong(selectedTruong.id);
    }
  }, [allMonThiDauVao, selectedTruong, loadMonThiDauVaoByTruong]);



  return (
    <Box p={3}>
      <Box display="flex" sx={{ height: "calc(100vh - 240px)", gap: 3 }}>
        {/* Bảng danh sách trường */}
        <Box flex="1" minWidth="0">
          <DataTable
            height="calc(100vh - 250px)"
            data={truongs}
            isLoading={isLoadingTruongs}
            title="Môn thi đầu vào - Trường"
            paging={{
              enable: true,
              pageSizeItems: [20, 50, 200, 500, 1000],
            }}
            selection={{
              mode: "single",
              selectedRowKeys: selectedTruong ? [selectedTruong.id] : [],
              onSelectionChanged: (keys: number[]) => {
                const selected = truongs.find((item) => item.id === keys[0]);
                if (selected) {
                  handleSelectTruong(selected);
                }
              },
            }}
            actionComponent={
              <Box sx={{ display: "flex" }}>
                <MyButton
                  text="Base.Label.Refresh"
                  leadingVisual={SyncIcon}
                  size="medium"
                  onClick={loadTruongs}
                />
              </Box>
            }
            columns={[
              {
                dataField: "ten_truong",
                caption: translate("Trường"),
                isMainColumn: true,
              }
            ]}
          />
        </Box>

        {/* Bảng danh sách môn thi đầu vào với checkbox */}
        <Box flex="1" minWidth="0">
          <DataTable
            height="calc(100vh - 250px)"
            data={monThiDauVaoWithSelection}
            isLoading={isLoadingMonThi}
            title={selectedTruong ? `${selectedTruong.ten_truong}` : "Chọn trường để xem môn thi đầu vào"}
            paging={{
              enable: true,
              pageSizeItems: [20, 50, 200, 500, 1000],
            }}
            actionComponent={
              <Box sx={{ display: "flex" }}>
                <MyButton
                  text="Base.Label.Refresh"
                  leadingVisual={SyncIcon}
                  size="medium"
                  onClick={() => {
                    loadAllMonThiDauVao();
                    if (selectedTruong) {
                      loadMonThiDauVaoByTruong(selectedTruong.id);
                    }
                  }}
                />
              </Box>
            }
            columns={[
              {
                id: "checkbox",
                caption: "",
                width: "60px",
                align: "center",
                cellRender: (data: MonThiDauVaoWithSelection) => (
                  <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <Checkbox
                      checked={data.isSelected}
                      disabled={!selectedTruong}
                      onChange={(e) => handleMonThiCheckboxChange(data, e.target.checked)}
                    />
                  </Box>
                ),
              },
              {
                dataField: "mon_thi",
                caption: translate("Môn thi"),
                isMainColumn: true,
              }
            ]}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default MonThiDauVaoTruongPage;
