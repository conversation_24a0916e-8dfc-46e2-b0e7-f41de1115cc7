import { Box, FormControl } from "@primer/react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Button from "../../components/ui/button";
import Modal from "../../components/ui/modal";
import ModalActions from "../../components/ui/modal/ModalActions";
import TextInput from "../../components/ui/text-input";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { monThiDauVaoApi } from "../../api/monThiDauVaoApi";
import { ex_monthidauvao } from "../../models/response/quan-ly-thi/ex_monthidauvao";

interface IMonThiDauVaoDetailModalProps {
  id: number;
  onClose: () => void;
  onSuccess: () => void;
}

const MonThiDauVaoDetailModal = (props: IMonThiDauVaoDetailModalProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { translate } = useCommonContext();

  const [formData, setFormData] = useState<ex_monthidauvao>({
    id: props.id,
    mon_thi: "",
    mon_thi_en: "",
  });

  const {
    register,
    handleSubmit,
    clearErrors,
    reset,
    setError,
    control,
    formState: { errors },
  } = useForm<ex_monthidauvao>({
    defaultValues: {},
  });

  useEffect(() => {
    if (props.id > 0) {
      handleGetVm();
    }
  }, [props.id]);

  const handleGetVm = async () => {
    try {
      const res = await monThiDauVaoApi.selectById(props.id);
      if (res.is_success) {
        const data: ex_monthidauvao = {
          ...res.data,
        };
        setFormData(data);
        reset(data);
      } else {
        NotifyHelper.Error(res?.data?.message || "Lỗi khi tải dữ liệu");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    }
  };

  const onSubmit = async (data: any) => {
    setIsSaving(true);
    try {
      let res: any;
      if (props.id > 0) {
        res = await monThiDauVaoApi.update({
          ...data,
          id: props.id,
        });
      } else {
        res = await monThiDauVaoApi.insert({
          ...data,
          id: 0, // Đảm bảo id = 0 khi thêm mới
        });
      }
      if (res.is_success) {
        NotifyHelper.Success("Lưu thành công");
        props.onSuccess();
      } else {
        NotifyHelper.Error(res?.data?.message || "Lỗi khi lưu dữ liệu");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi lưu dữ liệu");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Modal
      isOpen
      onClose={props.onClose}
      sx={{
        width: "500px"
      }}
      title={props.id > 0 ? "Cập nhật môn thi đầu vào" : "Thêm mới môn thi đầu vào"}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="row">
          <div className="col-md-12 mb-3">
            <FormControl>
              <FormControl.Label>Môn thi</FormControl.Label>
              <TextInput
                block
                name="mon_thi"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng nhập môn thi"
              />
            </FormControl>
          </div>
          <div className="col-md-12 mb-3">
            <FormControl>
              <FormControl.Label>Môn thi (Tiếng Anh)</FormControl.Label>
              <TextInput
                block
                name="mon_thi_en"
                register={register}
                errors={errors}
                validateMessage="Vui lòng nhập môn thi tiếng Anh"
              />
            </FormControl>
          </div>
        </div>

        <ModalActions>
          <Button text="Base.Label.Close" onClick={props.onClose} />
          <Button
            text="Base.Label.Save"
            variant="primary"
            type="submit"
            isLoading={isSaving}
          />
        </ModalActions>
      </form>
    </Modal>
  );
};

export default MonThiDauVaoDetailModal;
