import { FileDirectoryOpenFillIcon, StackIcon } from "@primer/octicons-react";
import { Box, UnderlineNav } from "@primer/react";
import { useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";

import MonThiDauVaoPage from "./MonThiDauVaoPage";
import MonThiDauVaoTruongPage from "../monthidauvao-truong/MonThiDauVaoTruongPage";

const MonThiDauVaoLayout = () => {
  const { tab } = useParams();
  const navigate = useNavigate();
  const selectedTab = useMemo(() => {
    if (tab === "truong") return "truong";
    return "monthidauvao";
  }, [tab]);
  
  return (
    <Box>
      <UnderlineNav aria-label="tabs">       
        <UnderlineNav.Item
          icon={FileDirectoryOpenFillIcon}
          aria-current={selectedTab === "monthidauvao" ? "page" : undefined}
          onClick={() => {
            navigate(`../../monthidauvao/monthidauvao`);
          }}
        >
          Môn thi đầu vào
        </UnderlineNav.Item>
        <UnderlineNav.Item
          icon={StackIcon}
          aria-current={selectedTab === "truong" ? "page" : undefined}
          onClick={() => {
            navigate(`../../monthidauvao/truong`);
          }}
        >
          Môn thi đầu vào - Trường
        </UnderlineNav.Item>
      </UnderlineNav>
      <Box>{selectedTab === "monthidauvao" && <MonThiDauVaoPage />}</Box>
      <Box>{selectedTab === "truong" && <MonThiDauVaoTruongPage />}</Box>
    </Box>
  );
};

export default MonThiDauVaoLayout;
