import {
  KebabHorizontalIcon,
  PencilIcon,
  PlusIcon,
  SyncIcon,
  TrashIcon,
} from "@primer/octicons-react";
import {
  ActionList,
  ActionMenu,
  Box,
  IconButton,
  useConfirm
} from "@primer/react";
import { useEffect, useState } from "react";
import { monThiDauVaoApi } from "../../api/monThiDauVaoApi";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { MonThiDauVaoItemResponse } from "../../models/response/quan-ly-thi/ex_monthidauvao";
import MonThiDauVaoDetailModal from "./MonThiDauVaoDetailModal";
import MyButton from "../../components/ui/button/Button";

const MonThiDauVaoPage = () => {
  const [isShowModal, setIsShowModal] = useState(false);
  const [selectedId, setSelectedId] = useState(0);
  const [monThiDauVaos, setMonThiDauVaos] = useState<MonThiDauVaoItemResponse[]>([]);
  const { translate } = useCommonContext();
  const confirm = useConfirm();

  useEffect(() => {
    handleReloadAsync();
  }, []);

  const handleReloadAsync = async () => {
    try {
      const res = await monThiDauVaoApi.selectAll();
      if (res.is_success) {
        setMonThiDauVaos(res.data || []);
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    }
  };

  const handeDelete = async (id: number) => {
    if (
      await confirm({
        content: "Bạn có chắc chắn muốn xóa môn thi đầu vào này?",
        title: "Lưu ý",
        cancelButtonContent: "Không xóa",
        confirmButtonContent: "Xóa",
        confirmButtonType: "danger",
      })
    ) {
      try {
        const res = await monThiDauVaoApi.delete(id);
        if (res.is_success) {
          NotifyHelper.Success("Xóa thành công");
          handleReloadAsync();
        } else {
          NotifyHelper.Error(res.message ?? "");
        }
      } catch (error) {
        console.error(error);
        NotifyHelper.Error("Có lỗi xảy ra khi xóa dữ liệu");
      }
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <DataTable
        height={`${window.innerHeight - 250}px`}
        columns={[
          {
            id: "cmd",
            width: "50px",
            caption: "#",
            align: "center",
            cellRender: (data: MonThiDauVaoItemResponse) => {
              return (
                <Box sx={{ m: -1 }}>
                  <ActionMenu>
                    <ActionMenu.Anchor>
                      <IconButton
                        icon={KebabHorizontalIcon}
                        variant="invisible"
                        aria-label="Open column options"
                      />
                    </ActionMenu.Anchor>

                    <ActionMenu.Overlay>
                      <ActionList>
                        <ActionList.Item
                          onSelect={() => {
                            setIsShowModal(true);
                            if (data.id !== undefined) {
                              setSelectedId(data.id);
                            }
                          }}
                        >
                          <ActionList.LeadingVisual>
                            <PencilIcon />
                          </ActionList.LeadingVisual>
                          <TextTranslated value="Base.Label.Edit" />
                        </ActionList.Item>
                        <ActionList.Divider />
                        <ActionList.Item
                          variant="danger"
                          onSelect={() => {
                            if (data.id !== undefined) {
                              handeDelete(data.id);
                            }
                          }}
                        >
                          <ActionList.LeadingVisual>
                            <TrashIcon />
                          </ActionList.LeadingVisual>
                          <TextTranslated value="Base.Label.Delete" />
                        </ActionList.Item>
                      </ActionList>
                    </ActionMenu.Overlay>
                  </ActionMenu>
                </Box>
              );
            },
          },
          {
            dataField: "mon_thi",
            width: "300px",
            isMainColumn: true,
            caption: `Môn thi`,
            cellRender: (data: MonThiDauVaoItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.mon_thi || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "mon_thi_en",
            width: "300px",
            caption: `Môn thi (Tiếng Anh)`,
            cellRender: (data: MonThiDauVaoItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.mon_thi_en || "..."}
                </Box>
              );
            },
          }        
        ]}
        data={monThiDauVaos}
        title="Môn thi đầu vào"
        subTitle={`${translate("Base.Label.TotalCount")}: ${monThiDauVaos.length}`}
        searchEnable
        actionComponent={
          <>
            <Button
              text={translate('Base.Label.Create')}
              leadingVisual={PlusIcon}
              variant="primary"
              size="medium"
              onClick={() => {
                setIsShowModal(true);
                setSelectedId(0);
              }}
            />
              <MyButton
                  text="Base.Label.Refresh"
                  leadingVisual={SyncIcon}
                  size="medium"
                  sx={{ ml: 2 }}
                  onClick={() => handleReloadAsync()}
                />
          </>
        }
      />
      {isShowModal && (
        <MonThiDauVaoDetailModal
          id={selectedId}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReloadAsync();
            setIsShowModal(false);
          }}
        />
      )}
    </Box>
  );
};

export default MonThiDauVaoPage;
