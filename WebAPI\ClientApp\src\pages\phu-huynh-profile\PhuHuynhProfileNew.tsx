import { Box } from "@primer/react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { PlaceHolder } from "../../components/place-holder";
import { phuHuynhApi } from "../../api/phuHuynhApi";
import Modal from "../../components/ui/modal";
import QuanHeGiaDinhPhuHuynh from "./gia-dinh";
import { PhuHuynhProfileListTab } from "./list-tab";
import { PhuHuynhProfileTabs } from "./list-tab/PhuHuynhProfileListTab";
import { PhuHuynhNameAndAvavtar } from "./name-and-avatar";
import SearchBoxPhuHuynhModal from "../../components/searchbox-phuhuynh-modal/SearchBoxPhuHuynhModal";

interface IPhuHuynhItemRespone {
  id: number;
  ho_ten: string;
  dien_thoai: string;
  email: string;
  quan_he: string;
  avatar?: string;
}

interface PhuHuynhProfileNewProps {
  height?: number;
  onClose: () => void;
  onSuccess: () => void;
}

const PhuHuynhProfileNew = (props: PhuHuynhProfileNewProps) => {
  const { id } = useParams<{ id: string }>();
  const [PhuHuynh, setPhuHuynh] = useState<any>({ id: 0 });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState<string>("dashboard");
  const [hasSelectedPhuHuynh, setHasSelectedPhuHuynh] = useState(false);
  const componentDidMount = useRef(false);

  useEffect(() => {
    componentDidMount.current = true;
    return () => {
      componentDidMount.current = false;
    };
  }, []);

  const handleGetPhuHuynhData = async (phuHuynhId: number) => {
    setIsLoading(true);
    const res = await phuHuynhApi.getById(phuHuynhId);
    if (!componentDidMount.current) return;
    setIsLoading(false);
    if (res.is_success) {
      setPhuHuynh(res.data);
      setHasSelectedPhuHuynh(true);
    }
  };

  const handlePhuHuynhSelected = (selectedPhuHuynh: IPhuHuynhItemRespone) => {
    // Set basic info first to show immediately
    setPhuHuynh({
      id: selectedPhuHuynh.id,
      ho_ten: selectedPhuHuynh.ho_ten,
      dien_thoai: selectedPhuHuynh.dien_thoai,
      email: selectedPhuHuynh.email,
      quan_he: selectedPhuHuynh.quan_he,
      avatar: selectedPhuHuynh.avatar,
    });
    setHasSelectedPhuHuynh(true);
    
    // Then load full data
    handleGetPhuHuynhData(selectedPhuHuynh.id);
  };

  const PhuHuynhProfileTab = useMemo(() => {
    return PhuHuynhProfileTabs.find((x) => x.id === selectedTab);
  }, [selectedTab]);

  return (
    <Modal
      isOpen
      onClose={props.onClose}
      sx={{
        width: "80%"
      }}
      title={"Thông tin phụ huynh"}
    >
      <Box className="container" p={0}>
        {!hasSelectedPhuHuynh ? (
          // Search phase - show search box
          <Box p={3}>
            <Box mb={3}>
              <h3>Tìm kiếm phụ huynh</h3>
              <p style={{ color: "#666", marginBottom: "16px" }}>
                Nhập tên hoặc số điện thoại để tìm kiếm phụ huynh
              </p>
            </Box>
            <SearchBoxPhuHuynhModal
              onPhuHuynhSelected={handlePhuHuynhSelected}
              placeholder="Nhập tên hoặc số điện thoại phụ huynh..."
            />
          </Box>
        ) : (
          // Profile phase - show profile like original component
          <>
            <Box
              className="top-side"
              mb={0}
              pb={1}
              display="flex"
              flexDirection="row"
              alignItems="center"
              justifyContent="space-between"
            >
              {isLoading && <PlaceHolder line_number={2} />}
              {!isLoading && (
                <Box
                  display="flex"
                  flexDirection="row"
                  alignItems="center"
                  flexGrow={1}
                >
                  <PhuHuynhNameAndAvavtar key={PhuHuynh.id} phuHuynh={PhuHuynh} />
                  <Box flexGrow={1}>
                    <PhuHuynhProfileListTab
                      selectedTab={selectedTab}
                      PhuHuynhId={PhuHuynh.id}
                      onTabSelectionChanged={setSelectedTab}
                    />
                  </Box>
                </Box>
              )}
            </Box>

            <Box
              className="bottom-side"
              overflow="auto"
              height={props.height ?? window.innerHeight - 300}
            >
              <Box className="content">
                <Box p={0}>
                  {selectedTab === "dashboard" && (
                    <QuanHeGiaDinhPhuHuynh id={PhuHuynh.id} />
                  )}
                </Box>
              </Box>
            </Box>
          </>
        )}
      </Box>
    </Modal>
  );
};

export default PhuHuynhProfileNew;
