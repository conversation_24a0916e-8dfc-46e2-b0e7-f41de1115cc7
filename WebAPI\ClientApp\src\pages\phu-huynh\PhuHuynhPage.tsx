import {
  CalendarIcon,
  DeviceMobileIcon,
  KebabHorizontalIcon,
  LocationIcon,
  MailIcon,
  MoveToTopIcon,
  NoteIcon,
  PencilIcon,
  PersonIcon,
  PlusIcon,
  SyncIcon,
  TrashIcon,
} from "@primer/octicons-react";
import {
  ActionList,
  ActionMenu,
  Box,
  IconButton,
  Label,
  Link,
  useConfirm,
} from "@primer/react";
import moment from "moment";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { phuHuynhApi } from "../../api/phuHuynhApi";
import ComboboxTrangThaiPhuHuynh from "../../components/combobox-trangthai-phuhuynh";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import UserAvatar from "../../components/user-avatar";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { ePageBaseStatus } from "../../models/ePageBaseStatus";
import { IPhuHuynhSelectRequest } from "../../models/request/phu-huynh/IPhuHuynhSelectRequest";
import { IPhuHuynhItemResponse } from "../../models/response/hoc-sinh/IPhuHuynhItemReponse";
import { actions } from "../../state/actions/actionsWrapper";
import { RootState } from "../../state/reducers";
import PhuHuynhInsertModal from "./insert-modal/PhuHuynhInsertModal";
import SelectBoxTrangThaiHocSinhMultiple from "../../components/selectbox-trangthai-hocsinh-multiple";
import TextWithEllipse from "../../components/ui/text-with-ellipse";
import PhuHuynhProfile from "../phu-huynh-profile";
import Text from "../../components/ui/text";
import GanTuVanVienModal from "./GanTuVanVienModal";

const PhuHuynhPage = ({
  onValueChanged,
}: {
  onValueChanged?: (values: { dm_trangthaiphuhuynh_ids: string }) => void;
}) => {
  const { status, PhuHuynhs } = useSelector(
    (state: RootState) => state.crmPhuHuynh
  );
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
  const { translate } = useCommonContext();
  const confirm = useConfirm();
  const dispatch = useDispatch();

  const { dm_truong_selected_id } = useSelector((x: RootState) => x.common);
  const [dm_trangthaiphuhuynh_id, setDmTrangThaiPhuHuynhId] =
    useState<number>(0);
  const [chienDichSelectedId, setChienDichSelectedId] = useState(0);
  const [selectedId, setSelectedId] = useState(0);
  const [phuHuynhEditing, setPhuHuynhEditing] = useState({ id: 0 });
  const [filterStatusId, setFilterStatusId] = useState(0);
  const [ts_phuhuynhs, setTsPhuHuynhs] = useState<IPhuHuynhItemResponse[]>([]);
  const [isShowModal, setIsShowModal] = useState(false);
  const [isShowPHModal, setIsShowPHModal] = useState(false);
  const [isGanTuVanVien, setIsGanTuVanVien] = useState(false);
  const [isShowModalGanTuVanVien, setIsShowModalGanTuVanVien] = useState(false);
    const [danhSachPHSlectedId, setDanhSachPHSlectedId] = useState<
      number[]
    >([]);
    const onSelectionChanged = (selectedRowKeys: number[]) => {
      setDanhSachPHSlectedId(selectedRowKeys);
    };
  // const [dmChiendichs, setDmChiendichs] = useState<IDmChienDichView[]>([]);
  // const [chienDichSelectedId, setChienDichSelectedId] = useState(0);
  // const [filterStatusId, setFilterStatusId] = useState(0);
  // const [userTuVanVienFilter, setUserTuVanVienFilter] = useState("");
  // const [phuHuynhEditing, setPhuHuynhEditing] = useState({ id: 0 });
  useEffect(() => {
    setTsPhuHuynhs(PhuHuynhs);
  }, [PhuHuynhs]);
  // Function to load data
  const handleReload = useCallback(() => {
    const payload: IPhuHuynhSelectRequest = {
      dm_coso_id: dm_coso_id,
      ts_phuhuynh_adm_status_id: dm_trangthaiphuhuynh_id,
      dm_chiendich_id: chienDichSelectedId,
    };
    dispatch(actions.crmPhuHuynh.LOAD_START(payload));
  }, [dm_trangthaiphuhuynh_id, dm_coso_id, chienDichSelectedId, dispatch]);

  useEffect(() => {
    if (
      status === ePageBaseStatus.is_not_initialization ||
      status === ePageBaseStatus.is_need_reload
    ) {
      handleReload();
    }
  }, [status, handleReload]);

  const dataSource = useMemo(() => {
    return ts_phuhuynhs.filter(
      (x) =>
        x.ts_phuhuynh_adm_status_id == filterStatusId || filterStatusId == 0
    );
  }, [filterStatusId, ts_phuhuynhs]);

  // const tuVanViens = useMemo(() => {
  //   const user_tu_van_full_names = phuHuynhs.map((x) => x.user_tu_van_full_name ? x.user_tu_van_full_name : '--Chưa gán--');
  //   const distinctTuVanVienNames = [...new Set(user_tu_van_full_names)];
  //   return distinctTuVanVienNames.map((x) => ({ id: x, name: x }));
  // }, [phuHuynhs]);
  // const handleEditClick = (data: any) => {
  //   setPhuHuynhEditing(data);
  //   setSelectedId(data);
  //   alert(selectedId)
  //   setIsShowModal(true);
  // };

  const handeDelete = async (id: number) => {
    if (
      await confirm({
        content: ``,
        title: `${translate("Base.Label.Warning")}`,
        cancelButtonContent: `Không xóa`,
        confirmButtonContent: `Xóa`,
        confirmButtonType: "danger",
      })
    ) {
      const res = await phuHuynhApi.delete(id);
      if (res.is_success) {
        NotifyHelper.Success("Success");
        handleReload();
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    }
  };

  return (
    <Box
      sx={{
        p: 3,
        overflowX: "auto", // Bật thanh cuộn ngang
      }}
    >
      {/* <div
        className="col-md-12 p-0"
        style={{
          marginTop: 10,
        }}
      >
        <PhuHuynhStatusSummary
          onSelectionChanged={setFilterStatusId}
          ts_phuhuynhs={ts_phuhuynhs}
        />
      </div> */}
      <DataTable
        height={`${window.innerHeight - 280}px`}
        data={dataSource}
        title={translate("Phụ huynh")}
        subTitle={`${translate("Base.Label.TotalCount")}: ${dataSource.length}`}
        searchEnable
        paging={{
          enable: true,
          pageSizeItems: [20, 50, 200, 500, 1000],
        }}
        actionComponent={
          <Box
            sx={{
              display: "flex",
            }}
          >
            {/* <ComboboxTruong
              value={dm_truong_selected_id}
              onValueChanged={(value) => {
                dispatch(
                  actions.common.changedValueHeaderComboboxTruong(value)
                );
              }}
            /> */}
            <ComboboxTrangThaiPhuHuynh
              value={filterStatusId}
              onValueChanged={setFilterStatusId}
              phuHuynhs={PhuHuynhs}
              isCounting={true}
            />
<ActionMenu>
              <ActionMenu.Button variant="default" sx={{ ml: 1 }}>
                <Text text="Thao tác" />
              </ActionMenu.Button>
              <ActionMenu.Overlay width="auto">
                <ActionList>
                  <ActionList.Item
                    onSelect={() => {
                      if (danhSachPHSlectedId.length > 0) {
                        setIsGanTuVanVien(true);
                        setIsShowModalGanTuVanVien(true);
                      } else {
                        NotifyHelper.Warning("Vui lòng chọn học sinh trước");
                      }
                    }}
                  >
                    <ActionList.LeadingVisual>
                      <MoveToTopIcon />
                    </ActionList.LeadingVisual>
                    <Text
                      text={`Gán tư vấn viên (${danhSachPHSlectedId.length})`}
                    />
                  </ActionList.Item>
                </ActionList>
              </ActionMenu.Overlay>
            </ActionMenu>
            <Button
              sx={{ ml: 1 }}
              text="Base.Label.AddNew"
              leadingVisual={PlusIcon}
              size="medium"
              variant="primary"
              onClick={() => {
                setSelectedId(0);
                setIsShowModal(true);
              }}
            />
            <Button
              text="Base.Label.Refresh"
              leadingVisual={SyncIcon}
              size="medium"
              sx={{ ml: 1 }}
              onClick={() => {
                handleReload();
              }}
            />
          </Box>
        }
        selection={{
          mode: "multiple",
          keyExpr: "id",
          selectedRowKeys: danhSachPHSlectedId,
          onSelectionChanged(keys) {
            onSelectionChanged(keys);
          },
        }}
        columns={[
          {
            id: "cmd",
            width: "50px",
            align: "center",
            cellRender: (data: IPhuHuynhItemResponse) => {
              return (
                <ActionMenu>
                  <ActionMenu.Anchor>
                    <IconButton
                      icon={KebabHorizontalIcon}
                      variant="invisible"
                      aria-label="Open column options"
                    />
                  </ActionMenu.Anchor>

                  <ActionMenu.Overlay>
                    <ActionList>
                      <ActionList.Item
                        onClick={() => {
                          setSelectedId(data.id);
                          setIsShowModal(true);
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <PencilIcon />
                        </ActionList.LeadingVisual>
                        <TextTranslated value="Base.Label.Edit" />
                      </ActionList.Item>
                      <ActionList.Divider />
                      <ActionList.Item
                        variant="danger"
                        onSelect={() => {
                          handeDelete(data.id);
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <TrashIcon />
                        </ActionList.LeadingVisual>
                        <TextTranslated value="Base.Label.Delete" />
                      </ActionList.Item>
                    </ActionList>
                  </ActionMenu.Overlay>
                </ActionMenu>
              );
            },
          },
          {
            dataField: "trang_thai",
            width: "100px",
            caption: translate("Trạng thái"),
            fixed: true,
            isMainColumn: true,
            cellRender: (data: IPhuHuynhItemResponse) => {
              let color: any = "fg.default";
              switch (data.ts_phuhuynh_adm_status_name) {
                case "New Enrollment":
                  color = "accent.emphasis";
                  break;
                case "Thôi học":
                  color = "danger.emphasis";
                  break;
                case "Học sinh lên lớp chưa đóng phí":
                  color = "attention.emphasis";
                  break;

                default:
                  break;
              }
              return (
                <Box
                  sx={{
                    color: color,
                    // color: "attention.emphasis"
                  }}
                  // variant={
                  //   data.trang_thai === "New Enrollment"
                  //     ? "accent"
                  //     : data.trang_thai === "Re-Enrollment"
                  //       ? "success"
                  //       : data.trang_thai === "Tiềm năng"
                  //         ? "sponsors"
                  //         : data.trang_thai === "Thôi học"
                  //           ? "danger"
                  //           : "default"
                  // }
                >
                  <TextWithEllipse
                    text={data.ts_phuhuynh_adm_status_name}
                    lineNumber={1}
                  />
                </Box>
              );
            },
          },
          {
            dataField: "ten_chien_dich",
            width: "100px",
            caption: translate("Chiến dịch"),
            cellRender: (data: IPhuHuynhItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                  }}
                >
                  {data.ten_chien_dich || "-"}
                </Box>
              );
            },
          },

          {
            dataField: "avatar",
            width: "100px",
            caption: translate("Avatar"),
            cellRender: (data: IPhuHuynhItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                  }}
                >
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <div style={{ color: "#10375C" }}>
                      <UserAvatar fullName={data.ho_ten} size="large" />
                    </div>
                  </div>
                </Box>
              );
            },
          },
          {
            dataField: "thong_tin_cha",
            caption: translate("Họ tên"),
            width: "300px",
            cellRender: (data: IPhuHuynhItemResponse) => {
              const handleRowClick = () => {
                setSelectedId(data.id);
                setIsShowPHModal(true);
              };
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  <Box sx={{ display: "flex" }} onClick={handleRowClick}>
                    <Link href="#" underline>
                      <span style={{ fontWeight: "600" }}>{data.ho_ten}</span>
                    </Link>
                  </Box>
                  <Box sx={{ color: "fg.muted" }}>
                    <TextWithEllipse
                      text={`${data.dien_thoai ?? ""} - ${data.email ?? ""}`}
                      lineNumber={1}
                    />
                  </Box>
                </Box>
              );
            },
          },

          {
            dataField: "gioi_tinh",
            width: "100px",

            caption: translate("Giới tính"),
            cellRender: (data: IPhuHuynhItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                  }}
                >
                  {data.gioi_tinh || "-"}
                </Box>
              );
            },
          },

          {
            dataField: "thong_tin_cham_soc",
            width: "250px",
            caption: translate("Thông tin chăm sóc gần nhất"),
            cellRender: (data: IPhuHuynhItemResponse) => {
              const hasNoData =
                !data.ngay_cham_soc_gan_nhat &&
                !data.noi_dung_cham_soc_gan_nhat &&
                !data.nguoi_cham_soc_gan_nhat;

              if (hasNoData) {
                return <span>-</span>;
              }

              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    whiteSpace: "nowrap",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                    }}
                  >
                    {data.noi_dung_cham_soc_gan_nhat ? (
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <div style={{ color: "#133E87" }}>
                          <NoteIcon size={14} />
                        </div>
                        <span style={{ marginLeft: "5px" }}>
                          <TextWithEllipse
                            text={data.noi_dung_cham_soc_gan_nhat}
                            lineNumber={1}
                          />
                        </span>
                      </div>
                    ) : null}

                    <div style={{ display: "flex", alignItems: "center" }}>
                      {(data.nguoi_cham_soc_gan_nhat || data.ngay_cham_soc_gan_nhat) && (
                        <>
                          <div style={{ color: "#133E87" }}>
                            <PersonIcon size={14} />
                          </div>
                          <span style={{ marginLeft: "5px" }}>
                            {data.nguoi_cham_soc_gan_nhat || "-"}
                            {data.ngay_cham_soc_gan_nhat && (
                              <span style={{ marginLeft: "5px" }}>
                                ({moment(data.ngay_cham_soc_gan_nhat).format("DD/MM/YYYY")})
                              </span>
                            )}
                          </span>
                        </>
                      )}
                    </div>
                  </Box>
                </Box>
              );
            },
          },
          {
            dataField: "user_tu_van_full_name",
            width: "150px",
            caption: translate("Tư vấn viên"),
            cellRender: (data: IPhuHuynhItemResponse) => {
              if (!data.user_tu_van_full_name) {
                return <span>-</span>;
              }

              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    whiteSpace: "nowrap",
                  }}
                >
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <div style={{ color: "#133E87" }}>
                      <PersonIcon size={14} />
                    </div>
                    <span style={{ marginLeft: "5px" }}>
                      {data.user_tu_van_full_name}
                    </span>
                  </div>
                </Box>
              );
            },
          },
          {
            dataField: "ngay_tao",
            width: "100px",
            caption: translate("Ngày tạo"),
            cellRender: (data: IPhuHuynhItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    whiteSpace: "nowrap",
                  }}
                >
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <div style={{ color: "#133E87" }}>
                      <CalendarIcon size={14} />
                    </div>
                    <span style={{ marginLeft: "5px" }}>
                      {data.ngay_tao
                        ? moment(data.ngay_tao).format("DD/MM/YYYY")
                        : ""}
                    </span>
                  </div>
                </Box>
              );
            },
          },
        ]}
      />
      {isShowModal && (
        <PhuHuynhInsertModal
          id={selectedId}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReload();
            setIsShowModal(false);
          }}
        />
      )}
      {isShowPHModal && (
        <PhuHuynhProfile
          id={selectedId}
          onClose={() => {
            setIsShowPHModal(false);
          }}
          onSuccess={() => {
            handleReload();
            setIsShowPHModal(false);
          }}
        />
      )}
      {isShowModalGanTuVanVien && (
        <GanTuVanVienModal
        selectedIds={danhSachPHSlectedId}
        onClose={() => setIsShowModalGanTuVanVien(false)}
        onSuccess={() => {
          setIsShowModalGanTuVanVien(false);
          handleReload(); // Tải lại dữ liệu
        }}
        />
      )}
    </Box>
  );
};

export default PhuHuynhPage;
