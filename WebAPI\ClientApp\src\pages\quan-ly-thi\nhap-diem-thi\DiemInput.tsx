// DiemInput.tsx
import React, { useState, useEffect } from 'react';
import { TextInput } from '@primer/react';

interface DiemInputProps {
  diem_toi_da: number;
  nhap_diem_chu: boolean;
  value: string | number;
  onValueChanged: (value: string | number) => void;
}

const DiemInput: React.FC<DiemInputProps> = ({
  diem_toi_da,
  nhap_diem_chu,
  value,
  onValueChanged,
}) => {
  const [localValue, setLocalValue] = useState(value);

  // Đồng bộ localValue với prop value khi value thay đổi từ bên ngoài
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
  };

  const handleBlur = () => {
    console.log("DiemInput handleBlur:", { localValue, value, nhap_diem_chu, diem_toi_da });

    // Chỉ lưu khi giá trị thay đổi
    if (localValue === value) {
      console.log("No change, skipping save");
      return;
    }

    if (nhap_diem_chu) {
      console.log("Saving text value:", localValue);
      onValueChanged(localValue);
      return;
    }

    const numValue = parseFloat(String(localValue));
    console.log("Parsed number value:", numValue);

    // Nếu diem_toi_da = 0, cho phép nhập điểm (có thể chưa thiết lập điểm tối đa)
    const isValidNumber = !isNaN(numValue) && numValue >= 0 && (diem_toi_da === 0 || numValue <= diem_toi_da);

    if (isValidNumber) {
      console.log("Valid number, saving:", numValue);
      onValueChanged(numValue);
    } else {
      console.log("Invalid number, resetting to:", value);
      // Reset về giá trị ban đầu nếu giá trị không hợp lệ
      setLocalValue(value);
    }
  };

  return (
    <TextInput
      value={localValue}
      onChange={handleChange}
      onBlur={handleBlur}
      type={nhap_diem_chu ? "text" : "number"}
      min={0}
      max={diem_toi_da}
      sx={{
        textAlign: 'center',
        '& input': {
          textAlign: 'center'
        }
      }}
    />
  );
};

export default DiemInput;