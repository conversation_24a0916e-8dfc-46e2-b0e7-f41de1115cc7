import React, { useEffect, useState, useMemo } from "react";
import {
  Box,
  Text,
  ActionList,
  ActionMenu,
  Button,
  FormControl,
  TextInput,
  Textarea,
} from "@primer/react";
import { SyncIcon, PencilIcon } from "@primer/octicons-react";
import { useCommonContext } from "../../../contexts/common";
import { NotifyHelper } from "../../../helpers/toast";
import { diemThiApi } from "../../../api/diemThiApi";
import { phongThiApi } from "../../../api/phongThiApi";
import DataTable from "../../../components/ui/data-table";
import PhongThiTreeSelection from "../phong-thi/phongthi-tree-selection/PhongThiTreeSelection";
import MyButton from "../../../components/ui/button/Button";
import { ComboboxTruong } from "../../../components/combobox-truong";
import ComboboxKyThi from "../../../components/combobox-kythi";
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";
import DiemInput from "./DiemInput";
import NhanXetInput from "./NhanXetInput";

// Interfaces
interface IThiSinh {
  ex_danhsachthi_id: number;
  ex_kythi_monthi_id: number;
  ex_thisinh_id: number;
  ex_phongthi_id: number;
  ho_ten: string;
  ten_phong: string;
  so_bao_danh: string;
  diem_so: number;
  nhan_xet_chung: string;
  [key: string]: any;
}

interface IDiemTong {
  ex_danhsachthi_id: number;
  ex_kythi_monthi_id: number;
  diem_so: number;
  nhan_xet_chung: string;
}

interface IMonThiThanhPhan {
  id: number;
  mon_thi: string;
  thang_diem: string;
  diem_toi_da: number;
  free_text: boolean;
  diem_thanh_phan?: string;
  nhan_xet_rieng?: boolean;
}

interface IPhongThiData {
  ex_phongthi_id: number;
  ex_kythi_monthi_id: number;
  ky_mon_thi?: {
    id: number;
    ten_mon_thi: string;
    ten_hinh_thuc_thi: string;
    nhan_xet_chung: boolean;
    thang_diem: string;
  };
  monthi_thanhphan?: IMonThiThanhPhan[];
}

interface IDiemThanhPhan {
  ex_danhsachthi_id: number;
  ex_monthi_thanhphan_id: number;
  diem_chu: string;
  diem_so: number;
  nhan_xet_rieng: string;
}

interface IColumn<T = any> {
  caption?: string;
  dataField?: string;
  id?: string;
  cellRender?: (data: T) => JSX.Element;
  width?: string;
  align?: "left" | "center" | "right";
  columns?: IColumn<T>[];
  minWidth?: string;
  maxWidth?: string;
  isVisible?: boolean;
}

interface IUpdateDiemRequest {
  ex_danhsachthi_id: number;
  ex_kythi_monthi_id: number;
  ex_monthi_thanhphan_id: number;
  ex_phongthi_id: number;
  ex_thisinh_id: number;
  diem_chu: string;
  diem_so: number;
  nhan_xet_rieng: string;
}

// Định nghĩa type riêng cho từng loại key
type DiemKey = `D_${number}`;
type NhanXetKey = `NXR_${number}`;

// Định nghĩa type cho các dynamic keys
type DiemThanhPhanKeys = {
  [K in DiemKey | NhanXetKey]: K extends DiemKey ? string | number : string;
};

// Interface cơ bản cho thí sinh
interface IThiSinh {
  ex_danhsachthi_id: number;
  ex_kythi_monthi_id: number;
  ex_thisinh_id: number;
  ex_phongthi_id: number;
  ho_ten: string;
  ten_phong: string;
  so_bao_danh: string;
  diem_so: number;
  nhan_xet_chung: string;
}
interface PhongThiTreeItem {
  key: string;
  text: string;
  parent_key?: string | undefined; // Đổi kiểu phù hợp với component
  ex_kythi_monthi_id: number;
  ex_phongthi_id: number;
  ky_mon_thi?: {
    id: number;
    ten_mon_thi: string;
    ten_hinh_thuc_thi: string;
    nhan_xet_chung: boolean;
    thang_diem: string;
  };
  monthi_thanhphan?: Array<{
    id: number;
    mon_thi: string;
    thang_diem: string;
    diem_toi_da: number;
    free_text: boolean;
    diem_thanh_phan?: string;
    nhan_xet_rieng?: boolean;
  }>;
}

interface PhongThiTreeSelectionProps {
  dataSource: PhongThiTreeItem[];
  onSelectionChanged: (
    selected: {
      ex_phongthi_id: number;
      ex_kythi_monthi_id: number;
    } | null
  ) => void;
}
// Interface cho thí sinh với điểm thành phần
interface IThiSinhDiem extends IThiSinh, DiemThanhPhanKeys {}

const NhapDiemThiPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showNhapDiem, setShowNhapDiem] = useState(false);
  const [dsThiSinhs, setDSThiSinhs] = useState<IThiSinh[]>([]);
  const [dsDiems, setDSDiems] = useState<IDiemThanhPhan[]>([]);
  const [dsDiemTongs, setDSDiemTongs] = useState<IDiemTong[]>([]);
  const [selectedPhongThi, setSelectedPhongThi] =
    useState<IPhongThiData | null>(null);
  const [kyThiKetThuc, setKyThiKetThuc] = useState(false);
  const [isNavCollapsed, setIsNavCollapsed] = useState(false);
  const [dsPhongHocs, setDSPhongHocs] = useState<PhongThiTreeItem[]>([]);

  const { nam_hoc } = useCommonSelectedHook();
  const { translate } = useCommonContext();

  const [filter, setFilter] = useState({
    dm_truong_id: 0,
    nam_hoc: nam_hoc,
    ex_kythi_id: 0,
  });

  const [thiSinhNhapDiem, setThiSinhNhapDiem] = useState<IThiSinh | null>(null);

  const loadPhongThi = async () => {
    try {
      const res = await phongThiApi.selectTreeView(filter.ex_kythi_id || 0);
      if (res.is_success) {
        // API trả về đúng cấu trúc TreeItem
        setDSPhongHocs(res.data);
      }
      setSelectedPhongThi(null);
    } catch (error) {
      NotifyHelper.Error(translate("Error.LoadDataFailed"));
    }
  };

  const loadThiSinhs = async () => {
    if (selectedPhongThi?.ex_kythi_monthi_id) {
      setIsLoading(true);
      try {
        const res = await diemThiApi.selectDiemThiByPhongThi(
          selectedPhongThi.ex_phongthi_id,
          selectedPhongThi.ex_kythi_monthi_id
        );
        if (res.is_success) {
          setDSThiSinhs(res.data);
        }
      } catch (error) {
        NotifyHelper.Error(translate("Error.LoadDataFailed"));
      } finally {
        setIsLoading(false);
      }
    }
  };

  const loadDiemThi = async () => {
    if (selectedPhongThi?.ex_kythi_monthi_id) {
      try {
        const res = await diemThiApi.selectDiemThiThanhPhanByPhongThi(
          selectedPhongThi.ex_phongthi_id,
          selectedPhongThi.ex_kythi_monthi_id
        );
        if (res.is_success) {
          setDSDiems(res.data);
        }
      } catch (error) {
        NotifyHelper.Error(translate("Error.LoadDataFailed"));
      }
    }
  };

  const handleUpdateDiem = async (data: IUpdateDiemRequest) => {
    try {
      console.log("Updating diem with data:", data);
      const res = await diemThiApi.updateDiemThiThanhPhanThiSinh(data);
      console.log("API response:", res);
      if (res.is_success) {
        NotifyHelper.Success(translate("Success.Update"));
        loadDiemThi();
        loadThiSinhs();
      } else {
        NotifyHelper.Error(translate("Error.UpdateFailed"));
      }
    } catch (error) {
      console.error("Error updating diem:", error);
      NotifyHelper.Error(translate("Error.Generic"));
    }
  };

  useEffect(() => {
    loadPhongThi();
  }, [filter.ex_kythi_id]);

  useEffect(() => {
    if (selectedPhongThi) {
      loadThiSinhs();
      loadDiemThi();
    }
  }, [selectedPhongThi]);

  const danhSachNhapDiem = useMemo<IThiSinhDiem[]>(() => {
    return dsThiSinhs.map((thiSinh): IThiSinhDiem => {
      const tongDiem = dsDiemTongs.find(
        (diem) =>
          diem.ex_danhsachthi_id === thiSinh.ex_danhsachthi_id &&
          diem.ex_kythi_monthi_id === thiSinh.ex_kythi_monthi_id
      );

      const result = {
        ...thiSinh,
        diem_so: tongDiem?.diem_so ?? thiSinh.diem_so,
        nhan_xet_chung: tongDiem?.nhan_xet_chung ?? thiSinh.nhan_xet_chung,
      } as IThiSinhDiem;

      if (selectedPhongThi?.monthi_thanhphan) {
        selectedPhongThi.monthi_thanhphan.forEach((thanhPhan) => {
          const diemThanhPhan = dsDiems.find(
            (diem) =>
              diem.ex_danhsachthi_id === thiSinh.ex_danhsachthi_id &&
              diem.ex_monthi_thanhphan_id === thanhPhan.id
          );

          const diemKey = `D_${thanhPhan.id}` as DiemKey;
          result[diemKey] = thanhPhan.free_text
            ? diemThanhPhan?.diem_chu || ""
            : diemThanhPhan?.diem_so || 0;

          if (thanhPhan.nhan_xet_rieng) {
            const nhanXetKey = `NXR_${thanhPhan.id}` as NhanXetKey;
            result[nhanXetKey] = diemThanhPhan?.nhan_xet_rieng || "";
          }
        });
      }

      return result;
    });
  }, [dsThiSinhs, dsDiems, dsDiemTongs, selectedPhongThi?.monthi_thanhphan]);

  const columns: IColumn<IThiSinhDiem>[] = [
    // {
    //   id: "actions",
    //   caption: "",
    //   width: "50px",
    //   cellRender: (data) => {
    //     if (kyThiKetThuc) return <Box />;
    //     return (
    //       <ActionMenu>
    //         <ActionMenu.Button>
    //           <PencilIcon />
    //         </ActionMenu.Button>
    //         <ActionMenu.Overlay>
    //           <ActionList>
    //             <ActionList.Item
    //               onSelect={() => {
    //                 setThiSinhNhapDiem(data);
    //                 setShowNhapDiem(true);
    //               }}
    //             >
    //               {translate("Nhập điểm bài thi")}
    //             </ActionList.Item>
    //           </ActionList>
    //         </ActionMenu.Overlay>
    //       </ActionMenu>
    //     );
    //   },
    // },
    {
      caption: translate("TT"),
      width: "50px",
      align: "center",
      cellRender: (rowData) => (
        <Text>{danhSachNhapDiem.indexOf(rowData) + 1}</Text>
      ),
    },
    {
      dataField: "ten_phong",
      caption: translate("Phòng thi"),
      width: "200px",
    },
    {
      dataField: "so_bao_danh",
      caption: translate("Số báo danh"),
      width: "200px",
    },
    {
      dataField: "ho_ten",
      caption: translate("Họ tên"),
      width: "200px",
    },
    {
      dataField: "diem_so",
      caption: translate("Tổng điểm"),
      width: "200px",
      align: "center"
    },
    ...(selectedPhongThi?.monthi_thanhphan?.map((thanhPhan): IColumn<IThiSinhDiem>[] => {
      console.log("Creating column for thanhPhan:", thanhPhan);
      const columns: IColumn<IThiSinhDiem>[] = [
        {
          caption: `${translate(thanhPhan.diem_thanh_phan || '')} (${thanhPhan.diem_toi_da})`,
          dataField: `D_${thanhPhan.id}`,
          width: thanhPhan.free_text ? "200px" : "100px",
          align: "center",
          cellRender: (data) => !kyThiKetThuc ? (
            <DiemInput
              key={`D_${thanhPhan.id}`}
              diem_toi_da={thanhPhan.diem_toi_da}
              nhap_diem_chu={thanhPhan.free_text}
              value={data[`D_${thanhPhan.id}` as DiemKey]}
              onValueChanged={(value) => {
                handleUpdateDiem({
                  ex_danhsachthi_id: data.ex_danhsachthi_id,
                  ex_kythi_monthi_id: data.ex_kythi_monthi_id,
                  ex_monthi_thanhphan_id: thanhPhan.id,
                  ex_phongthi_id: data.ex_phongthi_id,
                  ex_thisinh_id: data.ex_thisinh_id,
                  diem_chu: thanhPhan.free_text ? String(value) : '',
                  diem_so: thanhPhan.free_text ? 0 : Number(value),
                  nhan_xet_rieng: String(data[`NXR_${thanhPhan.id}` as NhanXetKey] || '')
                });
              }}
            />
          ) : (
            <Text>{data[`D_${thanhPhan.id}` as DiemKey]}</Text>
          )
        }
      ];

      if (thanhPhan.nhan_xet_rieng) {
        columns.push({
          dataField: `NXR_${thanhPhan.id}`,
          caption: translate("Nhận xét"),
          width: "200px",
          cellRender: (data) => {
            if (kyThiKetThuc)
              return <Text>{data[`NXR_${thanhPhan.id}` as NhanXetKey]}</Text>;

            return (
              <NhanXetInput
                value={data[`NXR_${thanhPhan.id}` as NhanXetKey] || ''}
                onSave={(newValue) => {
                  handleUpdateDiem({
                    ex_danhsachthi_id: data.ex_danhsachthi_id,
                    ex_kythi_monthi_id: data.ex_kythi_monthi_id,
                    ex_monthi_thanhphan_id: thanhPhan.id,
                    ex_phongthi_id: data.ex_phongthi_id,
                    ex_thisinh_id: data.ex_thisinh_id,
                    diem_chu: thanhPhan.free_text ? data[`D_${thanhPhan.id}` as DiemKey].toString() : '',
                    diem_so: thanhPhan.free_text ? 0 : Number(data[`D_${thanhPhan.id}` as DiemKey]),
                    nhan_xet_rieng: newValue
                  });
                }}
                disabled={kyThiKetThuc}
              />
            );
          }
        });
      }

      return columns;
    }).flat() || [])

  ];

  return (
    <Box sx={{ bg: "canvas.subtle", p: 3, height: "calc(100vh - 100px)" }}>
      <Box
        sx={{
          bg: "canvas.default",
          borderRadius: 2,
          borderColor: "border.default",
          borderStyle: "solid",
          borderWidth: 1,
          display: "flex",
          height: "100%",
        }}
      >
        {/* Sidebar */}
        <Box
          sx={{
            borderRightWidth: 1,
            borderRightStyle: "solid",
            borderRightColor: "border.default",
            width: isNavCollapsed ? 48 : 256,
            transition: "width 0.2s",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            sx={{
              p: 2,
              borderBottomWidth: 1,
              borderBottomStyle: "solid",
              borderBottomColor: "border.default",
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            {!isNavCollapsed && (
              <Text sx={{ fontWeight: "bold", fontSize: "22px" }}>
                {translate("Nhập điểm thi")}
              </Text>
            )}
            <Button
              variant="invisible"
              onClick={() => setIsNavCollapsed(!isNavCollapsed)}
            />
          </Box>
          <Box sx={{ p: 2, overflowY: "auto", flex: 1 }}>
            <PhongThiTreeSelection
              dataSource={dsPhongHocs}
              onSelectionChanged={(selected) => {
                if (!selected) return;

                const selectedNode =
                  dsPhongHocs.find(
                    (node) =>
                      node.ex_phongthi_id === selected.ex_phongthi_id &&
                      node.ex_kythi_monthi_id === selected.ex_kythi_monthi_id
                  ) ||
                  dsPhongHocs.find(
                    (node) =>
                      node.ex_kythi_monthi_id === selected.ex_kythi_monthi_id
                  );

                if (selectedNode) {
                  console.log("Selected node:", selectedNode);
                  console.log("monthi_thanhphan:", selectedNode.monthi_thanhphan);
                  setSelectedPhongThi({
                    ex_phongthi_id: selected.ex_phongthi_id,
                    ex_kythi_monthi_id: selected.ex_kythi_monthi_id,
                    ky_mon_thi: selectedNode.ky_mon_thi,
                    monthi_thanhphan: selectedNode.monthi_thanhphan,
                  });
                }
              }}
            />
          </Box>
        </Box>

        {/* Main Content */}
        <Box
          sx={{
            flex: 1,
            display: "flex",
            flexDirection: "column",
            overflow: "hidden",
          }}
        >
          {/* Filters */}
          <Box
            sx={{
              p: 2,
              borderBottomWidth: 1,
              borderBottomStyle: "solid",
              borderBottomColor: "border.default",
              bg: "canvas.subtle",
            }}
          >
            <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
              <ComboboxTruong
                value={filter.dm_truong_id}
                onValueChanged={(value) => {
                  setFilter((prev) => ({ ...prev, dm_truong_id: value || 0 }));
                }}
              />
              <ComboboxKyThi
                value={filter.ex_kythi_id}
                dm_truong_id={filter.dm_truong_id}
                onValueChanged={(value, data) => {
                  setFilter((prev) => ({ ...prev, ex_kythi_id: value || 0 }));
                  setKyThiKetThuc(data.da_ket_thuc);
                }}
              />
            </Box>
          </Box>

          {/* Content Area */}
          <Box sx={{ p: 2, flex: 1, overflow: "hidden" }}>
            <Box sx={{
              height: "calc(100vh - 260px)",
              overflow: "auto",
              "& .container": {
                overflowX: "auto",
                overflowY: "auto"
              },
              "& table": {
                tableLayout: "auto",
                minWidth: "max-content"
              }
            }}>
              <DataTable
                height="100%"
                data={danhSachNhapDiem}
                isLoading={isLoading}
                searchEnable
                paging={{
              enable: true,
              pageSizeItems: [20, 50, 200, 500, 1000],
          }}
                actionComponent={
                  <Box sx={{ display: "flex", gap: 2 }}>
                    {kyThiKetThuc && (
                      <Box
                        sx={{
                          bg: "danger.emphasis",
                          color: "white",
                          px: 2,
                          borderRadius: 2,
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <Text sx={{ fontWeight: "bold" }}>
                          {translate("Kỳ thi đã kết thúc")}
                        </Text>
                      </Box>
                    )}
                    <MyButton
                      text={translate("Base.Label.Refresh")}
                      leadingVisual={SyncIcon}
                      onClick={loadThiSinhs}
                    />
                  </Box>
                }
                columns={columns}
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default NhapDiemThiPage;
