import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Box, FormControl, TextInput } from "@primer/react";
import Modal from "../../../components/ui/modal";
import { useCommonContext } from "../../../contexts/common";
import { NotifyHelper } from "../../../helpers/toast";
import { trangThaiThiSinhApi } from "../../../api/trangThaiThiSinhApi";
import { ex_trangthai_thisinh } from "../../../models/response/trang-thai-thi-sinh/ex_trangthai_thisinh";
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";
import Button from "../../../components/ui/button";

interface ITrangThaiThiSinhDetailModalProps {
  id: number;
  onClose: () => void;
  onSuccess: () => void;
}

const TrangThaiThiSinhDetailModal = (props: ITrangThaiThiSinhDetailModalProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { translate } = useCommonContext();
  const { dm_coso_id } = useCommonSelectedHook();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ex_trangthai_thisinh>({
    defaultValues: {},
  });

  useEffect(() => {
    if (props.id > 0) {
      handleGetVm();
    }
  }, [props.id]);

  const handleGetVm = async () => {
    const res = await trangThaiThiSinhApi.detail(props.id);
    if (res.is_success) {
      const data: ex_trangthai_thisinh = {
        ...res.data,
      };
      reset(data);
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
  };

  const onSubmit = async (data: any) => {
    setIsSaving(true);
    let res: any;

    const submitData = {
      ...data,
      id: props.id,
      dm_coso_id: dm_coso_id,
    };

    if (props.id > 0) {
      res = await trangThaiThiSinhApi.update(submitData);
    } else {
      res = await trangThaiThiSinhApi.insert(submitData);
    }

    if (res.is_success) {
      NotifyHelper.Success("Success");
      props.onSuccess();
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
    setIsSaving(false);
  };

  return (
    <Modal
      isOpen={true}
      onClose={props.onClose}
      title={`${props.id > 0 ? translate("Base.Label.Edit") : translate("Base.Label.Create")} ${translate("Trạng thái thí sinh")}`}
      width="medium"
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
          <FormControl>
            <FormControl.Label>
              {translate("Tên trạng thái")} <span style={{ color: "red" }}>*</span>
            </FormControl.Label>
            <TextInput
              {...register("ten_trangthai", {
                required: translate("Base.Label.Required"),
              })}
              placeholder={translate("Nhập tên trạng thái")}
            />
            {errors.ten_trangthai && (
              <FormControl.Validation variant="error">
                {errors.ten_trangthai.message}
              </FormControl.Validation>
            )}
          </FormControl>

          <FormControl>
            <FormControl.Label>
              {translate("Tên trạng thái (English)")}
            </FormControl.Label>
            <TextInput
              {...register("ten_trangthai_en")}
              placeholder={translate("Nhập tên trạng thái (English)")}
            />
          </FormControl>
        </Box>
      </form>

      <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end", mt: 3 }}>
        <Button
          text="Base.Label.Close"
          onClick={props.onClose}
        />
        <Button
          text={isSaving ? "Base.Label.Saving" : "Base.Label.Save"}
          variant="primary"
          onClick={handleSubmit(onSubmit)}
          isLoading={isSaving}
        />
      </Box>
    </Modal>
  );
};

export default TrangThaiThiSinhDetailModal;
