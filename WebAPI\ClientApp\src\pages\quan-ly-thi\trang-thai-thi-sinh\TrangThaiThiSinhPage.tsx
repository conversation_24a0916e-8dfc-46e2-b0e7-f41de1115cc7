import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  ActionMenu,
  ActionList,
  IconButton,
  useConfirm,
} from "@primer/react";
import {
  PlusIcon,
  KebabHorizontalIcon,
  PencilIcon,
  TrashIcon,
} from "@primer/octicons-react";
import DataTable from "../../../components/ui/data-table";
import { useCommonContext } from "../../../contexts/common";
import { NotifyHelper } from "../../../helpers/toast";
import { trangThaiThiSinhApi } from "../../../api/trangThaiThiSinhApi";
import { ex_trangthai_thisinh } from "../../../models/response/trang-thai-thi-sinh/ex_trangthai_thisinh";
import TrangThaiThiSinhDetailModal from "./TrangThaiThiSinhDetailModal";
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";
import Text from "../../../components/ui/text";
import Button from "../../../components/ui/button";

const TrangThaiThiSinhPage = () => {
  const [trangThaiThiSinhs, setTrangThaiThiSinhs] = useState<ex_trangthai_thisinh[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isShowModal, setIsShowModal] = useState(false);
  const [selectedId, setSelectedId] = useState(0);
  const { translate } = useCommonContext();
  const { dm_coso_id } = useCommonSelectedHook();
  const confirm = useConfirm();

  const handleReloadAsync = useCallback(async () => {
    setIsLoading(true);
    try {
      const res = await trangThaiThiSinhApi.selectAll();
      if (res.is_success) {
        setTrangThaiThiSinhs(res.data);
      } else {
        setTrangThaiThiSinhs([]);
        NotifyHelper.Error(res.message ?? "Error");
      }
    } catch (error) {
      setTrangThaiThiSinhs([]);
      NotifyHelper.Error("Có lỗi khi tải dữ liệu");
    }
    setIsLoading(false);
  }, [dm_coso_id]);

  useEffect(() => {
    handleReloadAsync();
  }, [handleReloadAsync]);

  const handleDelete = async (id: number) => {
    if (await confirm({
      content: translate("Base.Label.DeleteConfirm.Content"),
      title: translate("Base.Label.Confirm"),
      cancelButtonContent: translate("Base.Label.Close"),
      confirmButtonContent: translate("Base.Label.Delete"),
      confirmButtonType: "danger"
    })) {
      try {
        const res = await trangThaiThiSinhApi.delete(id);
        if (res.is_success) {
          NotifyHelper.Success("Xóa thành công");
          handleReloadAsync();
        } else {
          NotifyHelper.Error(res.message ?? "Error");
        }
      } catch (error) {
        NotifyHelper.Error("Có lỗi khi xóa");
      }
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <DataTable
        isLoading={isLoading}
        columns={[
          {
            id: "cmd",
            caption: "#",
            width: "50px",
            align: "center",
            cellRender: (data: ex_trangthai_thisinh) => {
              return (
                <ActionMenu>
                  <ActionMenu.Anchor>
                    <IconButton
                      icon={KebabHorizontalIcon}
                      variant="invisible"
                      aria-label="Open column options"
                    />
                  </ActionMenu.Anchor>
                  <ActionMenu.Overlay width="auto">
                    <ActionList>
                      <ActionList.Item onSelect={() => {
                        setSelectedId(data.id);
                        setIsShowModal(true);
                      }}>
                        <ActionList.LeadingVisual><PencilIcon /></ActionList.LeadingVisual>
                        <Text text='Base.Label.Edit' />
                      </ActionList.Item>
                      <ActionList.Item variant="danger" onSelect={() => {
                        handleDelete(data.id);
                      }}>
                        <ActionList.LeadingVisual><TrashIcon /></ActionList.LeadingVisual>
                        <Text text='Base.Label.Delete' />
                      </ActionList.Item>
                    </ActionList>
                  </ActionMenu.Overlay>
                </ActionMenu>
              );
            },
          },
          {
            dataField: "ten_trangthai",
            caption: translate("Tên trạng thái"),
          },
          {
            dataField: "ten_trangthai_en",
            caption: translate("Tên trạng thái (English)"),
          },
        ]}
        data={trangThaiThiSinhs}
        title="Trạng thái thí sinh"
        subTitle={`${translate("Base.Label.TotalCount")}: ${trangThaiThiSinhs.length}`}
        searchEnable
        actionComponent={
          <>
            <Button
              text='Base.Label.Create'
              leadingVisual={PlusIcon}
              variant="primary"
              size="medium"
              onClick={() => {
                setIsShowModal(true);
                setSelectedId(0);
              }}
            />
          </>
        }
      />
      {isShowModal && (
        <TrangThaiThiSinhDetailModal
          id={selectedId}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReloadAsync();
            setIsShowModal(false);
          }}
        />
      )}
    </Box>
  );
};

export default TrangThaiThiSinhPage;
