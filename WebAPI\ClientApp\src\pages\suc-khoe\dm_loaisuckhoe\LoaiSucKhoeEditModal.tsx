import { Box, Checkbox, FormControl } from '@primer/react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import Button from '../../../components/ui/button';
import Modal from '../../../components/ui/modal';
import ModalActions from '../../../components/ui/modal/ModalActions';
import Text from '../../../components/ui/text';
import TextInput from '../../../components/ui/text-input';
import { useCommonContext } from '../../../contexts/common';
import { useCommonSelectedHook } from '../../../hooks/useCommonSelectedHook';
import { ePageBaseStatus } from '../../../models/ePageBaseStatus';
import { actions } from '../../../state/actions/actionsWrapper';
import { RootState } from '../../../state/reducers';
import { dm_loaisuckhoe } from '../../../models/response/category/dm_loaisuckhoe';
import { showNotify } from '../../../helpers/toast';

const LoaiSucKhoeEditModal = () => {
    const { status, editingData } = useSelector((x: RootState) => x.sucKhoe.loaiSucKhoe)
    const dispatch = useDispatch();
    const { dm_coso_id, nam_hoc } = useCommonSelectedHook();
    const { translate } = useCommonContext();
    const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm<dm_loaisuckhoe>({
        defaultValues: editingData ? {
            ...editingData,
        } : undefined
    })
    const onSubmit = async (data: any) => {
        if (!data.is_checkbox && !data.is_yesno_question) {
            showNotify({ type: "error", message: "Vui lòng chọn ít nhất một trong hai: 'Câu hỏi dạng CheckBox' hoặc 'Câu hỏi dạng Yes/No'." });

            return;
        }
        dispatch(actions.sucKhoe.loaiSucKhoe.SAVE_START({
            ...data,
            id: data?.id ?? 0,
            dm_coso_id: dm_coso_id,
            nam_hoc: nam_hoc
        }))
    }

    return (
        <Modal isOpen
            title={editingData ? "Base.Label.Edit" : "Base.Label.AddNew"}
            onClose={() => {
                dispatch(actions.sucKhoe.loaiSucKhoe.CLOSE_EDIT_MODAL(undefined))
            }}
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box sx={{ display: "grid", gap: 2 }}>
                    <FormControl>
                        <FormControl.Label><Text text='Loại sức khỏe' /></FormControl.Label>
                        <TextInput block
                            name="name"
                            register={register}
                            errors={errors}
                            required
                            validateMessage={`${translate("Base.Form.RequiredMessage")} ${translate("Loại sức khỏe")}`}
                        />
                    </FormControl>
                    <FormControl>
                        <FormControl.Label><Text text='Loại sức khỏe (en)' /></FormControl.Label>
                        <TextInput block
                            name="name_en"
                            register={register}
                            errors={errors}
                            required
                            validateMessage={`${translate("Base.Form.RequiredMessage")} ${translate("Loại sức khỏe (en)")}`}
                        />
                    </FormControl>
                    {/* Vùng 1: Loại câu hỏi */}
                    <Box sx={{
                        border: "1px solid #e1e4e8",
                        borderRadius: "6px",
                        padding: "16px",
                        marginBottom: "16px"
                    }}>
                        <Box sx={{ fontWeight: "bold", marginBottom: "8px" }}>Loại câu hỏi</Box>
                        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                                <input
                                    type="radio"
                                    id="checkbox_type"
                                    name="question_type"
                                    checked={watch("is_checkbox", editingData?.is_checkbox)}
                                    onChange={() => {
                                        setValue("is_checkbox", true);
                                        setValue("is_yesno_question", false);
                                    }}
                                    style={{ marginRight: "8px" }}
                                />
                                <label htmlFor="checkbox_type">Câu hỏi dạng CheckBox</label>
                            </Box>
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                                <input
                                    type="radio"
                                    id="yesno_type"
                                    name="question_type"
                                    checked={watch("is_yesno_question", editingData?.is_yesno_question)}
                                    onChange={() => {
                                        setValue("is_yesno_question", true);
                                        setValue("is_checkbox", false);
                                    }}
                                    style={{ marginRight: "8px" }}
                                />
                                <label htmlFor="yesno_type">Câu hỏi dạng Yes/No</label>
                            </Box>
                        </Box>
                    </Box>

                    {/* Vùng 2: Các tùy chọn khác */}
                    <Box sx={{
                        border: "1px solid #e1e4e8",
                        borderRadius: "6px",
                        padding: "16px"
                    }}>
                        <Box sx={{ fontWeight: "bold", marginBottom: "8px" }}>Tùy chọn khác</Box>
                        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                                <Checkbox
                                    id="multiple_choose"
                                    checked={watch("is_multiple_choose", editingData?.is_multiple_choose)}
                                    onChange={(e) => {
                                        setValue("is_multiple_choose", e.target.checked)
                                    }}
                                    sx={{ marginRight: "8px" }}
                                />
                                <label htmlFor="multiple_choose">Cho phép chọn nhiều lựa chọn</label>
                            </Box>
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                                <Checkbox
                                    id="active"
                                    checked={watch("is_active", editingData?.is_active)}
                                    onChange={(e) => {
                                        setValue("is_active", e.target.checked)
                                    }}
                                    sx={{ marginRight: "8px" }}
                                />
                                <label htmlFor="active">Active</label>
                            </Box>
                        </Box>
                    </Box>
                </Box>

                <ModalActions>
                    <Button text='Base.Label.Close' onClick={() => {
                        dispatch(actions.sucKhoe.loaiSucKhoe.CLOSE_EDIT_MODAL(undefined))
                    }} />
                    <Button text='Base.Label.Update' variant='primary' type='submit'
                        isLoading={status === ePageBaseStatus.is_saving}
                    />
                </ModalActions>
            </form>

        </Modal>
    );
};

export default LoaiSucKhoeEditModal;