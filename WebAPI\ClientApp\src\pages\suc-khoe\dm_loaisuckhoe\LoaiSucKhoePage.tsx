import { KebabHorizontalIcon, PencilIcon, PlusIcon, SyncIcon, TrashIcon } from "@primer/octicons-react";
import { ActionList, ActionMenu, Box, Checkbox, useConfirm } from "@primer/react";
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Button from "../../../components/ui/button";
import DataTable from "../../../components/ui/data-table";
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";
import { ePageBaseStatus } from "../../../models/ePageBaseStatus";
import { actions } from "../../../state/actions/actionsWrapper";

import Text from "../../../components/ui/text";
import { useCommonContext } from "../../../contexts/common";
import { RootState } from "../../../state/reducers";
import LoaiSucKhoeEditModal from "./LoaiSucKhoeEditModal";
import { dm_loaisuckhoe } from "../../../models/response/category/dm_loaisuckhoe";

const LoaiSucKhoePage = () => {
    const { status, loaiSucKhoes, isShowEditModal, editingData } = useSelector((x: RootState) => x.sucKhoe.loaiSucKhoe)
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
    const { translate } = useCommonContext();
    const confirm = useConfirm();
    const dispatch = useDispatch();
    useEffect(() => {
        if (status === ePageBaseStatus.is_not_initialization || status === ePageBaseStatus.is_need_reload) {
            handleReload();
        }
    }, [status])

    const filteredData = useMemo(() => {
        if (loaiSucKhoes.length > 0 && dm_coso_id) {
            return loaiSucKhoes.filter(x => x.dm_coso_id === dm_coso_id);
        }
        return loaiSucKhoes;
    }, [loaiSucKhoes, dm_coso_id]);
    const handleReload = () => {
        dispatch(actions.sucKhoe.loaiSucKhoe.LOAD_START(''))
    }
    const handleDelete = async (id: number) => {
        if (await confirm({
            content: translate("Base.Label.DeleteConfirm.Content"),
            title: translate("Base.Label.Confirm"),
            cancelButtonContent: translate("Base.Label.Close"),
            confirmButtonContent: translate("Base.Label.Delete"),
            confirmButtonType: "danger"
        })) {
            dispatch(actions.sucKhoe.loaiSucKhoe.DELETE_START(id))
        }
    }

    return (
        <Box sx={{
            p: 3
        }}>
            <DataTable
                data={filteredData}
                isLoading={status === ePageBaseStatus.is_loading}
                title={translate("Danh sách loại sức khỏe")}
                searchEnable
                paging={{
            enable: true,
            pageSizeItems: [20, 50, 200, 500, 1000],
        }}
                actionComponent={
                    <Box sx={{
                        display: "flex"
                    }}>
                        <Button text="Base.Label.AddNew" leadingVisual={PlusIcon}
                            size="medium"
                            variant="primary"
                            onClick={() => {
                                dispatch(actions.sucKhoe.loaiSucKhoe.SHOW_EDIT_MODAL(undefined))
                            }}
                        />
                        <Button text="Base.Label.Refresh" leadingVisual={SyncIcon}
                            size="medium"
                            sx={{ ml: 1 }}
                            onClick={() => {
                                handleReload();
                            }}
                        />
                    </Box>
                }
                columns={[
                    {
                        id: "cmd",
                        caption: "#",
                        width: "50px",
                        align: "center",
                        cellRender: (data: dm_loaisuckhoe) => {
                            return (
                                <ActionMenu>
                                    <ActionMenu.Button icon={KebabHorizontalIcon} variant="invisible" sx={{ m: -1 }}>&nbsp;</ActionMenu.Button>
                                    <ActionMenu.Overlay width="auto">
                                        <ActionList>
                                            <ActionList.Item onSelect={() => {
                                                dispatch(actions.sucKhoe.loaiSucKhoe.SHOW_EDIT_MODAL(data))
                                            }}>
                                                <ActionList.LeadingVisual><PencilIcon /></ActionList.LeadingVisual>
                                                <Text text='Base.Label.Edit' />
                                            </ActionList.Item>
                                            <ActionList.Item variant="danger" onSelect={() => {
                                                handleDelete(data.id)
                                            }}>
                                                <ActionList.LeadingVisual><TrashIcon /></ActionList.LeadingVisual>
                                                <Text text='Base.Label.Delete' />
                                            </ActionList.Item>
                                        </ActionList>
                                    </ActionMenu.Overlay>
                                </ActionMenu>
                            );
                        }
                    },
                    {
                        dataField: "name",
                        caption: translate("Loại sức khỏe"),

                    },
                    {
                        dataField: "name_en",
                        caption: translate("Loại sức khỏe (en)"),

                    },
                    {
                        dataField: "is_checkbox",
                        caption: translate("Câu hỏi dạng Checkbox"),
                        cellRender: (data: dm_loaisuckhoe) => {
                            return (
                                <Box sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                }}>
                                    <Checkbox checked={data.is_checkbox} />
                                </Box>
                            )
                        }
                    },
                    {
                        dataField: "is_yesno_question",
                        caption: translate("Câu hỏi dạng Yes/No"),
                        cellRender: (data: dm_loaisuckhoe) => {
                            return (
                                <Box sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                }}>
                                    <Checkbox checked={data.is_yesno_question} />
                                </Box>
                            )
                        }
                    },
                    {
                        dataField: "is_multiple_choose",
                        caption: translate("Cho phép lựa chọn nhiều"),
                        cellRender: (data: dm_loaisuckhoe) => {
                            return (
                                <Box sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                }}>
                                    <Checkbox checked={data.is_multiple_choose} />
                                </Box>
                            )
                        }
                    },
                    {
                        dataField: "Active",
                        caption: translate("Active"),
                        cellRender: (data: dm_loaisuckhoe) => {
                            return (
                                <Box sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                }}>
                                    <Checkbox checked={data.is_active} />
                                </Box>
                            )
                        }
                    },
                ]}
            />
            {isShowEditModal &&
                <LoaiSucKhoeEditModal />
            }
        </Box>
    );
};

export default LoaiSucKhoePage;