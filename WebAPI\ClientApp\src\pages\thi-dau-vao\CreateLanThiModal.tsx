import React, { useState } from "react";
import {
  Box,
  FormControl,
  TextInput,
  Text,
  Textarea,
} from "@primer/react";

import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import Modal from "../../components/ui/modal";
import ModalActions from "../../components/ui/modal/ModalActions";
import Button from "../../components/ui/button";
import { CreateWithSubjectsRequest } from "../../api/ketQuaThiDauVaoApi";
import ComboboxTrangThaiThiSinh from "../../components/combobox-trangthai-thisinh/ComboboxTrangThaiThiSinh";
import moment from "moment";

interface CreateLanThiModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateWithSubjectsRequest) => void;
  hocSinhId: number;
  dmTruongId: number;
  nextLanThi: number;
  isLoading?: boolean;
}

const CreateLanThiModal: React.FC<CreateLanThiModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  hocSinhId,
  dmTruongId,
  nextLanThi,
  isLoading = false,
}) => {
  const { nam_hoc } = useCommonSelectedHook();

  const [formData, setFormData] = useState({
    ngay_thi: moment().format("YYYY-MM-DD"),
    ghi_chu: "",
    ex_trangthai_thisinh_id: 1, // Mặc định chọn ID = 1
  });

  const handleSubmit = () => {
    const submitData: CreateWithSubjectsRequest = {
      nam_hoc: nam_hoc,
      ts_hocsinh_id: hocSinhId,
      dm_truong_id: dmTruongId,
      lan_thi: nextLanThi,
      ngay_thi: new Date(formData.ngay_thi),
      ghi_chu: formData.ghi_chu,
      ex_trangthai_thisinh_id: formData.ex_trangthai_thisinh_id > 0 ? formData.ex_trangthai_thisinh_id : undefined,
    };

    console.log("CreateLanThiModal submitData:", submitData); // Debug log
    onSubmit(submitData);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTrangThaiThiSinhChange = (id: number) => {
    setFormData(prev => ({
      ...prev,
      ex_trangthai_thisinh_id: id
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Thêm lần thi mới"
      width="large"
    >
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
          {/* Thông tin cơ bản compact */}
          <Box sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 3 }}>
            <FormControl>
              <FormControl.Label>Năm học</FormControl.Label>
              <TextInput
                value={nam_hoc}
                disabled
              />
            </FormControl>

            <FormControl>
              <FormControl.Label>Lần thi</FormControl.Label>
              <TextInput
                value={nextLanThi.toString()}
                disabled
              />
            </FormControl>
          </Box>

             <Box sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 3 }}>
            <FormControl>
              <FormControl.Label>
              Ngày thi <Text sx={{ color: "danger.fg" }}>*</Text>
            </FormControl.Label>
            <TextInput
              type="date"
              value={formData.ngay_thi}
              onChange={(e) => handleInputChange("ngay_thi", e.target.value)}
              required
            />
            </FormControl>

            <FormControl>
               <FormControl.Label>Trạng thái thí sinh</FormControl.Label>
            <ComboboxTrangThaiThiSinh
              value={formData.ex_trangthai_thisinh_id}
              onValueChanged={handleTrangThaiThiSinhChange}
              preText="Chọn trạng thái thí sinh"
              isShowClearButton={true}
            />
            </FormControl>
          </Box>

          {/* Ghi chú */}
          <FormControl sx={{ gridColumn: "1 / -1" }}>
            <FormControl.Label>Ghi chú</FormControl.Label>
            <Textarea
              value={formData.ghi_chu}
              onChange={(e) => handleInputChange("ghi_chu", e.target.value)}
              placeholder="Nhập ghi chú..."
              rows={4}
              resize="vertical"
              sx={{ width: "100%" }}
            />
          </FormControl>
        </Box>
      </Box>

      <ModalActions>
        <Button
          text="Hủy"
          variant="default"
          onClick={onClose}
          disabled={isLoading}
        />
        <Button
          text="Tạo lần thi"
          variant="primary"
          onClick={handleSubmit}
          isLoading={isLoading}
        />
      </ModalActions>
    </Modal>
  );
};

export default CreateLanThiModal;
