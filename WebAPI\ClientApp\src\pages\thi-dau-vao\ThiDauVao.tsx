import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  Box,
  useConfirm,
  ActionList,
  ActionMenu,
  IconButton,
  Text,
  TextInput as PrimerTextInput,
} from "@primer/react";
import {
  PlusIcon,
  SyncIcon,
  TrashIcon,
  KebabHorizontalIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  PencilIcon,
  CheckIcon,
  XIcon,
  CalendarIcon,
  CommentIcon,
  NumberIcon,
} from "@primer/octicons-react";
import { useCommonContext } from "../../contexts/common";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { NotifyHelper } from "../../helpers/toast";
import Button from "../../components/ui/button";
import MyButton from "../../components/ui/button/Button";
import { ketQuaThiDauVaoApi, CreateWithSubjectsRequest } from "../../api/ketQuaThiDauVao<PERSON><PERSON>";
import { KetQuaThiDauVaoItemResponse } from "../../models/response/quan-ly-thi/ex_ketquathidauvao";
import moment from "moment";
import { IHocSinh } from "../../models/response/crm-hocsinh/IHocsinh";
import ThiDauVaoMonThiTable, { ThiDauVaoMonThiTableRef } from "./ThiDauVaoMonThiTable";
import CreateLanThiModal from "./CreateLanThiModal";
import ComboboxTrangThaiThiSinh from "../../components/combobox-trangthai-thisinh/ComboboxTrangThaiThiSinh";

interface ThiDauVaoProps {
  hocSinh: IHocSinh;
}

interface EditableLanThi {
  id: number;
  isEditing: boolean;
  ngay_thi: string;
  ghi_chu: string;
  ex_trangthai_thisinh_id: number;
}

const ThiDauVao: React.FC<ThiDauVaoProps> = ({ hocSinh }) => {
  const [ketQuaThiDauVaos, setKetQuaThiDauVaos] = useState<
    KetQuaThiDauVaoItemResponse[]
  >([]);
  const [expandedRows, setExpandedRows] = useState<{ [key: number]: boolean }>({});
  const [editableLanThi, setEditableLanThi] = useState<{ [key: number]: EditableLanThi }>({});
  const [editingLanThiId, setEditingLanThiId] = useState<number | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isShowCreateModal, setIsShowCreateModal] = useState(false);
  const tableRefs = useRef<{ [key: number]: ThiDauVaoMonThiTableRef | null }>({});
  const { translate } = useCommonContext();
  const { nam_hoc } = useCommonSelectedHook();
  const confirm = useConfirm();

  const handleReloadAsync = useCallback(async () => {
    try {
      const res = await ketQuaThiDauVaoApi.selectByHocSinhNamHoc(
        hocSinh.id,
        nam_hoc
      );
      if (res.is_success) {
        console.log("ThiDauVao API Response:", res.data); // Debug log
        setKetQuaThiDauVaos(res.data || []);
        // Initialize editable state for each lần thi
        const editableState: { [key: number]: EditableLanThi } = {};
        const expandedState: { [key: number]: boolean } = {};
        (res.data || []).forEach((item: KetQuaThiDauVaoItemResponse) => {
          editableState[item.id] = {
            id: item.id,
            isEditing: false,
            ngay_thi: moment(item.ngay_thi).format("YYYY-MM-DD"),
            ghi_chu: item.ghi_chu || "",
            ex_trangthai_thisinh_id: item.ex_trangthai_thisinh_id || 0,
          };
          // Mặc định tất cả các lần thi đều mở
          expandedState[item.id] = true;
        });
        setEditableLanThi(editableState);
        setExpandedRows(expandedState);
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    }
  }, [hocSinh, nam_hoc]);

  useEffect(() => {
    if (hocSinh && hocSinh.id) {
      handleReloadAsync();
    }
  }, [hocSinh, handleReloadAsync]);

  const handleDelete = async (id: number) => {
    if (
      await confirm({
        title: translate("Base.Label.Confirm"),
        content: translate("Base.Message.DeleteConfirm"),
        confirmButtonContent: "Xóa",
        cancelButtonContent: "Hủy",
        confirmButtonType: "danger",
      })
    ) {
      try {
        const res = await ketQuaThiDauVaoApi.delete(id);
        if (res.is_success) {
          NotifyHelper.Success(translate("Base.Message.DeleteSuccess"));
          handleReloadAsync();
        } else {
          NotifyHelper.Error(res.message ?? "");
        }
      } catch (error) {
        console.error(error);
        NotifyHelper.Error("Có lỗi xảy ra khi xóa dữ liệu");
      }
    }
  };

  const handleCreateNew = () => {
    setIsShowCreateModal(true);
  };

  const handleSubmitCreateLanThi = async (data: CreateWithSubjectsRequest) => {
    setIsCreating(true);
    try {
      const res = await ketQuaThiDauVaoApi.createWithSubjects(data);
      if (res.is_success) {
        NotifyHelper.Success(translate("Base.Message.CreateSuccess"));
        setIsShowCreateModal(false);
        handleReloadAsync();
        // Tự động mở rộng lần thi mới tạo
        setExpandedRows(prev => ({ ...prev, [res.data]: true }));
        // Khởi tạo editable state cho lần thi mới
        setEditableLanThi(prev => ({
          ...prev,
          [res.data]: {
            id: res.data,
            isEditing: false,
            ngay_thi: moment(data.ngay_thi).format("YYYY-MM-DD"),
            ghi_chu: data.ghi_chu || "",
            ex_trangthai_thisinh_id: data.ex_trangthai_thisinh_id || 0,
          }
        }));
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi tạo lần thi mới");
    } finally {
      setIsCreating(false);
    }
  };

  const getNextLanThi = () => {
    return ketQuaThiDauVaos.length > 0
      ? Math.max(...ketQuaThiDauVaos.map(k => k.lan_thi)) + 1
      : 1;
  };

  const toggleExpanded = (id: number) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const handleEditLanThi = (id: number) => {
    setEditingLanThiId(id);
    setEditableLanThi(prev => ({
      ...prev,
      [id]: { ...prev[id], isEditing: true }
    }));
    // Tự động expand nếu chưa expand
    setExpandedRows(prev => ({ ...prev, [id]: true }));
  };

  const handleCancelEditLanThi = (id: number) => {
    const originalData = ketQuaThiDauVaos.find(item => item.id === id);
    if (originalData) {
      setEditableLanThi(prev => ({
        ...prev,
        [id]: {
          ...prev[id],
          isEditing: false,
          ngay_thi: moment(originalData.ngay_thi).format("YYYY-MM-DD"),
          ghi_chu: originalData.ghi_chu || "",
          ex_trangthai_thisinh_id: originalData.ex_trangthai_thisinh_id || 0,
        }
      }));
    }
    setEditingLanThiId(null);
    // Cancel all changes in table
    tableRefs.current[id]?.cancelAll();
  };

  const handleSaveLanThi = async (id: number) => {
    const editableData = editableLanThi[id];
    if (!editableData) return;

    try {
      // Save lần thi info first
      const originalData = ketQuaThiDauVaos.find(item => item.id === id);
      if (!originalData) return;

      const updateData = {
        ...originalData,
        ngay_thi: new Date(editableData.ngay_thi),
        ghi_chu: editableData.ghi_chu,
        ex_trangthai_thisinh_id: editableData.ex_trangthai_thisinh_id,
      };

      const res = await ketQuaThiDauVaoApi.update(updateData);
      if (res.is_success) {
        // Save all table data
        await tableRefs.current[id]?.saveAll();
        setEditableLanThi(prev => ({
          ...prev,
          [id]: { ...prev[id], isEditing: false }
        }));
        setEditingLanThiId(null);
        handleReloadAsync();
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi lưu dữ liệu");
    }
  };

  const handleLanThiFieldChange = (id: number, field: keyof EditableLanThi, value: string | number) => {
    setEditableLanThi(prev => ({
      ...prev,
      [id]: { ...prev[id], [field]: field === "ex_trangthai_thisinh_id" ? Number(value) : value }
    }));
  };

  return (
    <Box>
      {/* Header compact */}
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
        <Text sx={{ fontSize: 1, fontWeight: "semibold" }}>
          Kết quả thi đầu vào ({ketQuaThiDauVaos.length})
        </Text>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            text="Thêm mới"
            leadingVisual={PlusIcon}
            variant="primary"
            size="small"
            onClick={handleCreateNew}
          />
          <MyButton
            text="Base.Label.Refresh"
            leadingVisual={SyncIcon}
            size="small"
            onClick={() => handleReloadAsync()}
          />
        </Box>
      </Box>

      {ketQuaThiDauVaos.map((ketQua) => (
        <Box key={ketQua.id} sx={{ mb: 2, border: "1px solid", borderColor: "border.default", borderRadius: 1 }}>
          {/* Header compact của mỗi lần thi */}
          <Box
            sx={{
              px: 2,
              py: 1.5,
              borderBottom: expandedRows[ketQua.id] ? "1px solid" : "none",
              borderColor: "border.default",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              cursor: "pointer",
              "&:hover": { backgroundColor: "canvas.subtle" }
            }}
            onClick={() => toggleExpanded(ketQua.id)}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2, flex: 1 }}>
              {/* Expand/Collapse Icon */}
              <IconButton
                icon={expandedRows[ketQua.id] ? ChevronDownIcon : ChevronRightIcon}
                variant="invisible"
                size="small"
                aria-label={expandedRows[ketQua.id] ? "Thu gọn" : "Mở rộng"}
              />

              {/* Lần thi */}
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Text sx={{
                  fontWeight: "semibold",
                  fontSize: 1,
                  minWidth: "fit-content"
                }}>
                  Lần {ketQua.lan_thi}
                </Text>
              </Box>

              {/* Ngày thi section */}
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <CalendarIcon size={12} />
                {editableLanThi[ketQua.id]?.isEditing ? (
                  <PrimerTextInput
                    type="date"
                    value={editableLanThi[ketQua.id]?.ngay_thi || ""}
                    onChange={(e) => handleLanThiFieldChange(ketQua.id, "ngay_thi", e.target.value)}
                    size="small"
                    sx={{ width: "130px", fontSize: 0 }}
                  />
                ) : (
                  <Text sx={{
                    color: "fg.muted",
                    fontSize: 0
                  }}>
                    {moment(ketQua.ngay_thi).format("DD/MM/YYYY")}
                  </Text>
                )}
              </Box>

              {/* Ghi chú section */}
              {(editableLanThi[ketQua.id]?.isEditing || ketQua.ghi_chu) && (
                <Box sx={{ display: "flex", alignItems: "center", gap: 1, flex: 1 }}>
                  <CommentIcon size={12} />
                  {editableLanThi[ketQua.id]?.isEditing ? (
                    <PrimerTextInput
                      value={editableLanThi[ketQua.id]?.ghi_chu || ""}
                      onChange={(e) => handleLanThiFieldChange(ketQua.id, "ghi_chu", e.target.value)}
                      placeholder="Ghi chú..."
                      size="small"
                      sx={{
                        flex: 1,
                        maxWidth: "200px",
                        fontSize: 0
                      }}
                    />
                  ) : (
                    <Text sx={{
                      color: "fg.muted",
                      fontSize: 0,
                      fontStyle: "italic",
                      maxWidth: "200px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap"
                    }}>
                      {ketQua.ghi_chu}
                    </Text>
                  )}
                </Box>
              )}

              {/* Trạng thái thí sinh section */}
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
               {editableLanThi[ketQua.id]?.isEditing ? (
                  <Box sx={{ minWidth: "150px" }}>
                    <ComboboxTrangThaiThiSinh
                      value={editableLanThi[ketQua.id]?.ex_trangthai_thisinh_id || 0}
                      onValueChanged={(id) => handleLanThiFieldChange(ketQua.id, "ex_trangthai_thisinh_id", id)}
                      preText="Chọn trạng thái"
                      isShowClearButton={true}
                    />
                  </Box>
                ) : (
                  <Text sx={{
                    color: ketQua.ten_trangthai_thisinh ? "accent.fg" : "fg.muted",
                    fontSize: 0,
                    fontWeight: ketQua.ten_trangthai_thisinh ? "semibold" : "normal",
                    px: 1,
                    py: 0.5,
                    borderRadius: 1,
                    backgroundColor: ketQua.ten_trangthai_thisinh ? "accent.subtle" : "canvas.subtle",
                    fontStyle: ketQua.ten_trangthai_thisinh ? "normal" : "italic"
                  }}>
                    {ketQua.ten_trangthai_thisinh || "Chưa có trạng thái"}
                  </Text>
                )}
              </Box>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {/* Edit/Save/Cancel buttons */}
              {editableLanThi[ketQua.id]?.isEditing ? (
                <>
                  <IconButton
                    icon={CheckIcon}
                    variant="invisible"
                    size="small"
                    aria-label="Lưu"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSaveLanThi(ketQua.id);
                    }}
                    sx={{ color: "success.fg" }}
                  />
                  <IconButton
                    icon={XIcon}
                    variant="invisible"
                    size="small"
                    aria-label="Hủy"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCancelEditLanThi(ketQua.id);
                    }}
                    sx={{ color: "danger.fg" }}
                  />
                </>
              ) : (
                <IconButton
                  icon={PencilIcon}
                  variant="invisible"
                  size="small"
                  aria-label="Chỉnh sửa"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditLanThi(ketQua.id);
                  }}
                />
              )}

              <ActionMenu>
                <ActionMenu.Anchor>
                  <IconButton
                    icon={KebabHorizontalIcon}
                    variant="invisible"
                    size="small"
                    aria-label="Open menu"
                    onClick={(e) => e.stopPropagation()}
                  />
                </ActionMenu.Anchor>
                <ActionMenu.Overlay>
                  <ActionList>
                    <ActionList.Item
                      onSelect={() => handleDelete(ketQua.id)}
                      variant="danger"
                    >
                      <ActionList.LeadingVisual>
                        <TrashIcon />
                      </ActionList.LeadingVisual>
                      {translate("Base.Label.Delete")}
                    </ActionList.Item>
                  </ActionList>
                </ActionMenu.Overlay>
              </ActionMenu>
            </Box>
          </Box>

          {/* Bảng môn thi chi tiết compact */}
          {expandedRows[ketQua.id] && (
            <Box sx={{ p: 2 }}>
              <ThiDauVaoMonThiTable
                ref={(ref) => { tableRefs.current[ketQua.id] = ref; }}
                ketQuaThiDauVaoId={ketQua.id}
                onDataChange={handleReloadAsync}
                isEditMode={editingLanThiId === ketQua.id}
              />
            </Box>
          )}
        </Box>
      ))}

      {ketQuaThiDauVaos.length === 0 && (
        <Box sx={{ textAlign: "center", py: 4 }}>
          <Text sx={{ color: "fg.muted", fontSize: 1 }}>
            Chưa có kết quả thi đầu vào. Nhấn "Thêm mới" để bắt đầu.
          </Text>
        </Box>
      )}

      {/* Modal tạo lần thi mới */}
      <CreateLanThiModal
        isOpen={isShowCreateModal}
        onClose={() => setIsShowCreateModal(false)}
        onSubmit={handleSubmitCreateLanThi}
        hocSinhId={hocSinh.id}
        dmTruongId={hocSinh.dm_truong_id || 0}
        nextLanThi={getNextLanThi()}
        isLoading={isCreating}
      />
    </Box>
  );
};

export default ThiDauVao;
