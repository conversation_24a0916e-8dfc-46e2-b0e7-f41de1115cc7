import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  ActionList,
  ActionMenu,
  IconButton,
  useConfirm,
} from "@primer/react";
import {
  PlusIcon,
  SyncIcon,
  PencilIcon,
  TrashIcon,
  KebabHorizontalIcon,
} from "@primer/octicons-react";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import DataTable from "../../components/ui/data-table";
import Button from "../../components/ui/button";
import MyButton from "../../components/ui/button/Button";
import { ketQuaThiDauVaoChiTietApi } from "../../api/ketQuaThiDauVaoChiTietApi";
import { KetQuaThiDauVaoChiTietItemResponse } from "../../models/response/quan-ly-thi/ex_ketquathidauvao_chitiet";
import ThiDauVaoChiTietModal from "./ThiDauVaoChiTietModal";

interface ThiDauVaoChiTietListProps {
  ketQuaThiDauVaoId: number;
}

const ThiDauVaoChiTietList: React.FC<ThiDauVaoChiTietListProps> = ({
  ketQuaThiDauVaoId,
}) => {
  const [isShowModal, setIsShowModal] = useState(false);
  const [selectedId, setSelectedId] = useState(0);
  const [chiTietList, setChiTietList] = useState<KetQuaThiDauVaoChiTietItemResponse[]>([]);
  const { translate } = useCommonContext();
  const confirm = useConfirm();

  // Declare handleReloadAsync first
  const handleReloadAsync = useCallback(async () => {
    try {
      const res = await ketQuaThiDauVaoChiTietApi.selectByKetQuaThiDauVao(ketQuaThiDauVaoId);
      if (res.is_success) {
        setChiTietList(res.data || []);
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    }
  }, [ketQuaThiDauVaoId]);

  // Then use it in useEffect
  useEffect(() => {
    if (ketQuaThiDauVaoId) {
      handleReloadAsync();
    }
  }, [ketQuaThiDauVaoId, handleReloadAsync]);

  const handleDelete = async (id: number) => {
    if (
      await confirm({
        title: translate("Base.Label.Confirm"),
        content: translate("Base.Message.DeleteConfirm"),
        confirmButtonContent: "Xóa",
        cancelButtonContent: "Hủy",
        confirmButtonType: "danger",
      })
    ) {
      try {
        const res = await ketQuaThiDauVaoChiTietApi.delete(id);
        if (res.is_success) {
          NotifyHelper.Success(translate("Base.Message.DeleteSuccess"));
          handleReloadAsync();
        } else {
          NotifyHelper.Error(res.message ?? "");
        }
      } catch (error) {
        console.error(error);
        NotifyHelper.Error("Có lỗi xảy ra khi xóa dữ liệu");
      }
    }
  };

  return (
    <Box  sx={{
        p: 0,
        overflowX: "auto",
      }}>
      <DataTable
        height="170px"
        columns={[
          {
            dataField: "actions",
            caption: "",
            width: 50,
            align: "center",
            cellRender: (data) => (
              <Box>
                <ActionMenu>
                  <ActionMenu.Anchor>
                    <IconButton
                      icon={KebabHorizontalIcon}
                      variant="invisible"
                      aria-label="Open menu"
                    />
                  </ActionMenu.Anchor>
                  <ActionMenu.Overlay>
                    <ActionList>
                      <ActionList.Item
                        onSelect={() => {
                          setIsShowModal(true);
                          setSelectedId(data.id);
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <PencilIcon />
                        </ActionList.LeadingVisual>
                        {translate("Base.Label.Edit")}
                      </ActionList.Item>
                      <ActionList.Item
                        onSelect={() => handleDelete(data.id)}
                        variant="danger"
                      >
                        <ActionList.LeadingVisual>
                          <TrashIcon />
                        </ActionList.LeadingVisual>
                        {translate("Base.Label.Delete")}
                      </ActionList.Item>
                    </ActionList>
                  </ActionMenu.Overlay>
                </ActionMenu>
              </Box>
            ),
          },
          {
            dataField: "mon_thi",
            caption: "Môn thi",
            width: 150,
          },
          {
            dataField: "diem_thi",
            caption: "Điểm thi",
            width: 100,
          },
          {
            dataField: "diem_thi_cuoi",
            caption: "Điểm thi cuối",
            width: 100,
          },
          {
            dataField: "nhan_xet",
            caption: "Nhận xét",
            width: 200,
          },
          {
            dataField: "ghi_chu",
            caption: "Ghi chú",
            isMainColumn: true,
            width: 200,
          },
        ]}
        data={chiTietList}
        title="Điểm thi"
        subTitle={`${translate("Base.Label.TotalCount")}: ${chiTietList.length}`}
        searchEnable
        actionComponent={
          <>
            <Button
              text={translate("Base.Label.Create")}
              leadingVisual={PlusIcon}
              variant="primary"
              size="medium"
              onClick={() => {
                setIsShowModal(true);
                setSelectedId(0);
              }}
            />
            <MyButton
              text="Base.Label.Refresh"
              leadingVisual={SyncIcon}
              size="medium"
              sx={{ ml: 2 }}
              onClick={() => handleReloadAsync()}
            />
          </>
        }
      />
      {isShowModal && (
        <ThiDauVaoChiTietModal
          id={selectedId}
          ketQuaThiDauVaoId={ketQuaThiDauVaoId}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReloadAsync();
            setIsShowModal(false);
          }}
        />
      )}
    </Box>
  );
};

export default ThiDauVaoChiTietList;
