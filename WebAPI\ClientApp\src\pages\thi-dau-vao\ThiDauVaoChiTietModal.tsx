import React, { useEffect, useState } from "react";
import { Box, FormControl, TextInput as PrimerTextInput } from "@primer/react";
import { Controller, useForm } from "react-hook-form";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import Modal from "../../components/ui/modal";
import ModalActions from "../../components/ui/modal/ModalActions";
import Button from "../../components/ui/button";
// import TextInput from "../../components/ui/text-input";
import { ketQuaThiDauVaoChiTietApi } from "../../api/ketQuaThiDauVaoChiTietApi";
import { ex_ketquathidauvao_chitiet, KetQuaThiDauVaoChiTietItemResponse } from "../../models/response/quan-ly-thi/ex_ketquathidauvao_chitiet";
import { ComboboxMonThiDauVao } from "../../components/combobox";

interface ThiDauVaoChiTietModalProps {
  id: number;
  ketQuaThiDauVaoId: number;
  onClose: () => void;
  onSuccess: () => void;
}

const ThiDauVaoChiTietModal: React.FC<ThiDauVaoChiTietModalProps> = ({
  id,
  ketQuaThiDauVaoId,
  onClose,
  onSuccess,
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const { translate } = useCommonContext();

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<ex_ketquathidauvao_chitiet>({
    defaultValues: {
      id: 0,
      ex_ketquathidauvao_id: ketQuaThiDauVaoId,
      ex_monthidauvao_id: 0,
      diem_thi: "",
      diem_thi_cuoi: "",
      nhan_xet: "",
      ghi_chu: "",
    },
  });

  useEffect(() => {
    if (id > 0) {
      loadData();
    }
  }, [id]);

  const loadData = async () => {
    try {
      const res = await ketQuaThiDauVaoChiTietApi.selectById(id);
      if (res.is_success && res.data) {
        reset(res.data);
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    }
  };

  const onSubmit = async (data: ex_ketquathidauvao_chitiet) => {
    setIsSaving(true);
    try {
      // Check for duplicate subjects when adding new record
      if (id === 0) {
        // Get existing subjects for this entrance exam
        const res = await ketQuaThiDauVaoChiTietApi.selectByKetQuaThiDauVao(ketQuaThiDauVaoId);
        if (res.is_success && res.data) {
          // Check if the selected subject already exists
          const existingSubject = res.data.find(
            (item: KetQuaThiDauVaoChiTietItemResponse) => item.ex_monthidauvao_id === data.ex_monthidauvao_id
          );

          if (existingSubject) {
            NotifyHelper.Error("Môn thi này đã tồn tại trong kết quả thi đầu vào. Vui lòng chọn môn thi khác.");
            setIsSaving(false);
            return;
          }
        }
      }

      // Proceed with saving if no duplicate found
      let res;
      if (id > 0) {
        res = await ketQuaThiDauVaoChiTietApi.update(data);
      } else {
        res = await ketQuaThiDauVaoChiTietApi.insert(data);
      }

      if (res.is_success) {
        NotifyHelper.Success(
          translate(
            id > 0
              ? "Base.Message.UpdateSuccess"
              : "Base.Message.CreateSuccess"
          )
        );
        onSuccess();
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi lưu dữ liệu");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Modal
      isOpen={true}
      title={id > 0 ? "Cập nhật chi tiết điểm thi" : "Thêm mới chi tiết điểm thi"}
      onClose={onClose}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box sx={{ display: "grid", gap: 3, mb: 3 }}>
          <FormControl>
            <FormControl.Label>Môn thi</FormControl.Label>
            <Controller
              name="ex_monthidauvao_id"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <ComboboxMonThiDauVao
                  value={field.value}
                  onValueChanged={(value) => field.onChange(value)}
                  isShowClearButton={true}
                />
              )}
            />
            {errors.ex_monthidauvao_id && (
              <FormControl.Validation variant="error">
                Vui lòng chọn môn thi
              </FormControl.Validation>
            )}
          </FormControl>
          <Box sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 3 }}>
            <FormControl>
              <FormControl.Label>Điểm thi</FormControl.Label>
              <Controller
                name="diem_thi"
                control={control}
                render={({ field }) => (
                  <PrimerTextInput
                    value={field.value || ""}
                    onChange={(e) => {
                      console.log("Điểm thi changed:", e.target.value);
                      field.onChange(e.target.value);
                    }}
                    width="100%"
                  />
                )}
              />
            </FormControl>
            <FormControl>
              <FormControl.Label>Điểm thi cuối</FormControl.Label>
              <Controller
                name="diem_thi_cuoi"
                control={control}
                render={({ field }) => (
                  <PrimerTextInput
                    value={field.value || ""}
                    onChange={(e) => {
                      console.log("Điểm thi cuối changed:", e.target.value);
                      field.onChange(e.target.value);
                    }}
                    width="100%"
                  />
                )}
              />
            </FormControl>
          </Box>
          <FormControl>
            <FormControl.Label>Nhận xét</FormControl.Label>
            <Controller
              name="nhan_xet"
              control={control}
              render={({ field }) => (
                <PrimerTextInput
                  type="text"
                  value={field.value || ""}
                  onChange={(e) => {
                    console.log("Nhận xét changed:", e.target.value);
                    field.onChange(e.target.value);
                  }}
                  width="100%"
                />
              )}
            />
          </FormControl>
          <FormControl>
            <FormControl.Label>Ghi chú</FormControl.Label>
            <Controller
              name="ghi_chu"
              control={control}
              render={({ field }) => (
                <PrimerTextInput
                  type="text"
                  value={field.value || ""}
                  onChange={(e) => {
                    console.log("Ghi chú changed:", e.target.value);
                    field.onChange(e.target.value);
                  }}
                  width="100%"
                />
              )}
            />
          </FormControl>
        </Box>

        <ModalActions>
          <Button text="Base.Label.Close" onClick={onClose} />
          <Button
            text="Base.Label.Save"
            variant="primary"
            type="submit"
            isLoading={isSaving}
          />
        </ModalActions>
      </form>
    </Modal>
  );
};

export default ThiDauVaoChiTietModal;
