import React, { useEffect, useState } from "react";
import { Box, FormControl, Text, TextInput as PrimerTextInput } from "@primer/react";
import { Controller, useForm } from "react-hook-form";
import { useCommonContext } from "../../contexts/common";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { NotifyHelper } from "../../helpers/toast";
import Modal from "../../components/ui/modal";
import ModalActions from "../../components/ui/modal/ModalActions";
import Button from "../../components/ui/button";
// import TextInput from "../../components/ui/text-input";
import { ketQuaThiDauVaoApi } from "../../api/ketQuaThiDauVaoApi";
import { ex_ketquathidauvao } from "../../models/response/quan-ly-thi/ex_ketquathidauvao";
import { IHocSinh } from "../../models/response/crm-hocsinh/IHocsinh";
import ThiDauVaoChiTietList from "./ThiDauVaoChiTietList";
import moment from "moment";

interface ThiDauVaoDetailModalProps {
  id: number;
  hocSinh: IHocSinh;
  onClose: () => void;
  onSuccess: () => void;
}

const ThiDauVaoDetailModal: React.FC<ThiDauVaoDetailModalProps> = ({
  id,
  hocSinh,
  onClose,
  onSuccess,
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [showChiTietList, setShowChiTietList] = useState(false);
  const [savedId, setSavedId] = useState<number>(id);
  const { translate } = useCommonContext();
  const { nam_hoc } = useCommonSelectedHook();

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<ex_ketquathidauvao>({
    defaultValues: {
      id: 0,
      nam_hoc: nam_hoc,
      ts_hocsinh_id: hocSinh.id,
      lan_thi: 1,
      ngay_thi: new Date(),
      ghi_chu: "",
    },
  });

  useEffect(() => {
    if (id > 0) {
      loadData();
    }
  }, [id]);

  const loadData = async () => {
    try {
      const res = await ketQuaThiDauVaoApi.selectById(id);
      if (res.is_success && res.data) {
        reset({
          ...res.data,
          ngay_thi: new Date(res.data.ngay_thi),
        });
        setShowChiTietList(true);
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    }
  };

  const onSubmit = async (data: ex_ketquathidauvao) => {
    setIsSaving(true);
    try {
      let res;
      if (id > 0) {
        res = await ketQuaThiDauVaoApi.update(data);
      } else {
        res = await ketQuaThiDauVaoApi.insert(data);
      }

      if (res.is_success) {
        NotifyHelper.Success(
          translate(
            id > 0
              ? "Base.Message.UpdateSuccess"
              : "Base.Message.CreateSuccess"
          )
        );
        if (id === 0 && res.data) {
          // If this is a new record, show the chi tiết list after saving
          reset({
            ...data,
            id: res.data,
          });
          setSavedId(res.data);
          setShowChiTietList(true);
        } else {
          onSuccess();
        }
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi lưu dữ liệu");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Modal
      isOpen={true}
      title={id > 0 ? "Cập nhật kết quả thi đầu vào" : "Thêm mới kết quả thi đầu vào"}
      width="xlarge"
      onClose={onClose}
      sx={id > 0 ? {
        width: "90%", // Make the modal wider (90% of screen width)
        maxWidth: "1200px", // Set a maximum width
        height: "60vh", // Make the modal taller (90% of viewport height)
        maxHeight: "900px" // Set a maximum height
      } : undefined}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box sx={{ mb: 3 }}>
          <Text>Học sinh: {hocSinh.ho_ten}</Text>
        </Box>
        <Box sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 3, mb: 3 }}>
          <FormControl>
            <FormControl.Label>Lần thi</FormControl.Label>
            <Controller
              name="lan_thi"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <PrimerTextInput
                  type="number"
                  value={field.value}
                  onChange={(e) => {
                    console.log("Lần thi changed:", e.target.value);
                    field.onChange(parseInt(e.target.value) || 0);
                  }}
                  width="100%"
                />
              )}
            />
            {errors.lan_thi && (
              <FormControl.Validation variant="error">
                Vui lòng nhập lần thi
              </FormControl.Validation>
            )}
          </FormControl>
          <FormControl>
            <FormControl.Label>Ngày thi</FormControl.Label>
            <Controller
              name="ngay_thi"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <PrimerTextInput
                  type="date"
                  value={moment(field.value).format("YYYY-MM-DD")}
                  onChange={(e) => {
                    console.log("Ngày thi changed:", e.target.value);
                    field.onChange(new Date(e.target.value));
                  }}
                  width="100%"
                />
              )}
            />
            {errors.ngay_thi && (
              <FormControl.Validation variant="error">
                Vui lòng chọn ngày thi
              </FormControl.Validation>
            )}
          </FormControl>
        </Box>
        <FormControl sx={{ mb: 3 }}>
          <FormControl.Label>Ghi chú</FormControl.Label>
          <Controller
            name="ghi_chu"
            control={control}
            render={({ field }) => (
              <PrimerTextInput
                type="text"
                value={field.value || ""}
                onChange={(e) => {
                  console.log("Ghi chú changed:", e.target.value);
                  field.onChange(e.target.value);
                }}
                width="100%"
              />
            )}
          />
        </FormControl>

        {!showChiTietList && (
          <ModalActions>
            <Button text="Base.Label.Close" onClick={onClose} />
            <Button
              text="Base.Label.Save"
              variant="primary"
              type="submit"
              isLoading={isSaving}
            />
          </ModalActions>
        )}
      </form>

      {showChiTietList && (
        <>
          <Box sx={{ mt: 4 }}>
            <ThiDauVaoChiTietList ketQuaThiDauVaoId={id > 0 ? id : savedId} />
          </Box>
          <ModalActions>
            <Button text="Base.Label.Close" onClick={onClose} />
            <Button
              text="Base.Label.Save"
              variant="primary"
              onClick={handleSubmit(onSubmit)}
              isLoading={isSaving}
            />
          </ModalActions>
        </>
      )}
    </Modal>
  );
};

export default ThiDauVaoDetailModal;
