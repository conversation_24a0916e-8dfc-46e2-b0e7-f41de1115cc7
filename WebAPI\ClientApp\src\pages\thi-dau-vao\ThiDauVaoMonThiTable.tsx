import React, { useState, useEffect, useImperativeHandle, forwardRef } from "react";
import {
  Box,
  TextInput as PrimerTextInput,
  Text,
} from "@primer/react";


import { NotifyHelper } from "../../helpers/toast";
import DataTable from "../../components/ui/data-table";

import { ketQuaThiDauVaoChiTietApi } from "../../api/ketQuaThiDauVaoChiTietApi";
import { KetQuaThiDauVaoChiTietItemResponse, ex_ketquathidauvao_chitiet } from "../../models/response/quan-ly-thi/ex_ketquathidauvao_chitiet";

interface ThiDauVaoMonThiTableProps {
  ketQuaThiDauVaoId: number;
  onDataChange?: () => void;
  isEditMode?: boolean;
  onSaveAll?: () => void;
  onCancelAll?: () => void;
}

export interface ThiDauVaoMonThiTableRef {
  saveAll: () => Promise<void>;
  cancelAll: () => void;
}

interface EditableRow {
  id: number;
  isEditing: boolean;
  diem_thi: string;
  diem_thi_cuoi: string;
  nhan_xet: string;
  ghi_chu: string;
}

const ThiDauVaoMonThiTable = forwardRef<ThiDauVaoMonThiTableRef, ThiDauVaoMonThiTableProps>(({
  ketQuaThiDauVaoId,
  onDataChange,
  isEditMode = false,
  onSaveAll,
  onCancelAll,
}, ref) => {
  const [chiTietData, setChiTietData] = useState<KetQuaThiDauVaoChiTietItemResponse[]>([]);
  const [editableRows, setEditableRows] = useState<{ [key: number]: EditableRow }>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (ketQuaThiDauVaoId > 0) {
      loadData();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ketQuaThiDauVaoId]);

  // Cập nhật trạng thái edit khi isEditMode thay đổi
  useEffect(() => {
    setEditableRows(prev => {
      const updated = { ...prev };
      Object.keys(updated).forEach(key => {
        updated[parseInt(key)].isEditing = isEditMode;
      });
      return updated;
    });
  }, [isEditMode]);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    saveAll: handleSaveAll,
    cancelAll: handleCancelAll,
  }));

  const loadData = async () => {
    setIsLoading(true);
    try {
      const res = await ketQuaThiDauVaoChiTietApi.selectByKetQuaThiDauVao(ketQuaThiDauVaoId);
      if (res.is_success) {
        setChiTietData(res.data || []);
        // Initialize editable rows state
        const editableState: { [key: number]: EditableRow } = {};
        (res.data || []).forEach((item: KetQuaThiDauVaoChiTietItemResponse) => {
          editableState[item.id] = {
            id: item.id,
            isEditing: isEditMode, // Sử dụng isEditMode từ props
            diem_thi: item.diem_thi || "",
            diem_thi_cuoi: item.diem_thi_cuoi || "",
            nhan_xet: item.nhan_xet || "",
            ghi_chu: item.ghi_chu || "",
          };
        });
        setEditableRows(editableState);
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi tải dữ liệu");
    } finally {
      setIsLoading(false);
    }
  };



  const handleFieldChange = (id: number, field: keyof EditableRow, value: string) => {
    setEditableRows(prev => ({
      ...prev,
      [id]: { ...prev[id], [field]: value }
    }));
  };

  const handleSaveAll = async () => {
    try {
      const promises = Object.values(editableRows).map(async (editableRow) => {
        const updateData: ex_ketquathidauvao_chitiet = {
          id: editableRow.id,
          ex_ketquathidauvao_id: ketQuaThiDauVaoId,
          ex_monthidauvao_id: chiTietData.find(item => item.id === editableRow.id)?.ex_monthidauvao_id || 0,
          diem_thi: editableRow.diem_thi,
          diem_thi_cuoi: editableRow.diem_thi_cuoi,
          nhan_xet: editableRow.nhan_xet,
          ghi_chu: editableRow.ghi_chu,
        };

        return await ketQuaThiDauVaoChiTietApi.update(updateData);
      });

      const results = await Promise.all(promises);
      const hasError = results.some(res => !res.is_success);

      if (!hasError) {
        NotifyHelper.Success("Thành công");

        // Cập nhật dữ liệu local và tắt chế độ edit cho tất cả rows
        setChiTietData(prev => prev.map(item => {
          const editableRow = editableRows[item.id];
          return editableRow ? {
            ...item,
            diem_thi: editableRow.diem_thi,
            diem_thi_cuoi: editableRow.diem_thi_cuoi,
            nhan_xet: editableRow.nhan_xet,
            ghi_chu: editableRow.ghi_chu,
          } : item;
        }));

        setEditableRows(prev => {
          const updated = { ...prev };
          Object.keys(updated).forEach(key => {
            updated[parseInt(key)].isEditing = false;
          });
          return updated;
        });

        onDataChange?.();
        onSaveAll?.();
      } else {
        NotifyHelper.Error("Có lỗi xảy ra khi lưu một số dữ liệu");
      }
    } catch (error) {
      console.error(error);
      NotifyHelper.Error("Có lỗi xảy ra khi lưu dữ liệu");
    }
  };

  const handleCancelAll = () => {
    // Reset về dữ liệu gốc
    const editableState: { [key: number]: EditableRow } = {};
    chiTietData.forEach((item) => {
      editableState[item.id] = {
        id: item.id,
        isEditing: false,
        diem_thi: item.diem_thi || "",
        diem_thi_cuoi: item.diem_thi_cuoi || "",
        nhan_xet: item.nhan_xet || "",
        ghi_chu: item.ghi_chu || "",
      };
    });
    setEditableRows(editableState);
    onCancelAll?.();
  };

  const renderEditableCell = (data: KetQuaThiDauVaoChiTietItemResponse, field: keyof EditableRow, placeholder: string) => {
    const editableRow = editableRows[data.id];
    const isEditing = editableRow?.isEditing || false;
    const value = editableRow?.[field];
    const stringValue = typeof value === 'string' ? value : String(value || "");

    if (isEditing) {
      return (
        <PrimerTextInput
          value={stringValue}
          onChange={(e) => handleFieldChange(data.id, field, e.target.value)}
          placeholder={placeholder}
          size="small"
          width="100%"
          sx={{ fontSize: 1 }}
        />
      );
    }

    return <Text sx={{ fontSize: 1 }}>{stringValue}</Text>;
  };



  return (
    <Box>
      <DataTable
        columns={[
          {
            dataField: "mon_thi",
            caption: "Môn thi",
            width: 150,
            cellRender: (data) => <Text sx={{ fontSize: 1 }}>{data.mon_thi}</Text>,
          },
          {
            dataField: "diem_thi",
            caption: "Điểm",
            width: 150,
            cellRender: (data) => renderEditableCell(data, "diem_thi", "Điểm"),
          },
          {
            dataField: "diem_thi_cuoi",
            caption: "Điểm cuối",
            width: 150,
            cellRender: (data) => renderEditableCell(data, "diem_thi_cuoi", "Điểm cuối"),
          },
          {
            dataField: "nhan_xet",
            caption: "Nhận xét",
            width: 300,
            cellRender: (data) => renderEditableCell(data, "nhan_xet", "Nhận xét"),
          },
          {
            dataField: "ghi_chu",
            caption: "Ghi chú",
            isMainColumn: true,
            cellRender: (data) => renderEditableCell(data, "ghi_chu", "Ghi chú"),
          },
        ]}
        data={chiTietData}
        title=""
        subTitle=""
        isLoading={isLoading}
        searchEnable={false}
      />
    </Box>
  );
});

ThiDauVaoMonThiTable.displayName = "ThiDauVaoMonThiTable";

export default ThiDauVaoMonThiTable;
