import React, { useState } from "react";
import { Box, FormControl } from "@primer/react";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import { NotifyHelper } from "../../helpers/toast";
import { hocSinh<PERSON>pi } from "../../api/hocSinhApi";
import { IHocSinh } from "../../models/response/crm-hocsinh/IHocsinh";
import { IHocSinhSelectRequest } from "../../models/request/hoc-sinh/IHocSinhSelectRequest";
import SelectBoxHocSinh from "../../components/selectbox-hocsinh";
import ThiDauVao from "./ThiDauVao";

const ThiDauVaoPage: React.FC = () => {
  const [selectedHocSinh, setSelectedHocSinh] = useState<IHocSinh | null>(null);
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

  const handleHocSinhChange = async (id: number) => {
    if (id > 0) {
      try {
        const request: IHocSinhSelectRequest = {
          dm_coso_id: dm_coso_id,
          nam_hoc: nam_hoc,
          dm_truong_id: 0,
          dm_khoi_id: 0,
          dm_he_id: 0,
          dm_lop_id: 0,
          searchText: id.toString(),
        };
        const res = await hocSinhApi.SelectViewShort(request);
        if (res.is_success && res.data && res.data.length > 0) {
          setSelectedHocSinh(res.data[0]);
        } else {
          setSelectedHocSinh(null);
          NotifyHelper.Error("Không tìm thấy thông tin học sinh");
        }
      } catch (error) {
        console.error(error);
        NotifyHelper.Error("Có lỗi xảy ra khi tải thông tin học sinh");
      }
    } else {
      setSelectedHocSinh(null);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ mb: 4 }}>
        <FormControl>
          <FormControl.Label>Chọn học sinh</FormControl.Label>
          <SelectBoxHocSinh
            onValueChanged={handleHocSinhChange}
          />
        </FormControl>
      </Box>

      {selectedHocSinh && (
        <ThiDauVao hocSinh={selectedHocSinh} />
      )}

      {/* Test modal removed */}
    </Box>
  );
};

export default ThiDauVaoPage;
