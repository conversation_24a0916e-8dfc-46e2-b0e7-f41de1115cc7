import React, { useEffect, useRef, useState } from "react";
import { Box, Link, TextInput } from "@primer/react";
import { CheckIcon, DownloadIcon, UploadIcon, XIcon } from "@primer/octicons-react";
import { uploadApi } from '../../../api/uploadApi';
import { ketQuaThiDauVaoApi } from '../../../api/ketQuaThiDauVaoApi';
import { appInfo } from '../../../AppInfo';
import { AnimationPopup } from '../../../components/modal';
import DataTable from '../../../components/data-table/DataTable';
import { PopUpFormActions } from '../../../components/pop-up-form/PopUpForm';
import Steps, { IStepData } from '../../../components/steps/Steps';
import Button from '../../../components/ui/button';
import { useCommonContext } from '../../../contexts/common';
import { NotifyHelper } from '../../../helpers/toast';
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";

interface IImportThiDauVaoModalProps {
    onClose: () => void;
    animationOf: string;
}

const _steps: IStepData[] = [
    {
        id: 1,
        name: "Upload file",
        is_active: true
    },
    {
        id: 2,
        name: "Validate",
        is_active: false
    },
    {
        id: 3,
        name: "Import",
        is_active: false
    }
];

const ImportThiDauVaoModal = (props: IImportThiDauVaoModalProps) => {
    const { translate } = useCommonContext();
    const { onClose, animationOf } = props;

    const [stepId, setStepId] = useState(1);
    const [uploadedFileName, setUploadedFileName] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const componentDidMount = useRef<boolean>(false);
    const [dataSourceValidated, setDataSourceValidated] = useState<any[]>([]);
    const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

    useEffect(() => {
        componentDidMount.current = true;
        return () => {
            componentDidMount.current = false;
        };
    }, []);

    const handleSelectFile = () => {
        const input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.setAttribute(
            'accept',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'
        );
        input.click();
        input.onchange = async () => {
            var file = input && input.files ? input.files[0] : null;
            if (file) {
                setIsLoading(true);
                const res = await uploadApi.uploadExcel(file);
                if (res.is_success) {
                    setUploadedFileName(res.data.fileNameId);
                    setStepId(2);
                } else {
                    NotifyHelper.Error(res.message ?? "Error");
                }
                setIsLoading(false);
            }
        };
        input.remove();
    };

    useEffect(() => {
        if (stepId === 2 && uploadedFileName) {
            handleReadUploadedFile();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [stepId, uploadedFileName]);

    const handleReadUploadedFile = async () => {
        setIsLoading(true);
        const res = await uploadApi.readExcel({
            fileName: uploadedFileName,
            sheetIndex: 0,
        });

        if (!componentDidMount.current) return;

        if (res.is_success) {
            await handleValidateImportFile();
        }
        setIsLoading(false);
    };

    const handleValidateImportFile = async () => {
      setIsLoading(true);
      const datasend = {
          fileName: uploadedFileName,
          sheetIndex: 0
      };
      const res = await ketQuaThiDauVaoApi.validateImport(datasend, nam_hoc, dm_coso_id);
      if (!componentDidMount.current) return;
      if (res.is_success) {
          setDataSourceValidated(res.data);
      }
      setIsLoading(false);
    };

    const handleImport = async () => {
        setIsLoading(true);
        const datasend = {
            fileName: uploadedFileName,
            sheetIndex: 0
        };
        const res = await ketQuaThiDauVaoApi.import(datasend, nam_hoc, dm_coso_id);
        if (!componentDidMount.current) return;
        if (res.is_success) {
            setStepId(4); // Chuyển sang bước 4 sau khi lưu thành công
            NotifyHelper.Success("Import dữ liệu thành công!");
        } else {
            NotifyHelper.Error(res.message || "Có lỗi xảy ra khi import dữ liệu");
        }
        setIsLoading(false);
    };

    // Calculate if there are validation errors
    const notValidCount = dataSourceValidated.filter(x => x.err && x.err !== "").length;

    return (
        <AnimationPopup
            animationOf={animationOf}
            width="100%"  // Set width to 100% - this prop seems to be accepted
            title='Import kết quả thi đầu vào'
            onClose={onClose}
        >
            <Box style={{
                display: "flex",
                flexDirection: "column",
                height: "calc(100vh - 220px)",
                overflow: "hidden"
            }}>
                <Box style={{ marginBottom: "24px" }}>
                    <Steps steps={_steps.map(x => ({
                        ...x,
                        is_active: x.id === stepId
                    }))} />
                </Box>

                <Box style={{ display: "flex", marginBottom: "24px" }}>
                    <div style={{ display: 'flex', gap: '8px', alignItems: 'center', width: '100%' }}>
                        <Button
                            leadingVisual={UploadIcon}
                            text='Upload file'
                            onClick={handleSelectFile}
                            size='medium'
                            isLoading={isLoading}
                        />
                        {uploadedFileName && (
                            <TextInput
                                style={{ width: 400 }}
                                value={uploadedFileName}
                                readOnly
                            />
                        )}
                        <Box style={{ flex: 1 }} />
                        <Link href={`${appInfo.baseApiURL}/template/thidauvao`} target='_blank'>
                            <Button
                                text={translate('Tải mẫu import thi đầu vào tại đây')}
                                variant='invisible'
                                leadingVisual={DownloadIcon}
                            />
                        </Link>
                    </div>
                </Box>

                {stepId === 2 && (
                    <Box style={{ flex: 1, overflow: "auto" }}>
                        <DataTable
                            columns={[
                                {
                                    field: "status",
                                    header: "",
                                    width: "40px",
                                    renderCell: () => (
                                        <Box sx={{
                                            color: 'success.fg',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }} />
                                    )
                                },
                                { field: "Năm học", header: "Năm học", width: "100px" },
                                { field: "Mã học sinh", header: "Mã học sinh", width: "120px" },
                                { field: "Lần thi", header: "Lần thi", width: "80px" },
                                { field: "Ngày thi", header: "Ngày thi", width: "120px" },
                                { field: "Ghi chú lần thi", header: "Ghi chú lần thi", width: "150px" },
                                { field: "Môn thi đầu vào", header: "Môn thi đầu vào", width: "150px" },
                                { field: "Điểm thi", header: "Điểm thi", width: "100px" },
                                { field: "Điểm thi cuối", header: "Điểm thi cuối", width: "100px" },
                                { field: "Nhận xét", header: "Nhận xét", width: "150px" },
                                { field: "Ghi chú môn thi", header: "Ghi chú môn thi", width: "150px" },
                                { field: "err", header: "Lỗi", width: "300px" }
                            ]}
                            data={dataSourceValidated}
                            height="calc(100% - 60px)" // Adjust for the sticky footer
                        />
                    </Box>
                )}

                {stepId === 4 && (
                    <Box style={{ flex: 1, overflow: "auto" }}>                      
                        <DataTable
                            columns={[
                                {
                                    field: "status",
                                    header: "",
                                    width: "40px",
                                    renderCell: (_data: any) => (
                                        <Box sx={{
                                            color: 'success.fg',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}>
                                            <CheckIcon />
                                        </Box>
                                    )
                                },
                                { field: "Năm học", header: "Năm học", width: "100px" },
                                { field: "Mã học sinh", header: "Mã học sinh", width: "120px" },
                                { field: "Lần thi", header: "Lần thi", width: "80px" },
                                { field: "Ngày thi", header: "Ngày thi", width: "120px" },
                                { field: "Ghi chú lần thi", header: "Ghi chú lần thi", width: "150px" },
                                { field: "Môn thi đầu vào", header: "Môn thi đầu vào", width: "150px" },
                                { field: "Điểm thi", header: "Điểm thi", width: "100px" },
                                { field: "Điểm thi cuối", header: "Điểm thi cuối", width: "100px" },
                                { field: "Nhận xét", header: "Nhận xét", width: "150px" },
                                { field: "Ghi chú môn thi", header: "Ghi chú môn thi", width: "150px" }
                            ]}
                            data={dataSourceValidated}
                            height="calc(100% - 60px)" // Adjust for the sticky footer
                        />
                    </Box>
                )}

                <div style={{
                    position: 'sticky',
                    bottom: 0,
                    backgroundColor: 'white',
                    padding: '16px 0',
                    borderTop: '1px solid #e1e4e8'
                }}>
                    <PopUpFormActions>
                        <Button
                            leadingVisual={XIcon}
                            text='Đóng'
                            onClick={onClose}
                        />
                        {stepId === 2 && (
                            <Button
                                variant='primary'
                                isLoading={isLoading}
                                text='Tiếp tục'
                                onClick={handleImport}
                                disabled={notValidCount > 0}
                            />
                        )}
                    </PopUpFormActions>
                </div>
            </Box>
        </AnimationPopup>
    );
};

export default ImportThiDauVaoModal;
