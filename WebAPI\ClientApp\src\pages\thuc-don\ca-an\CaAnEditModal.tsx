import { Box, Checkbox, FormControl } from '@primer/react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import Button from '../../../components/ui/button';
import Modal from '../../../components/ui/modal';
import ModalActions from '../../../components/ui/modal/ModalActions';
import Text from '../../../components/ui/text';
import TextInput from '../../../components/ui/text-input';
import { useCommonContext } from '../../../contexts/common';
import { useCommonSelectedHook } from '../../../hooks/useCommonSelectedHook';
import { ePageBaseStatus } from '../../../models/ePageBaseStatus';
import { IFoCaAn } from '../../../models/response/thuc-don/IFoCaAn';
import { actions } from '../../../state/actions/actionsWrapper';
import { RootState } from '../../../state/reducers';

const CaAnEditModal = () => {
    const { status, editingData } = useSelector((x: RootState) => x.thucDonWrapper.caAn)
    const dispatch = useDispatch();
    const { dm_coso_id } = useCommonSelectedHook();
    const { translate } = useCommonContext();
    const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm<IFoCaAn>({
        defaultValues: editingData ? {
            ...editingData,
        } : undefined
    })
    const onSubmit = async (data: any) => {
        dispatch(actions.thucDonWrapper.caAn.SAVE_START({
            ...data,
            id: data?.id ?? 0,
            dm_coso_id: dm_coso_id
        }))
    }

    return (
        <Modal isOpen
            title={editingData ? "Base.Label.Edit" : "Base.Label.AddNew"}
            onClose={() => {
                dispatch(actions.thucDonWrapper.caAn.CLOSE_EDIT_MODAL(undefined))
            }}
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box sx={{ display: "grid", gap: 2 }}>
                    <FormControl>
                        <FormControl.Label><Text text='Tên ca ăn (Vi)' /></FormControl.Label>
                        <TextInput block
                            name="ca_an"
                            register={register}
                            errors={errors}
                            required
                            validateMessage={`${translate("Base.Form.RequiredMessage")} ${translate("tên ca ăn (Vi)")}`}
                        />

                    </FormControl>
                    <FormControl>
                        <FormControl.Label><Text text='Tên ca ăn (En)' /></FormControl.Label>
                        <TextInput block
                            name="ca_an_en"
                            register={register}
                            errors={errors}
                            required
                            validateMessage={`${translate("Base.Form.RequiredMessage")} ${translate("tên ca ăn (En)")}`}
                        />
                    </FormControl>
                    <FormControl>
                        <FormControl.Label><Text text='Thứ tự' /></FormControl.Label>
                        <TextInput block
                            type='number'
                            name="order_idx"
                            register={register}
                            errors={errors}
                            required
                            validateMessage={`${translate("Base.Form.RequiredMessage")} ${translate("thứ tự")}`}
                        />
                    </FormControl>
                    {/* <FormControl>
                        <FormControl.Label><Text text='caanType.Name' /></FormControl.Label>
                        <ComboboxcaanType
                            width={'100%'}
                            value={watch("dm_caan_type_id", editingData?.dm_caan_type_id)}
                            onValueChanged={(value) => setValue("dm_caan_type_id", value)}
                        />

                    </FormControl>
                    <FormControl>
                        <FormControl.Label><Text text='Code' /></FormControl.Label>
                        <TextInput block
                            name="viet_tat"
                            register={register}
                            errors={errors}
                            required
                        />
                    </FormControl>
                    <FormControl>
                        <FormControl.Label>Show campaign</FormControl.Label>
                        <Checkbox checked={watch("is_show_campaign", editingData?.is_show_campaign)}
                            onChange={(e) => {
                                setValue("is_show_campaign", e.target.checked)
                            }}
                        />
                    </FormControl> */}
                </Box>

                <ModalActions>
                    <Button text='Base.Label.Close' onClick={() => {
                        dispatch(actions.thucDonWrapper.caAn.CLOSE_EDIT_MODAL(undefined))
                    }} />
                    <Button text='Base.Label.Update' variant='primary' type='submit'
                        isLoading={status === ePageBaseStatus.is_saving}
                    />
                </ModalActions>
            </form>

        </Modal>
    );
};

export default CaAnEditModal;