import {
  KebabHorizontalIcon,
  PencilIcon,
  PlusIcon,
  StackIcon,
  SyncIcon,
  TrashIcon,
} from "@primer/octicons-react";
import {
  ActionList,
  ActionMenu,
  Box,
  Checkbox,
  useConfirm,
} from "@primer/react";
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Button from "../../../components/ui/button";
import DataTable from "../../../components/ui/data-table";
import { useCommonSelectedHook } from "../../../hooks/useCommonSelectedHook";
import { ePageBaseStatus } from "../../../models/ePageBaseStatus";
import { actions } from "../../../state/actions/actionsWrapper";

import Text from "../../../components/ui/text";
import { useCommonContext } from "../../../contexts/common";
import { RootState } from "../../../state/reducers";
import CaAnEditModal from "./CaAnEditModal";
import { IFoCaAn } from "../../../models/response/thuc-don/IFoCaAn";
import UserAvatar from "../../../components/user-avatar";

const CaAnPage = () => {
  const { status, foCaAns, isShowEditModal, editingData } = useSelector(
    (x: RootState) => x.thucDonWrapper.caAn
  );
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();
  const { translate } = useCommonContext();
  const confirm = useConfirm();
  const dispatch = useDispatch();
  useEffect(() => {
    if (
      status === ePageBaseStatus.is_not_initialization ||
      status === ePageBaseStatus.is_need_reload
    ) {
      handleReload();
    }
  }, [status]);
  const filteredData = useMemo(() => {
    if (foCaAns.length > 0 && dm_coso_id) {
      return foCaAns.filter(x => x.dm_coso_id === dm_coso_id);
    }
    return foCaAns;
  }, [dm_coso_id, foCaAns]);
  const handleReload = () => {
    dispatch(actions.thucDonWrapper.caAn.LOAD_START(undefined));
  };
  const handleDelete = async (id: number) => {
    if (
      await confirm({
        content: translate("Base.Label.DeleteConfirm.Content"),
        title: translate("Base.Label.Confirm"),
        cancelButtonContent: translate("Base.Label.Close"),
        confirmButtonContent: translate("Base.Label.Delete"),
        confirmButtonType: "danger",
      })
    ) {
      dispatch(actions.thucDonWrapper.caAn.DELETE_START(id));
    }
  };

  return (
    <Box
      sx={{
        p: 3,
      }}
    >
      <DataTable
        height={`${window.innerHeight - 260}px`}
        data={filteredData}
        isLoading={status === ePageBaseStatus.is_loading}
        title={"Danh sách ca ăn"}
        searchEnable
        paging={{
            enable: true,
            pageSizeItems: [20, 50, 200, 500, 1000],
        }}
        actionComponent={
          <Box
            sx={{
              display: "flex",
            }}
          >
             <Button
              text="Base.Label.Refresh"
              leadingVisual={SyncIcon}
              size="medium"
              sx={{ ml: 1 }}
              onClick={() => {
                handleReload();
              }}
            />
            <Button
              text="Base.Label.AddNew"
              leadingVisual={PlusIcon}
              size="medium"
              variant="primary"
              onClick={() => {
                dispatch(
                  actions.thucDonWrapper.caAn.SHOW_EDIT_MODAL(undefined)
                );
              }}
            />

          </Box>
        }
        columns={[
          {
            dataField: "ca_an",
            caption: translate("Tên ca ăn (Vi)"),
            cellRender: (data) => (
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box
                  sx={{
                    display: "inline-flex",
                    verticalAlign: "sub",
                    lineHeight: 1,
                    mr: 1,
                  }}
                >
                  <div style={{ color: "#62825D" }}>
                    <StackIcon size={14} />{" "}
                  </div>
                </Box>

                <span style={{ fontWeight: "500" }}>{data.ca_an}</span>
              </Box>
            ),
            // cellRender: (data: IDmcaanVm) => {
            //     return (
            //         <Box sx={{
            //             display: "flex",
            //             flexDirection: "column",
            //             alignItems: "baseline"
            //         }}>

            //             <span style={{ fontWeight: '600' }}>{data.name}</span>
            //             <Label sx={{
            //                 mt: 2, backgroundColor:
            //                     data.dm_caan_type_id === 1
            //                         ? '#B2F7B2'
            //                         : data.dm_caan_type_id === 2 ? '#FFFBD3'
            //                             : data.dm_caan_type_id === 3 ? '#FDFFCD'
            //                                 : data.dm_caan_type_id === 4 ? '#F1CDFF'
            //                                     : data.dm_caan_type_id === 5 ? '#FFDDE2'
            //                                         : '#E0E0E0',
            //                 color: '#505050'
            //             }}>
            //                 {data.caan_type}
            //             </Label>
            //         </Box>
            //     );
            // }
          },
          {
            dataField: "ca_an_en",
            caption: translate("Tên ca ăn (En)"),
            // cellRender: (data: IDmcaanVm) => {
            //     return (
            //         <Box sx={{
            //             display: "flex",
            //             flexDirection: "column",
            //             alignItems: "baseline"
            //         }}>

            //             <span style={{ fontWeight: '600' }}>{data.name_en}</span>
            //             <Label sx={{
            //                 mt: 2, backgroundColor:
            //                     data.dm_caan_type_id === 1
            //                         ? '#B2F7B2'
            //                         : data.dm_caan_type_id === 2 ? '#FFFBD3'
            //                             : data.dm_caan_type_id === 3 ? '#FDFFCD'
            //                                 : data.dm_caan_type_id === 4 ? '#F1CDFF'
            //                                     : data.dm_caan_type_id === 5 ? '#FFDDE2'
            //                                         : '#E0E0E0',
            //                 color: '#505050'
            //             }}>
            //                 {data.caan_type_en}
            //             </Label>
            //         </Box>
            //     );
            // }
          },
          {
            dataField: "order_idx",
            caption: translate("Thứ tự hiển thị"),
            align: "center",
          },
          {
            id: "cmd",
            caption: "",
            width: "50px",
            align: "center",
            cellRender: (data: IFoCaAn) => {
              return (
                <ActionMenu>
                  <ActionMenu.Button
                    icon={KebabHorizontalIcon}
                    variant="invisible"
                    sx={{ m: -1 }}
                  >
                    &nbsp;
                  </ActionMenu.Button>
                  <ActionMenu.Overlay width="auto">
                    <ActionList>
                      <ActionList.Item
                        onSelect={() => {
                          dispatch(
                            actions.thucDonWrapper.caAn.SHOW_EDIT_MODAL(data)
                          );
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <PencilIcon />
                        </ActionList.LeadingVisual>
                        <Text text="Base.Label.Edit" />
                      </ActionList.Item>
                      <ActionList.Item
                        variant="danger"
                        onSelect={() => {
                          handleDelete(data.id);
                        }}
                      >
                        <ActionList.LeadingVisual>
                          <TrashIcon />
                        </ActionList.LeadingVisual>
                        <Text text="Base.Label.Delete" />
                      </ActionList.Item>
                    </ActionList>
                  </ActionMenu.Overlay>
                </ActionMenu>
              );
            },
          },
        ]}
      />
      {isShowEditModal && <CaAnEditModal />}
    </Box>
  );
};

export default CaAnPage;
