import {
  KebabHorizontalIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
} from "@primer/octicons-react";
import {
  ActionList,
  ActionMenu,
  Box,
  Checkbox,
  IconButton,
  useConfirm,
} from "@primer/react";
import { useEffect, useState } from "react";
import { formApi } from "../../api/formApi";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { IForm } from "../../models/response/form/IForm";

import { trangThaiHocSinhApi } from "../../api/trangThaiHocSinhApi";
import { ITrangThaiHocSinhCoSo } from "../../models/response/category/ITrangThaiHocSinhCoSo";
import { ITrangThaiHocSinhCoSoItemResponse } from "../../models/response/category/ITrangThaiHocSinhCoSoItemResponse";
import TrangThaiHocSinhDetailModal from "./TrangThaiHocSinhDetailModal";
import TextWithEllipse from "../../components/ui/text-with-ellipse";

const TrangThaiHocSinh = () => {
  const [isShowModal, setIsShowModal] = useState(false);
  const [isShowDetailModalDangKy, setIsShowDetailModalDangKy] = useState(false);

  const [selectedId, setSelectedId] = useState(0);
  const [tranghtaihocsinhs, setTrangthaihocsinhs] = useState<
    ITrangThaiHocSinhCoSoItemResponse[]
  >([]);
  const [tsForms, setTsForms] = useState<IForm[]>([]);
  const { translate } = useCommonContext();
  const [isSaving, setIsSaving] = useState(false);
  const confirm = useConfirm();

  useEffect(() => {
    handleReloadAsync();
    handleReloadTsForms();
  }, []);
  const handleReloadAsync = async () => {
    const res = await trangThaiHocSinhApi.selectAllCheckDangKy(1);
    if (res.is_success) {
      setTrangthaihocsinhs(res.data);
    } else {
      NotifyHelper.Error(res.message ?? "");
    }
  };
  const handleReloadTsForms = async () => {
    const res = await formApi.load();
    if (res.is_success) {
      setTsForms(res.data);
    }
  };
  const handeDelete = async (id: number) => {
    if (
      await confirm({
        content: `${translate("Base.Label.ConfirmDelete")}`,
        title: `${translate("Base.Label.Warning")}`,
        cancelButtonContent: `${translate("Base.Label.No")}`,
        confirmButtonContent: `${translate("Base.Label.Yes")}`,
        confirmButtonType: "danger",
      })
    ) {
      const res = await trangThaiHocSinhApi.delete(id);
      if (res.is_success) {
        NotifyHelper.Success("Success");
        handleReloadAsync();
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    }
  };
  return (
    <Box sx={{ p: 3 }}>
      <DataTable
        height={`${window.innerHeight - 250}px`}
        columns={[
          {
            id: "cmd",
            width: "50px",
            caption: "#",
            align: "center",
            cellRender: (data: ITrangThaiHocSinhCoSo) => {
              return (
                <Box sx={{ m: -1 }}>
                  <ActionMenu>
                    <ActionMenu.Anchor>
                      <IconButton
                        icon={KebabHorizontalIcon}
                        variant="invisible"
                        aria-label="Open column options"
                      />
                    </ActionMenu.Anchor>

                    <ActionMenu.Overlay>
                      <ActionList>
                        <ActionList.Item
                          onSelect={() => {
                            // dispatch(actions.soDiem.soDiemList.changeEditingData(item))
                            setIsShowModal(true);
                            setSelectedId(data.id);
                          }}
                        >
                          <ActionList.LeadingVisual>
                            <PencilIcon />
                          </ActionList.LeadingVisual>
                          <TextTranslated value="Base.Label.Edit" />
                        </ActionList.Item>
                        <ActionList.Divider />
                        <ActionList.Item
                          variant="danger"
                          onSelect={() => {
                            handeDelete(data.id);
                          }}
                        >
                          <ActionList.LeadingVisual>
                            <TrashIcon />
                          </ActionList.LeadingVisual>
                          <TextTranslated value="Base.Label.Delete" />
                        </ActionList.Item>
                      </ActionList>
                    </ActionMenu.Overlay>
                  </ActionMenu>
                </Box>
              );
            },
          },
          {
            dataField: "thu_tu",
            width: "100px",
            caption: `${translate("Base.Label.ThuTu")}`,
            cellRender: (data: ITrangThaiHocSinhCoSoItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.thu_tu}
                </Box>
              );
            },
          },
          {
            dataField: "trang_thai",
            width: "200px",

            isMainColumn: true,
            caption: `${translate("Base.Label.TenTrangThaiVi")}`,
            cellRender: (data: ITrangThaiHocSinhCoSoItemResponse) => {
              return (
                <TextWithEllipse text={data.trang_thai || ""} lineNumber={1} />
              );
            },
          },
          {
            dataField: "trang_thai_en",
            width: "200px",

            caption: `Trạng thái (En)`,
            cellRender: (data: ITrangThaiHocSinhCoSoItemResponse) => {
               return (
                <TextWithEllipse text={data.trang_thai_en || ""} lineNumber={1} />
              );
            },
          },
          {
            dataField: "mo_ta_tt_vi",
            width: "400px",
            caption: `${translate("Base.Label.MotaVi")}`,
            cellRender: (data: ITrangThaiHocSinhCoSoItemResponse) => {
               return (
                <TextWithEllipse text={data.mo_ta_tt_vi || ""} lineNumber={1} />
              );
            },
          },
          {
            dataField: "mo_ta_tt_en",
            width: "400px",
            caption: `${translate("Base.Label.MoTaEn")}`,
            cellRender: (data: ITrangThaiHocSinhCoSoItemResponse) => {
              return (
                <TextWithEllipse text={data.mo_ta_tt_en || ""} lineNumber={1} />
              );
            },
          },

          {
            dataField: "isDuTuyen",
            width: "100px",
            caption: `${translate("Base.Label.DangHoc")}`,
            //caption: `${translate('Base.Label.NamHoc')}`,
            cellRender: (data: ITrangThaiHocSinhCoSoItemResponse) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                  }}
                >
                  <Checkbox
                    checked={data.isDuTuyen === true || false}
                    color="primary"
                  />
                </Box>
              );
            },
          },
        ]}
        data={tranghtaihocsinhs}
        title={`${translate("Base.Label.TrangThaiHocSinh")}`}
        subTitle={`${translate("Base.Label.TotalCount")}: ${
          tranghtaihocsinhs.length
        }`}
        searchEnable
        actionComponent={
          <>
            <Button
              text={translate("Base.Label.Create")}
              leadingVisual={PlusIcon}
              variant="primary"
              size="medium"
              onClick={() => {
                setIsShowModal(true);
                setSelectedId(0);
              }}
            />
          </>
        }
      />
      {isShowModal && (
        <TrangThaiHocSinhDetailModal
          id={selectedId}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReloadAsync();
            setIsShowModal(false);
          }}
        />
      )}
    </Box>
  );
};

export default TrangThaiHocSinh;
