import {
  KebabHorizontalIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
} from "@primer/octicons-react";
import {
  ActionList,
  ActionMenu,
  Box,
  IconButton,
  useConfirm,
} from "@primer/react";
import { useEffect, useState } from "react";
import { truong<PERSON>pi } from "../../api/truongApi";
import TextTranslated from "../../components/text";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { IForm } from "../../models/response/form/IForm";
import { ITruong } from "../../models/response/truong/ITruong";
import TrangThaiHocSinhDetailModal from "./TruongDetailModal";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";
import TextWithEllipse from "../../components/ui/text-with-ellipse";

const Truong = () => {
  const [isShowModal, setIsShowModal] = useState(false);
  const [isShowDetailModalDangKy, setIsShowDetailModalDangKy] = useState(false);
  const { dm_coso_id } = useCommonSelectedHook();

  const [selectedId, setSelectedId] = useState(0);
  const [truongs, setTruongs] = useState<ITruong[]>([]);
  const [tsForms, setTsForms] = useState<IForm[]>([]);
  const { translate } = useCommonContext();
  const [isSaving, setIsSaving] = useState(false);
  const confirm = useConfirm();

  useEffect(() => {
    handleReloadAsync();
  }, [dm_coso_id]);
  const handleReloadAsync = async () => {
    const res = await truongApi.selectAll();
    if (res.is_success) {
      if (dm_coso_id) {
        const filteredData = res.data.filter(
          (truong: ITruong) => truong.dm_coso_id === dm_coso_id
        );
        setTruongs(filteredData);
      } else {
        setTruongs(res.data);
      }
    } else {
      NotifyHelper.Error(res.message ?? "");
    }
  };

  const handeDelete = async (id: number) => {
    if (
      await confirm({
        content: "Bạn có chắc chắn muốn xóa trường này?",
        title: "Lưu ý",
        cancelButtonContent: "Không xóa",
        confirmButtonContent: "Xóa trường",
        confirmButtonType: "danger",
      })
    ) {
      const res = await truongApi.delete(id);
      if (res.is_success) {
        NotifyHelper.Success("Success");
        handleReloadAsync();
      } else {
        NotifyHelper.Error(res.message ?? "");
      }
    }
  };
  return (
    <Box sx={{ p: 3 }}>
      <DataTable
        height={`${window.innerHeight - 250}px`}
        columns={[
          {
            id: "cmd",
            width: "50px",
            caption: "#",
            align: "center",
            cellRender: (data: ITruong) => {
              return (
                <Box sx={{ m: -1 }}>
                  <ActionMenu>
                    <ActionMenu.Anchor>
                      <IconButton
                        icon={KebabHorizontalIcon}
                        variant="invisible"
                        aria-label="Open column options"
                      />
                    </ActionMenu.Anchor>

                    <ActionMenu.Overlay>
                      <ActionList>
                        <ActionList.Item
                          onSelect={() => {
                            // dispatch(actions.soDiem.soDiemList.changeEditingData(item))
                            setIsShowModal(true);
                            if (data.id !== undefined) {
                              setSelectedId(data.id);
                            }
                          }}
                        >
                          <ActionList.LeadingVisual>
                            <PencilIcon />
                          </ActionList.LeadingVisual>
                          <TextTranslated value="Base.Label.Edit" />
                        </ActionList.Item>
                        <ActionList.Divider />
                        <ActionList.Item
                          variant="danger"
                          onSelect={() => {
                            if (data.id !== undefined) {
                              handeDelete(data.id);
                            }
                          }}
                        >
                          <ActionList.LeadingVisual>
                            <TrashIcon />
                          </ActionList.LeadingVisual>
                          <TextTranslated value="Base.Label.Delete" />
                        </ActionList.Item>
                      </ActionList>
                    </ActionMenu.Overlay>
                  </ActionMenu>
                </Box>
              );
            },
          },
          {
            dataField: "ma_truong",
            width: "100px",
            caption: `Mã trường`,
            cellRender: (data: ITruong) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ma_truong}
                </Box>
              );
            },
          },
          {
            dataField: "viet_tat",
            width: "200px",
            caption: `Viết tắt `,
            cellRender: (data: ITruong) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.viet_tat || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "ten_truong",
            width: "200px",
            isMainColumn: true,
            caption: `Tên trường`,
            cellRender: (data: ITruong) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_truong || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "ten_truong_tbtp",
            width: "200px",
            caption: `Tên trường TBTP `,
            cellRender: (data: ITruong) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.ten_truong_tbtp || "..."}
                </Box>
              );
            },
          },
          
          {
            dataField: "dien_thoai",
            width: "100px",
            caption: `Điện thoại `,
            cellRender: (data: ITruong) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.dien_thoai || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "may_le",
            width: "100px",
            caption: `Máy lẻ `,
            cellRender: (data: ITruong) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.may_le || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "ngan_hang",
            width: "200px",
            caption: `Ngân hàng `,
            cellRender: (data: ITruong) => {
              return (
                <TextWithEllipse text={data.ngan_hang || ""} lineNumber={1} />
              );
            },
          },
          {
            dataField: "so_tk",
            width: "200px",
            caption: `Số tài khoản `,
            cellRender: (data: ITruong) => {
              return <TextWithEllipse text={data.so_tk || ""} lineNumber={1} />;
            },
          },
          {
            dataField: "ten_tk",
            width: "200px",
            caption: `Tên tài khoản `,
            cellRender: (data: ITruong) => {
              return (
                <TextWithEllipse text={data.ten_tk || ""} lineNumber={1} />
              );
            },
          },

          {
            dataField: "email",
            width: "300px",
            caption: `Email `,
            cellRender: (data: ITruong) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.email || "..."}
                </Box>
              );
            },
          },
          {
            dataField: "dia_chi_truong",
            width: "500px",
            caption: `Địa chỉ `,
            cellRender: (data: ITruong) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "baseline",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.dia_chi_truong || "..."}
                </Box>
              );
            },
          },
        ]}
        data={truongs}
        title="Trường"
        subTitle={`${translate("Base.Label.TotalCount")}: ${truongs.length} ${
          dm_coso_id ? "(Đã lọc theo cơ sở)" : ""
        }`}
        searchEnable
        actionComponent={
          <>
            <Button
              text={translate("Base.Label.Create")}
              leadingVisual={PlusIcon}
              variant="primary"
              size="medium"
              onClick={() => {
                setIsShowModal(true);
                setSelectedId(0);
              }}
            />
          </>
        }
      />
      {isShowModal && (
        <TrangThaiHocSinhDetailModal
          id={selectedId}
          onClose={() => {
            setIsShowModal(false);
          }}
          onSuccess={() => {
            handleReloadAsync();
            setIsShowModal(false);
          }}
        />
      )}
    </Box>
  );
};

export default Truong;
