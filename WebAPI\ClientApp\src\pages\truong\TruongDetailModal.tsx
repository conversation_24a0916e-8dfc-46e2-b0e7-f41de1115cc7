import { Box, FormControl } from "@primer/react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Button from "../../components/ui/button";
import Modal from "../../components/ui/modal";
import ModalActions from "../../components/ui/modal/ModalActions";
import TextInput from "../../components/ui/text-input";
import { useCommonContext } from "../../contexts/common";
import { NotifyHelper } from "../../helpers/toast";
import { ITruong } from "../../models/response/truong/ITruong";
import { truongApi } from "../../api/truongApi";
import { useCommonSelectedHook } from "../../hooks/useCommonSelectedHook";

interface ITruongDetailModalProps {
  id: number;
  onClose: () => void;
  onSuccess: () => void;
}
const TruongDetailModal = (props: ITruongDetailModalProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { translate } = useCommonContext();
  const { nam_hoc, dm_coso_id } = useCommonSelectedHook();

  const [formData, setFormData] = useState<ITruong>({
    id: props.id,
    ma_truong: "",
    ten_truong: "",
    ten_truong_en: "",
    ten_truong_tbtp: "",
    viet_tat: "",
    dia_chi_truong: "",
    dien_thoai: "",
    email: "",
    may_le: "",
    so_tk: "",
    ten_tk: "",
    ngan_hang: "",
    dm_coso_id: dm_coso_id,
  });

  const {
    register,
    handleSubmit,
    clearErrors,
    reset,
    setError,
    control,
    formState: { errors },
  } = useForm<ITruong>({
    defaultValues: {},
  });
  useEffect(() => {
    if (props.id > 0) {
      handleGetVm();
    } else {
      setFormData((prev) => ({
        ...prev,
        dm_coso_id: dm_coso_id,
      }));
      reset({
        ...formData,
        dm_coso_id: dm_coso_id,
      });
    }
  }, [props.id, dm_coso_id]);
  const handleGetVm = async () => {
    const res = await truongApi.select(props.id);
    if (res.is_success) {
      const data: ITruong = {
        ...res.data,
      };
      setFormData(data);
      reset(data);
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
  };

  const onSubmit = async (data: any) => {
    setIsSaving(true);
    let res: any;
    // Make sure dm_coso_id is included in the data
    const submitData = {
      ...data,
      dm_coso_id: dm_coso_id,
      id: props.id,
    };

    if (props.id > 0) {
      res = await truongApi.update(submitData);
    } else {
      res = await truongApi.insert(submitData);
    }
    if (res.is_success) {
      NotifyHelper.Success("Success");
      props.onSuccess();
    } else {
      NotifyHelper.Error(res.message ?? "Error");
    }
    setIsSaving(false);
  };

  return (
    <Modal
      isOpen
      onClose={props.onClose}
      // width={"60%"}
      sx={{
        width: "60%",
      }}
      title={props.id > 0 ? "Cập nhật" : "Thêm mới"}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Hidden input for dm_coso_id */}
        <input type="hidden" {...register("dm_coso_id")} value={dm_coso_id} />

        <div className="row">
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Mã trường</FormControl.Label>
              <TextInput
                block
                name="ma_truong"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền mã trường"
              />
            </FormControl>
          </div>{" "}
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Viết tắt</FormControl.Label>
              <TextInput
                block
                name="viet_tat"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền tên viết tắt"
              />
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Tên trường</FormControl.Label>
              <TextInput
                block
                name="ten_truong"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền tên trường"
              />
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Tên trường TBTP</FormControl.Label>
              <TextInput
                block
                name="ten_truong_tbtp"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền tên trường TBTP"
              />
            </FormControl>
          </div>{" "}
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Điện thoại</FormControl.Label>
              <TextInput
                block
                name="dien_thoai"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền số điện thoại"
              />
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Máy lẻ</FormControl.Label>
              <TextInput
                block
                name="may_le"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền số máy lẻ"
              />
            </FormControl>
          </div>{" "}
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Email</FormControl.Label>
              <TextInput
                block
                name="email"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền email"
              />
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Ngân hàng</FormControl.Label>
              <TextInput
                block
                name="ngan_hang"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền ngân hàng"
              />
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Số tài khoản</FormControl.Label>
              <TextInput
                block
                name="so_tk"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền số tài khoản"
              />
            </FormControl>
          </div>
          <div className="col-md-6 mb-3">
            <FormControl>
              <FormControl.Label>Tên tài khoản</FormControl.Label>
              <TextInput
                block
                name="ten_tk"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền tên tài khoản"
              />
            </FormControl>
          </div>
          <div className="col-md-12 mb-3">
            <FormControl>
              <FormControl.Label>Địa chỉ trường</FormControl.Label>
              <TextInput
                block
                name="dia_chi_truong"
                register={register}
                errors={errors}
                required
                validateMessage="Vui lòng điền địa chỉ trường"
              />
            </FormControl>
          </div>
        </div>

        <ModalActions>
          <Button text="Base.Label.Close" onClick={props.onClose} />
          <Button
            text="Base.Label.Save"
            variant="primary"
            type="submit"
            isLoading={isSaving}
          />
        </ModalActions>
      </form>
    </Modal>
  );
};

export default TruongDetailModal;
